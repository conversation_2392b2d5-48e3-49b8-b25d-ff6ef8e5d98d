<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="4 4 1387.098876953125 2344.7587890625" style="max-width: 3840px; background-color: white; max-height: 3840px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#ffffff;}#my-svg .error-text{fill:#000000;stroke:#000000;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#000000;stroke:#000000;}#my-svg .marker.cross{stroke:#000000;}#my-svg svg{font-family:arial,sans-serif;font-size:14px;}#my-svg p{margin:0;}#my-svg .label{font-family:arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#000000;}#my-svg .cluster-label span{color:#000000;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#my-svg .arrowheadPath{fill:#000000;}#my-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#my-svg .flowchart-link{stroke:#000000;fill:none;}#my-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#my-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#my-svg .cluster text{fill:#000000;}#my-svg .cluster span{color:#000000;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .node .neo-node{stroke:#000000;}#my-svg [data-look="neo"].node rect,#my-svg [data-look="neo"].cluster rect,#my-svg [data-look="neo"].node polygon{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node path{stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}#my-svg [data-look="neo"].node circle{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node circle .state-start{fill:#000000;}#my-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].icon-shape .icon{fill:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .default&gt;*{fill:#282a36!important;stroke:#f8f8f2!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .default span{fill:#282a36!important;stroke:#f8f8f2!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .default tspan{fill:#f8f8f2!important;}#my-svg .cloud&gt;*{fill:#44475a!important;stroke:#bd93f9!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .cloud span{fill:#44475a!important;stroke:#bd93f9!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .cloud tspan{fill:#f8f8f2!important;}#my-svg .local&gt;*{fill:#44475a!important;stroke:#50fa7b!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .local span{fill:#44475a!important;stroke:#50fa7b!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .local tspan{fill:#f8f8f2!important;}#my-svg .external&gt;*{fill:#6272a4!important;stroke:#ffb86c!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .external span{fill:#6272a4!important;stroke:#ffb86c!important;stroke-width:2px!important;color:#f8f8f2!important;}#my-svg .external tspan{fill:#f8f8f2!important;}#my-svg .db&gt;*{fill:#f1fa8c!important;stroke:#44475a!important;stroke-width:2px!important;color:#282a36!important;}#my-svg .db span{fill:#f1fa8c!important;stroke:#44475a!important;stroke-width:2px!important;color:#282a36!important;}#my-svg .db tspan{fill:#282a36!important;}#my-svg .service&gt;*{fill:#bd93f9!important;stroke:#44475a!important;stroke-width:2px!important;color:#282a36!important;}#my-svg .service span{fill:#bd93f9!important;stroke:#44475a!important;stroke-width:2px!important;color:#282a36!important;}#my-svg .service tspan{fill:#282a36!important;}#my-svg .user&gt;*{fill:#ff79c6!important;stroke:#44475a!important;stroke-width:2px!important;color:#282a36!important;}#my-svg .user span{fill:#ff79c6!important;stroke:#44475a!important;stroke-width:2px!important;color:#282a36!important;}#my-svg .user tspan{fill:#282a36!important;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"/><g class="nodes"><g transform="translate(1002.351593017578, 1854.742736816406)" data-look="neo" data-et="cluster" data-id="Cloud_Final" id="Cloud_Final" class="cluster"><rect height="972.0325927734375" width="761.4948120117188" y="-486.01629638671875" x="-380.7474060058594" style="fill:#ffffff"/><g transform="translate(-100, -486.01629638671875)" class="cluster-label"><foreignObject height="42" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cloud Final (Google Cloud Platform)</p></span></div></foreignObject></g></g><g transform="translate(1002.351593017578, 1806.242736816406)" data-look="neo" data-et="cluster" data-id="Cloud_Services" id="Cloud_Services" class="cluster"><rect height="746.0325927734375" width="737.4948120117188" y="-373.01629638671875" x="-368.7474060058594" style="fill:#ffffff"/><g transform="translate(-100, -373.01629638671875)" class="cluster-label"><foreignObject height="42" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Cloud Backend Services (Cloud Run / VPC)</p></span></div></foreignObject></g></g><g transform="translate(513.990852355957, 637.3632202148438)" data-look="neo" data-et="cluster" data-id="Local_First" id="Local_First" class="cluster"><rect height="1250.726440429688" width="1003.981674194336" y="-625.363220214844" x="-501.990837097168" style="fill:#ffffff"/><g transform="translate(-100, -625.363220214844)" class="cluster-label"><foreignObject height="42" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Local First (Docker Compose en tu PC)</p></span></div></foreignObject></g></g><g transform="translate(571.9908676147461, 644.3631591796875)" data-look="neo" data-et="cluster" data-id="Backend_Services" id="Backend_Services" class="cluster"><rect height="841.726318359375" width="831.9816436767578" y="-420.8631591796875" x="-415.9908218383789" style="fill:#ffffff"/><g transform="translate(-100, -420.8631591796875)" class="cluster-label"><foreignObject height="42" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Backend Services (Docker Network)</p></span></div></foreignObject></g></g><g transform="translate(820.606689453125, 509.4369049072266)" data-look="neo" data-et="node" data-node="true" data-id="AuthService" id="flowchart-AuthService-0" class="node default service"><rect stroke="url(#gradient)" height="86.99996948242188" width="139.3905029296875" y="-43.49998474121094" x="-69.69525146484375" data-id="AuthService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-53.6875, -31.499984741210938)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="62.999969482421875" width="107.375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 107.391px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-id-card"></i> Auth Service<br />plap-auth-service<br />FastAPI/Docker</p></span></div></foreignObject></g></g><g transform="translate(483.0684967041016, 343.2184753417969)" data-look="neo" data-et="node" data-node="true" data-id="LocalAPIGateway" id="flowchart-LocalAPIGateway-1" class="node default service"><path transform="translate(-65.70310974121095, -62.7184753417969)" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,12.812308116752854 a65.70310974121095,12.812308116752854 0,0,0 131.4062194824219,0 a65.70310974121095,12.812308116752854 0,0,0 -131.4062194824219,0 l0,99.8123344500881 a65.70310974121095,12.812308116752854 0,0,0 131.4062194824219,0 l0,-99.8123344500881"/><g transform="translate(-53.6953125, -24)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="63" width="107.390625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 107.406px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-door-open"></i> API Gateway<br />plap-backend-api<br />FastAPI/Docker</p></span></div></foreignObject></g></g><g transform="translate(906.2942810058594, 641.4369506835938)" data-look="neo" data-et="node" data-node="true" data-id="UserService" id="flowchart-UserService-2" class="node default service"><rect stroke="url(#gradient)" height="87" width="139.3748168945312" y="-43.5" x="-69.6874084472656" data-id="UserService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-53.6796875, -31.5)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="63" width="107.359375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 107.375px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-user-cog"></i> User Service<br />plap-user-service<br />FastAPI/Docker</p></span></div></foreignObject></g></g><g transform="translate(506.7656402587891, 641.4369506835938)" data-look="neo" data-et="node" data-node="true" data-id="LibraryService" id="flowchart-LibraryService-3" class="node default service"><rect stroke="url(#gradient)" height="87" width="198.4999694824219" y="-43.5" x="-99.24998474121095" data-id="LibraryService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-83.2421875, -31.5)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="63" width="166.484375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 166.5px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-book"></i> Prompt Library<br />plap-prompt-library-service<br />FastAPI/Docker</p></span></div></foreignObject></g></g><g transform="translate(642.6614379882812, 509.4369049072266)" data-look="neo" data-et="node" data-node="true" data-id="Orchestrator" id="flowchart-Orchestrator-4" class="node default service"><rect stroke="url(#gradient)" height="86.99996948242188" width="156.5" y="-43.49998474121094" x="-78.25" data-id="Orchestrator" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-62.25, -31.499984741210938)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="62.999969482421875" width="124.5"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 124.5px; text-align: center; width: 124.5px;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-cogs"></i> Orchestrator<br />plap-orchestrator<br />ADK/Python/Docker</p></span></div></foreignObject></g></g><g transform="translate(288.7578430175781, 641.4369506835938)" data-look="neo" data-et="node" data-node="true" data-id="AgentsService" id="flowchart-AgentsService-5" class="node default service"><rect stroke="url(#gradient)" height="87" width="177.5155639648438" y="-43.5" x="-88.7577819824219" data-id="AgentsService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-72.75, -31.5)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="63" width="145.5"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 145.516px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-brain"></i> Agents Service<br />plap-agents-service<br />FastAPI/Python/Docker</p></span></div></foreignObject></g></g><g transform="translate(620.9583435058594, 781.5267333984375)" data-look="neo" data-et="node" data-node="true" data-id="ZepService" id="flowchart-ZepService-6" class="node default service"><rect stroke="url(#gradient)" height="66" width="130.2188110351562" y="-33" x="-65.1094055175781" data-id="ZepService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.109375, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="98.21875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 98.2188px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-memory"></i> Zep Memory<br />Zep/Docker</p></span></div></foreignObject></g></g><g transform="translate(586.2603759765625, 989.9662780761719)" data-look="neo" data-et="node" data-node="true" data-id="PostgresDB" id="flowchart-PostgresDB-7" class="node default db"><path transform="translate(-69.6171875, -63.2600402832031)" style="fill:#f1fa8c !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,13.173378274495889 a69.6171875,13.173378274495889 0,0,0 139.234375,0 a69.6171875,13.173378274495889 0,0,0 -139.234375,0 l0,100.17332401741442 a69.6171875,13.173378274495889 0,0,0 139.234375,0 l0,-100.17332401741442"/><g transform="translate(-57.6171875, -23.999972871459264)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="62.99994574291853" width="115.234375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 115.234px; text-align: center; width: 115.234px;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-database"></i> PostgreSQL DB<br />+pgvector<br />Docker</p></span></div></foreignObject></g></g><g transform="translate(455.6770477294922, 790.8216247558594)" data-look="neo" data-et="node" data-node="true" data-id="QdrantDB" id="flowchart-QdrantDB-8" class="node default db"><path transform="translate(-54.01564025878905, -60.8846740722656)" style="fill:#f1fa8c !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,11.589783169627848 a54.01564025878905,11.589783169627848 0,0,0 108.0312805175781,0 a54.01564025878905,11.589783169627848 0,0,0 -108.0312805175781,0 l0,98.58978180527549 a54.01564025878905,11.589783169627848 0,0,0 108.0312805175781,0 l0,-98.58978180527549"/><g transform="translate(-42.015625, -23.999999317823825)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="62.99999863564765" width="84.03125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 84.0313px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-search-plus"></i> Qdrant DB<br />Vector Store<br />Docker</p></span></div></foreignObject></g></g><g transform="translate(521.7350921630859, 102)" data-look="neo" data-et="node" data-node="true" data-id="UserInterface" id="flowchart-UserInterface-9" class="node default user"><rect stroke="url(#gradient)" height="66" width="232.0000305175781" y="-33" x="-116.00001525878905" data-id="UserInterface" style="fill:#ff79c6 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-100, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="200"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p>Usuario via Frontend React/Next.js</p></span></div></foreignObject></g></g><g transform="translate(167.9999780654907, 1184.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="GoogleCloudAPI" id="flowchart-GoogleCloudAPI-11" class="node default external"><polygon style="fill:#6272a4 !important;stroke:#ffb86c !important;stroke-width:2px !important" transform="translate(-133.99996089935306,66.5)" class="label-container" points="38,0 229.9999217987061,0 267.9999217987061,-66.5 229.9999217987061,-133 38,-133 0,-66.5"/><g transform="translate(-89.9921875, -31.5)" style="color:#f8f8f2 !important" class="label"><rect/><foreignObject height="63" width="179.984375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(248, 248, 242) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 180px; text-align: center;"><span class="nodeLabel" style="color:#f8f8f2 !important"><p><i class="fa fa-google"></i> Google Cloud API<br />(Vertex AI - Gemini LLMs &amp; Embeddings)</p></span></div></foreignObject></g></g><g transform="translate(436.6327972412109, 1150.726440429688)" data-look="neo" data-et="node" data-node="true" data-id="LocalVolumeUploads" id="flowchart-LocalVolumeUploads-12" class="node default db"><polygon style="fill:#f1fa8c !important;stroke:#44475a !important;stroke-width:2px !important" transform="translate(-61.63282775878905,33)" class="label-container" points="-33,0 123.2656555175781,0 156.2656555175781,-66 0,-66"/><g transform="translate(-52.6328125, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="105.265625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 123.266px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-folder-open"></i> local_uploads<br />Mapeado a Host</p></span></div></foreignObject></g></g><g transform="translate(1209.567749023438, 1695.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="CloudAuthService" id="flowchart-CloudAuthService-13" class="node default service"><rect stroke="url(#gradient)" height="66" width="130.249755859375" y="-33" x="-65.1248779296875" data-id="CloudAuthService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.1171875, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="98.234375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 98.2498px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-id-card"></i> Auth Service<br />Cloud Run</p></span></div></foreignObject></g></g><g transform="translate(1002.981597900391, 1546.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="CloudAPIGateway" id="flowchart-CloudAPIGateway-14" class="node default service"><polygon style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" transform="translate(-96.1484069824219,56)" class="label-container" points="32,0 160.2968139648438,0 192.2968139648438,-56 160.2968139648438,-112 32,-112 0,-56"/><g transform="translate(-58.140625, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="116.28125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 116.297px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-cloud"></i> GCP API Gateway<br />+ WAF, CDN</p></span></div></foreignObject></g></g><g transform="translate(1292.333312988281, 1806.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="CloudUserService" id="flowchart-CloudUserService-15" class="node default service"><rect stroke="url(#gradient)" height="66" width="133.5313720703125" y="-33" x="-66.76568603515625" data-id="CloudUserService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-50.765625, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="101.53125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 101.531px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-user-cog"></i> User Service<br />Cloud Run</p></span></div></foreignObject></g></g><g transform="translate(731.5807800292969, 1806.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="CloudLibraryService" id="flowchart-CloudLibraryService-16" class="node default service"><rect stroke="url(#gradient)" height="66" width="139.9530639648438" y="-33" x="-69.9765319824219" data-id="CloudLibraryService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-53.96875, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="107.9375"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 107.953px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-book"></i> Prompt Library<br />Cloud Run</p></span></div></foreignObject></g></g><g transform="translate(1018.145874023438, 1695.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="CloudOrchestrator" id="flowchart-CloudOrchestrator-17" class="node default service"><rect stroke="url(#gradient)" height="66" width="131.203125" y="-33" x="-65.6015625" data-id="CloudOrchestrator" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-49.6015625, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="99.203125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 99.2031px; text-align: center; width: 99.2031px;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-cogs"></i> Orchestrator<br />Cloud Run</p></span></div></foreignObject></g></g><g transform="translate(931.5051574707031, 1806.226440429688)" data-look="neo" data-et="node" data-node="true" data-id="CloudAgentsService" id="flowchart-CloudAgentsService-18" class="node default service"><rect stroke="url(#gradient)" height="66" width="143.2813110351562" y="-33" x="-71.6406555175781" data-id="CloudAgentsService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-55.640625, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="111.28125"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 111.281px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-brain"></i> Agents Service<br />Cloud Run</p></span></div></foreignObject></g></g><g transform="translate(1114.356872558594, 1918.188415527344)" data-look="neo" data-et="node" data-node="true" data-id="CloudZepService" id="flowchart-CloudZepService-19" class="node default service"><rect stroke="url(#gradient)" height="65.9998779296875" width="160.4219970703125" y="-32.99993896484375" x="-80.21099853515625" data-id="CloudZepService" style="fill:#bd93f9 !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container"/><g transform="translate(-64.2109375, -20.99993896484375)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="41.9998779296875" width="128.421875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 128.422px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-memory"></i> Zep Memory<br />Cloud Run/Managed</p></span></div></foreignObject></g></g><g transform="translate(1235.864624023438, 2112.685791015625)" data-look="neo" data-et="node" data-node="true" data-id="CloudSQLDB" id="flowchart-CloudSQLDB-20" class="node default db"><path transform="translate(-84.703125, -54.5732421875)" style="fill:#f1fa8c !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,14.385415560980787 a84.703125,14.385415560980787 0,0,0 169.40625,0 a84.703125,14.385415560980787 0,0,0 -169.40625,0 l0,80.37565325303842 a84.703125,14.385415560980787 0,0,0 169.40625,0 l0,-80.37565325303842"/><g transform="translate(-72.703125, -13.49511884602882)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="41.99023769205764" width="145.40625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 145.406px; text-align: center; width: 145.406px;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-database"></i> Cloud SQL DB<br />PostgreSQL + pgvector</p></span></div></foreignObject></g></g><g transform="translate(791.7239685058594, 1926.169555664062)" data-look="neo" data-et="node" data-node="true" data-id="CloudVectorDB" id="flowchart-CloudVectorDB-21" class="node default db"><path transform="translate(-110.4531555175781, -56.943115234375)" style="fill:#f1fa8c !important;stroke:#44475a !important;stroke-width:2px !important" class="basic label-container outer-path" d="M0,15.96576182536782 a110.4531555175781,15.96576182536782 0,0,0 220.9063110351562,0 a110.4531555175781,15.96576182536782 0,0,0 -220.9063110351562,0 l0,81.95470681801436 a110.4531555175781,15.96576182536782 0,0,0 220.9063110351562,0 l0,-81.95470681801436"/><g transform="translate(-98.453125, -13.494472496323269)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="41.98894499264654" width="196.90625"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 196.906px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-search-plus"></i> Vertex AI<br />Vector Search/Qdrant Managed</p></span></div></foreignObject></g></g><g transform="translate(1143.651123046875, 2272.759033203125)" data-look="neo" data-et="node" data-node="true" data-id="VertexAI_LLMs" id="flowchart-VertexAI_LLMs-23" class="node default external"><polygon style="fill:#6272a4 !important;stroke:#ffb86c !important;stroke-width:2px !important" transform="translate(-121.375,56)" class="label-container" points="32,0 210.75,0 242.75,-56 210.75,-112 32,-112 0,-56"/><g transform="translate(-83.375, -21)" style="color:#f8f8f2 !important" class="label"><rect/><foreignObject height="42" width="166.75"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(248, 248, 242) !important; display: table; white-space: break-spaces; line-height: 1.5; max-width: 166.75px; text-align: center; width: 166.75px;"><span class="nodeLabel" style="color:#f8f8f2 !important"><p><i class="fa fa-google"></i> Vertex AI<br />(Gemini LLMs &amp; Embeddings)</p></span></div></foreignObject></g></g><g transform="translate(884.6275939941406, 2249.759033203125)" data-look="neo" data-et="node" data-node="true" data-id="CloudStorage" id="flowchart-CloudStorage-24" class="node default db"><polygon style="fill:#f1fa8c !important;stroke:#44475a !important;stroke-width:2px !important" transform="translate(-64.6484069824219,33)" class="label-container" points="-33,0 129.2968139648438,0 162.2968139648438,-66 0,-66"/><g transform="translate(-55.6484375, -21)" style="color:#282a36 !important" class="label"><rect/><foreignObject height="42" width="111.296875"><div xmlns="http://www.w3.org/1999/xhtml" style="color: rgb(40, 42, 54) !important; display: table-cell; white-space: normal; line-height: 1.5; max-width: 129.297px; text-align: center;"><span class="nodeLabel" style="color:#282a36 !important"><p><i class="fa fa-cloud-upload-alt"></i> Cloud Storage<br />Archivos Adjuntos</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDgzLjA2ODQ1MjM4MDk1MjQsInkiOjEzNX0seyJ4Ijo0ODMuMDY4NDUyMzgwOTUyNCwieSI6MjE2fSx7IngiOjQ4My4wNjg0NTIzODA5NTI0LCJ5IjoyODAuNX1d" data-id="L_UserInterface_LocalAPIGateway_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 132.5 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserInterface_LocalAPIGateway_0" d="M483.0684523809524,135L483.0684523809524,216L483.0684523809524,276.5"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTExLjIyNjkzNDUyMzgwOTUsInkiOjQwNS45MzY5Mjc3OTU0MTAyfSx7IngiOjUxMS4yMjY5MzQ1MjM4MDk1LCJ5Ijo0MzUuOTM2OTI3Nzk1NDEwMn0seyJ4Ijo4MjAuNjA2NzcwODMzMzMzMywieSI6NDM1LjkzNjkyNzc5NTQxMDJ9LHsieCI6ODIwLjYwNjc3MDgzMzMzMzMsInkiOjQ2NS45MzY5Mjc3OTU0MTAyfV0=" data-id="L_LocalAPIGateway_AuthService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 351.05157470703125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LocalAPIGateway_AuthService_0" d="M511.2269345238095,405.9369277954102L511.2269345238095,428.86585998354474Q511.2269345238095,435.9369277954102 518.2980023356749,435.9369277954102L813.5357030214677,435.9369277954102Q820.6067708333333,435.9369277954102 820.6067708333333,443.0079956072757L820.6067708333333,461.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTI5Ljk5OTI1NTk1MjM4MSwieSI6NDA1LjkzNjkyNzc5NTQxMDJ9LHsieCI6NTI5Ljk5OTI1NTk1MjM4MSwieSI6NDIwLjkzNjkyNzc5NTQxMDJ9LHsieCI6OTI5LjUyMzQzNzQ5OTk5OTksInkiOjQyMC45MzY5Mjc3OTU0MTAyfSx7IngiOjkyOS41MjM0Mzc0OTk5OTk5LCJ5Ijo1OTcuOTM2OTI3Nzk1NDEwMn1d" data-id="L_LocalAPIGateway_UserService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 573.1959228515625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LocalAPIGateway_UserService_0" d="M529.999255952381,405.9369277954102L529.999255952381,413.86585998354474Q529.999255952381,420.9369277954102 537.0703237642465,420.9369277954102L922.4523696881345,420.9369277954102Q929.5234374999999,420.9369277954102 929.5234374999999,428.0079956072757L929.5234374999999,593.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDg4LjQxNDM1MDQ1MTYyOTEsInkiOjQwNS44OTQ0NzExMDIwODA3fSx7IngiOjQ5Ni42ODA2NDExNzUxODA3LCJ5Ijo1MDIuOTE4NDk0NzczMzQzMn0seyJ4Ijo1MDMuMzA5MDI3NjQxMzE5OCwieSI6NTk3LjkzNjk1MDY4MzU5Mzh9XQ==" data-id="L_LocalAPIGateway_LibraryService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 179.6231231689453 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LocalAPIGateway_LibraryService_0" d="M488.4143504516291,405.8944711020807L492.80752302980306,457.4585032020253Q496.6806411751807,502.9184947733432 499.8556547691303,548.4325713487344L503.0306683630798,593.9466479241257"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDkyLjQ1NDYxMzA5NTIzODEsInkiOjQwNS45MzY5Mjc3OTU0MTAyfSx7IngiOjQ5Mi40NTQ2MTMwOTUyMzgxLCJ5Ijo0NTAuOTM2OTI3Nzk1NDEwMn0seyJ4Ijo2NDIuNjYxNDU4MzMzMzMzMywieSI6NDUwLjkzNjkyNzc5NTQxMDJ9LHsieCI6NjQyLjY2MTQ1ODMzMzMzMzMsInkiOjQ2NS45MzY5Mjc3OTU0MTAyfV0=" data-id="L_LocalAPIGateway_Orchestrator_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 192.47048950195312 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LocalAPIGateway_Orchestrator_0" d="M492.4546130952381,405.9369277954102L492.4546130952381,443.86585998354474Q492.4546130952381,450.9369277954102 499.52568090710355,450.9369277954102L637.1614583333333,450.9369277954102Q642.6614583333333,450.9369277954102 642.6614583333333,456.4369277954102L642.6614583333333,461.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDU0LjkwOTk3MDIzODA5NTIsInkiOjQwNS45MzY5Mjc3OTU0MTAyfSx7IngiOjQ1NC45MDk5NzAyMzgwOTUyLCJ5Ijo0MzUuOTM2OTI3Nzk1NDEwMn0seyJ4IjoyNTkuMTcxODc1LCJ5Ijo0MzUuOTM2OTI3Nzk1NDEwMn0seyJ4IjoyNTkuMTcxODc1LCJ5Ijo1OTcuOTM2OTI3Nzk1NDEwMn1d" data-id="L_LocalAPIGateway_AgentsService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 369.40985107421875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LocalAPIGateway_AgentsService_0" d="M454.9099702380952,405.9369277954102L454.9099702380952,428.86585998354474Q454.9099702380952,435.9369277954102 447.8389024262297,435.9369277954102L266.2429428118655,435.9369277954102Q259.171875,435.9369277954102 259.171875,443.0079956072757L259.171875,593.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDM2LjEzNzY0ODgwOTUyMzcsInkiOjQwNS45MzY5Mjc3OTU0MTAyfSx7IngiOjQzNi4xMzc2NDg4MDk1MjM3LCJ5Ijo0MjAuOTM2OTI3Nzk1NDEwMn0seyJ4IjoxODQsInkiOjQyMC45MzY5Mjc3OTU0MTAyfSx7IngiOjE4NCwieSI6MTA3Mi43MjY0MDIyODI3MTV9LHsieCI6MTg0LCJ5IjoxMTAyLjcyNjQwMjI4MjcxNX0seyJ4Ijo0MDUuMDg4NTQxNjY2NjY2NywieSI6MTEwMi43MjY0MDIyODI3MTV9LHsieCI6NDA1LjA4ODU0MTY2NjY2NjcsInkiOjExMTcuNzI2NDAyMjgyNzE1fV0=" data-id="L_LocalAPIGateway_LocalVolumeUploads_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 1161.9510498046875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LocalAPIGateway_LocalVolumeUploads_0" d="M436.1376488095237,405.9369277954102L436.1376488095237,413.86585998354474Q436.1376488095237,420.9369277954102 429.0665809976582,420.9369277954102L191.07106781186548,420.9369277954102Q184,420.9369277954102 184,428.0079956072757L184,1072.726402282715L184,1095.6553344708495Q184,1102.726402282715 191.07106781186548,1102.726402282715L399.5885416666667,1102.726402282715Q405.0885416666667,1102.726402282715 405.0885416666667,1108.226402282715L405.0885416666667,1113.726402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTkwLjQ5NDc5MTY2NjY2NjYsInkiOjU1Mi45MzY5Mjc3OTU0MTAyfSx7IngiOjU5MC40OTQ3OTE2NjY2NjY2LCJ5Ijo1NjcuOTM2OTI3Nzk1NDEwMn0seyJ4IjozMTguMzQzNzUsInkiOjU2Ny45MzY5Mjc3OTU0MTAyfSx7IngiOjMxOC4zNDM3NSwieSI6NTk3LjkzNjkyNzc5NTQxMDJ9XQ==" data-id="L_Orchestrator_AgentsService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 298.82275390625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_AgentsService_0" d="M590.4947916666666,552.9369277954102L590.4947916666666,560.8658599835446Q590.4947916666666,567.9369277954102 583.4237238548012,567.9369277954102L325.4148178118655,567.9369277954102Q318.34375,567.9369277954102 318.34375,575.0079956072757L318.34375,593.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Njk0LjgyODEyNSwieSI6NTUyLjkzNjkyNzc5NTQxMDJ9LHsieCI6Njk0LjgyODEyNSwieSI6NTY3LjkzNjkyNzc5NTQxMDJ9LHsieCI6ODgzLjA2NTEwNDE2NjY2NjYsInkiOjU2Ny45MzY5Mjc3OTU0MTAyfSx7IngiOjg4My4wNjUxMDQxNjY2NjY2LCJ5Ijo1OTcuOTM2OTI3Nzk1NDEwMn1d" data-id="L_Orchestrator_UserService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 214.90869140625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_UserService_0" d="M694.828125,552.9369277954102L694.828125,560.8658599835446Q694.828125,567.9369277954102 701.8991928118655,567.9369277954102L875.9940363548012,567.9369277954102Q883.0651041666666,567.9369277954102 883.0651041666666,575.0079956072757L883.0651041666666,593.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjE2LjU3ODEyNSwieSI6NTUyLjkzNjkyNzc5NTQxMDJ9LHsieCI6NjE2LjU3ODEyNSwieSI6NTgyLjkzNjkyNzc5NTQxMDJ9LHsieCI6NTM5Ljg0ODk1ODMzMzMzMzMsInkiOjU4Mi45MzY5Mjc3OTU0MTAyfSx7IngiOjUzOS44NDg5NTgzMzMzMzMzLCJ5Ijo1OTcuOTM2OTI3Nzk1NDEwMn1d" data-id="L_Orchestrator_LibraryService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 103.99281311035156 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_LibraryService_0" d="M616.578125,552.9369277954102L616.578125,575.8658599835446Q616.578125,582.9369277954102 609.5070571881345,582.9369277954102L545.3489583333333,582.9369277954102Q539.8489583333333,582.9369277954102 539.8489583333333,588.4369277954102L539.8489583333333,593.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjM5LjE5MTY4NTc5NjMwMjksInkiOjU1Mi45MzY4ODk2NDg0Mzc1fSx7IngiOjYzOS4xOTE2ODU3OTYzMDI5LCJ5Ijo1NTIuOTM2ODg5NjQ4NDM3NX0seyJ4Ijo2MzIuMzkxMTI4MDEzMTQ4OCwieSI6NjUxLjczMTgxMTUyMzQzNzV9LHsieCI6NjIzLjU5MDU3MDIyOTk5NDYsInkiOjc0OC41MjY3MzMzOTg0Mzc1fV0=" data-id="L_Orchestrator_ZepService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 183.2191162109375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_ZepService_0" d="M639.1916857963029,552.9368896484375L639.1916857963029,552.9368896484375L635.5910706315073,605.2447304536569Q632.3911280131488,651.7318115234375 628.1719414189048,698.1374879394978L623.9527548246608,744.5431643555581"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjY4Ljc0NDc5MTY2NjY2NjYsInkiOjU1Mi45MzY5Mjc3OTU0MTAyfSx7IngiOjY2OC43NDQ3OTE2NjY2NjY2LCJ5Ijo1ODIuOTM2OTI3Nzk1NDEwMn0seyJ4Ijo3MDEuMDY3NzA4MzMzMzMzMywieSI6NTgyLjkzNjkyNzc5NTQxMDJ9LHsieCI6NzAxLjA2NzcwODMzMzMzMzMsInkiOjg4MS43MDYyNzU5Mzk5NDE0fSx7IngiOjU4Ni4yNjA0MTY2NjY2NjY2LCJ5Ijo4ODEuNzA2Mjc1OTM5OTQxNH0seyJ4Ijo1ODYuMjYwNDE2NjY2NjY2NiwieSI6OTI2LjcwNjI3NTkzOTk0MTR9XQ==" data-id="L_Orchestrator_PostgresDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 497.24298095703125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_Orchestrator_PostgresDB_0" d="M668.7447916666666,552.9369277954102L668.7447916666666,575.8658599835446Q668.7447916666666,582.9369277954102 675.815859478532,582.9369277954102L693.9966405214677,582.9369277954102Q701.0677083333333,582.9369277954102 701.0677083333333,590.0079956072757L701.0677083333333,874.6352081280759Q701.0677083333333,881.7062759399414 693.9966405214677,881.7062759399414L593.331484478532,881.7062759399414Q586.2604166666666,881.7062759399414 586.2604166666666,888.7773437518069L586.2604166666666,922.7062759399414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODIwLjYwNjc3MDgzMzMzMzMsInkiOjU1Mi45MzY5Mjc3OTU0MTAyfSx7IngiOjgyMC42MDY3NzA4MzMzMzMzLCJ5Ijo4OTYuNzA2Mjc1OTM5OTQxNH0seyJ4Ijo2MDkuNDY2MTQ1ODMzMzMzMywieSI6ODk2LjcwNjI3NTkzOTk0MTR9LHsieCI6NjA5LjQ2NjE0NTgzMzMzMzMsInkiOjkyNi43MDYyNzU5Mzk5NDE0fV0=" data-id="L_AuthService_PostgresDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 566.5817260742188 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AuthService_PostgresDB_0" d="M820.6067708333333,552.9369277954102L820.6067708333333,889.6352081280759Q820.6067708333333,896.7062759399414 813.5357030214677,896.7062759399414L616.5372136451988,896.7062759399414Q609.4661458333333,896.7062759399414 609.4661458333333,903.7773437518069L609.4661458333333,922.7062759399414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTA2LjI5NDI3MDgzMzMzMzMsInkiOjY4NC45MzY5Mjc3OTU0MTAyfSx7IngiOjkwNi4yOTQyNzA4MzMzMzMzLCJ5Ijo5MTEuNzA2Mjc1OTM5OTQxNH0seyJ4Ijo2MzIuNjcxODc0OTk5OTk5OSwieSI6OTExLjcwNjI3NTkzOTk0MTR9LHsieCI6NjMyLjY3MTg3NDk5OTk5OTksInkiOjkyNi43MDYyNzU5Mzk5NDE0fV0=" data-id="L_UserService_PostgresDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 497.6554260253906 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserService_PostgresDB_0" d="M906.2942708333333,684.9369277954102L906.2942708333333,904.6352081280759Q906.2942708333333,911.7062759399414 899.2232030214677,911.7062759399414L638.1718749999999,911.7062759399414Q632.6718749999999,911.7062759399414 632.6718749999999,917.2062759399414L632.6718749999999,922.7062759399414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTE2LjY4NzM5MDY4ODIxMDEsInkiOjY4NC45MzY5NTA2ODM1OTM4fSx7IngiOjUxNi42ODczOTA2ODgyMTAxLCJ5Ijo2ODQuOTM2OTUwNjgzNTkzOH0seyJ4Ijo1NDUuMjU5NTExNzAxOTgzMiwieSI6ODA2Ljk2NDYxNTk5MjExNjV9LHsieCI6NTcyLjA4NzE5MTQ0ODUxODgsInkiOjkyNi45ODIxMzA4OTcxNDY2fV0=" data-id="L_LibraryService_PostgresDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 235.30645751953125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LibraryService_PostgresDB_0" d="M516.6873906882101,684.9369506835938L516.6873906882101,684.9369506835938L531.6971304025451,749.0415193764812Q545.2595117019832,806.9646159921165 558.2370560042365,865.0215419522344L571.2146003064897,923.0784679123524"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDkxLjg4ODkyMTU1MzcwMDYsInkiOjY4NC45MzY5NTA2ODM1OTM4fSx7IngiOjQ5MS44ODg5MjE1NTM3MDA2LCJ5Ijo2ODQuOTM2OTUwNjgzNTkzOH0seyJ4Ijo0ODUuMTk0MDY3MDUxMDY4NywieSI6NzA4Ljg4NDgxMTY0OTAyMjR9LHsieCI6NDc3LjYxMDIxODMzNzg2OTEsInkiOjczMC45MzU0MTE5NDU0MTY4fV0=" data-id="L_LibraryService_QdrantDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 35.178871154785156 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_LibraryService_QdrantDB_0" d="M491.8889215537006,684.9369506835938L491.8889215537006,684.9369506835938L487.7946655720168,699.5823284087845Q485.1940670510687,708.8848116490224 482.05260558332077,718.0188429030043L478.91114411557277,727.1528741569862"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MzA2LjUwOTM3NSwieSI6Njg0LjkzNjkyNzc5NTQxMDJ9LHsieCI6MzA2LjUwOTM3NSwieSI6NzE0LjkzNjkyNzc5NTQxMDJ9LHsieCI6NDM3LjY3MTg3NSwieSI6NzE0LjkzNjkyNzc5NTQxMDJ9LHsieCI6NDM3LjY3MTg3NSwieSI6NzI5LjkzNjkyNzc5NTQxMDJ9XQ==" data-id="L_AgentsService_QdrantDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 158.4261932373047 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentsService_QdrantDB_0" d="M306.509375,684.9369277954102L306.509375,707.8658599835446Q306.509375,714.9369277954102 313.58044281186545,714.9369277954102L432.171875,714.9369277954102Q437.671875,714.9369277954102 437.671875,720.4369277954102L437.671875,725.9369277954102"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MzQyLjAxMjQ5OTk5OTk5OTksInkiOjY4NC45MzY5Mjc3OTU0MTAyfSx7IngiOjM0Mi4wMTI0OTk5OTk5OTk5LCJ5Ijo2OTkuOTM2OTI3Nzk1NDEwMn0seyJ4Ijo1OTkuMjU1MjA4MzMzMzMzMywieSI6Njk5LjkzNjkyNzc5NTQxMDJ9LHsieCI6NTk5LjI1NTIwODMzMzMzMzMsInkiOjc0OC41MjY3MTA1MTAyNTM5fV0=" data-id="L_AgentsService_ZepService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 302.50421142578125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentsService_ZepService_0" d="M342.0124999999999,684.9369277954102L342.0124999999999,692.8658599835446Q342.0124999999999,699.9369277954102 349.08356781186535,699.9369277954102L592.1841405214677,699.9369277954102Q599.2552083333333,699.9369277954102 599.2552083333333,707.0079956072757L599.2552083333333,744.5267105102539"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MjM1LjUwMzEyNSwieSI6Njg0LjkzNjkyNzc5NTQxMDJ9LHsieCI6MjM1LjUwMzEyNSwieSI6Njk5LjkzNjkyNzc5NTQxMDJ9LHsieCI6MTY4LCJ5Ijo2OTkuOTM2OTI3Nzk1NDEwMn0seyJ4IjoxNjgsInkiOjEwNzIuNzI2NDAyMjgyNzE1fSx7IngiOjE2OCwieSI6MTExNy43MjY0MDIyODI3MTV9XQ==" data-id="L_AgentsService_GoogleCloudAPI_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 481.9643249511719 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentsService_GoogleCloudAPI_0" d="M235.503125,684.9369277954102L235.503125,692.8658599835446Q235.503125,699.9369277954102 228.43205718813454,699.9369277954102L175.07106781186548,699.9369277954102Q168,699.9369277954102 168,707.0079956072757L168,1072.726402282715L168,1113.726402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MjcxLjAwNjI1LCJ5Ijo2ODQuOTM2OTI3Nzk1NDEwMn0seyJ4IjoyNzEuMDA2MjUsInkiOjEwNzIuNzI2NDAyMjgyNzE1fSx7IngiOjI3MS4wMDYyNSwieSI6MTA4Ny43MjY0MDIyODI3MTV9LHsieCI6NDY4LjE3NzA4MzMzMzMzMzQsInkiOjEwODcuNzI2NDAyMjgyNzE1fSx7IngiOjQ2OC4xNzcwODMzMzMzMzM0LCJ5IjoxMTE3LjcyNjQwMjI4MjcxNX1d" data-id="L_AgentsService_LocalVolumeUploads_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 611.6321411132812 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_AgentsService_LocalVolumeUploads_0" d="M271.00625,684.9369277954102L271.00625,1072.726402282715L271.00625,1080.6553344708495Q271.00625,1087.726402282715 278.0773178118655,1087.726402282715L461.10601552146795,1087.726402282715Q468.1770833333334,1087.726402282715 468.1770833333334,1094.7974700945806L468.1770833333334,1113.726402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjIwLjk1ODMzMzMzMzMzMzMsInkiOjgxNC41MjY3MTA1MTAyNTM5fSx7IngiOjYyMC45NTgzMzMzMzMzMzMzLCJ5Ijo4NjYuNzA2Mjc1OTM5OTQxNH0seyJ4Ijo1NjMuMDU0Njg3NDk5OTk5OSwieSI6ODY2LjcwNjI3NTkzOTk0MTR9LHsieCI6NTYzLjA1NDY4NzQ5OTk5OTksInkiOjkyNi43MDYyNzU5Mzk5NDE0fV0=" data-id="L_ZepService_PostgresDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 151.7549285888672 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_ZepService_PostgresDB_0" d="M620.9583333333333,814.5267105102539L620.9583333333333,859.6352081280759Q620.9583333333333,866.7062759399414 613.8872655214677,866.7062759399414L570.1257553118653,866.7062759399414Q563.0546874999999,866.7062759399414 563.0546874999999,873.7773437518069L563.0546874999999,922.7062759399414"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTYwLjQwMTc4NTcxNDI4NTgsInkiOjEzNX0seyJ4Ijo1NjAuNDAxNzg1NzE0Mjg1OCwieSI6MTUwfSx7IngiOjEwMDIuOTgxNzcwODMzMzMzLCJ5IjoxNTB9LHsieCI6MTAwMi45ODE3NzA4MzMzMzMsInkiOjEyNzAuMjI2NDAyMjgyNzE1fSx7IngiOjEwMDIuOTgxNzcwODMzMzMzLCJ5IjoxMzYxLjIyNjQwMjI4MjcxNX0seyJ4IjoxMDAyLjk4MTc3MDgzMzMzMywieSI6MTQyNS43MjY0MDIyODI3MTV9LHsieCI6MTAwMi45ODE3NzA4MzMzMzMsInkiOjE0OTAuMjI2NDAyMjgyNzE1fV0=" data-id="L_UserInterface_CloudAPIGateway_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 1779.4781494140625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_UserInterface_CloudAPIGateway_0" d="M560.4017857142858,135L560.4017857142858,142.92893218813452Q560.4017857142858,150 567.4728535261513,150L995.9107030214675,150Q1002.981770833333,150 1002.981770833333,157.07106781186548L1002.981770833333,1270.226402282715L1002.981770833333,1361.226402282715L1002.981770833333,1425.726402282715L1002.981770833333,1486.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTA0OC40NzM5NTgzMzMzMzMsInkiOjE2MDIuMjI2NDAyMjgyNzE1fSx7IngiOjEwNDguNDczOTU4MzMzMzMzLCJ5IjoxNjMyLjIyNjQwMjI4MjcxNX0seyJ4IjoxMjA5LjU2NzcwODMzMzMzMywieSI6MTYzMi4yMjY0MDIyODI3MTV9LHsieCI6MTIwOS41Njc3MDgzMzMzMzMsInkiOjE2NjIuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudAPIGateway_CloudAuthService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 202.7657470703125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAPIGateway_CloudAuthService_0" d="M1048.473958333333,1602.226402282715L1048.473958333333,1625.1553344708495Q1048.473958333333,1632.226402282715 1055.5450261451986,1632.226402282715L1202.4966405214675,1632.226402282715Q1209.567708333333,1632.226402282715 1209.567708333333,1639.2974700945806L1209.567708333333,1658.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTA3OC44MDIwODMzMzMzMzMsInkiOjE2MDIuMjI2NDAyMjgyNzE1fSx7IngiOjEwNzguODAyMDgzMzMzMzMzLCJ5IjoxNjE3LjIyNjQwMjI4MjcxNX0seyJ4IjoxMzE0LjU4ODU0MTY2NjY2NywieSI6MTYxNy4yMjY0MDIyODI3MTV9LHsieCI6MTMxNC41ODg1NDE2NjY2NjcsInkiOjE3NzMuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudAPIGateway_CloudUserService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 388.45849609375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAPIGateway_CloudUserService_0" d="M1078.802083333333,1602.226402282715L1078.802083333333,1610.1553344708495Q1078.802083333333,1617.226402282715 1085.8731511451986,1617.226402282715L1307.5174738548014,1617.226402282715Q1314.588541666667,1617.226402282715 1314.588541666667,1624.2974700945806L1314.588541666667,1769.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTU3LjQ4OTU4MzMzMzMzMzMsInkiOjE2MDIuMjI2NDAyMjgyNzE1fSx7IngiOjk1Ny40ODk1ODMzMzMzMzMzLCJ5IjoxNjMyLjIyNjQwMjI4MjcxNX0seyJ4Ijo3MDguMjU1MjA4MzMzMzMzMywieSI6MTYzMi4yMjY0MDIyODI3MTV9LHsieCI6NzA4LjI1NTIwODMzMzMzMzMsInkiOjE3NzMuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudAPIGateway_CloudLibraryService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 401.9063720703125 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAPIGateway_CloudLibraryService_0" d="M957.4895833333333,1602.226402282715L957.4895833333333,1625.1553344708495Q957.4895833333333,1632.226402282715 950.4185155214677,1632.226402282715L715.3262761451988,1632.226402282715Q708.2552083333333,1632.226402282715 708.2552083333333,1639.2974700945806L708.2552083333333,1769.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTAwOS42MjYwNjE4NjkyNTIsInkiOjE2MDIuNzI2NDQwNDI5Njg4fSx7IngiOjEwMDkuMTgwOTIzMTU0NjksInkiOjE2MDIuNzI2NDQwNDI5Njg4fSx7IngiOjEwMTIuOTg0MTMzMTEyMzUxLCJ5IjoxNjMzLjQ3NjQ0MDQyOTY4OH0seyJ4IjoxMDE0Ljc4NzM0MzA3MDAxMSwieSI6MTY2Mi4yMjY0NDA0Mjk2ODh9XQ==" data-id="L_CloudAPIGateway_CloudOrchestrator_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 47.13233184814453 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAPIGateway_CloudOrchestrator_0" d="M1009.626061869252,1602.726440429688L1009.4034925119711,1602.726440429688Q1009.18092315469,1602.726440429688 1009.2082427316905,1602.9473267341818L1011.4616798043195,1621.1669861152698Q1012.984133112351,1633.476440429688 1013.7605434048222,1645.8553627031183L1014.5369536972936,1658.2342849765487"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTg3LjgxNzcwODMzMzMzMzMsInkiOjE2MDIuMjI2NDAyMjgyNzE1fSx7IngiOjk4Ny44MTc3MDgzMzMzMzMzLCJ5IjoxNjQ3LjIyNjQwMjI4MjcxNX0seyJ4Ijo5MDcuNjI0OTk5OTk5OTk5OSwieSI6MTY0Ny4yMjY0MDIyODI3MTV9LHsieCI6OTA3LjYyNDk5OTk5OTk5OTksInkiOjE3NzMuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudAPIGateway_CloudAgentsService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 232.8647003173828 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAPIGateway_CloudAgentsService_0" d="M987.8177083333333,1602.226402282715L987.8177083333333,1640.1553344708495Q987.8177083333333,1647.226402282715 980.7466405214677,1647.226402282715L914.6960678118653,1647.226402282715Q907.6249999999999,1647.226402282715 907.6249999999999,1654.2974700945806L907.6249999999999,1769.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTI3LjE2MTQ1ODMzMzMzMzMsInkiOjE2MDIuMjI2NDAyMjgyNzE1fSx7IngiOjkyNy4xNjE0NTgzMzMzMzMzLCJ5IjoxNjE3LjIyNjQwMjI4MjcxNX0seyJ4Ijo2NDUuNjA0MTY2NjY2NjY2NSwieSI6MTYxNy4yMjY0MDIyODI3MTV9LHsieCI6NjQ1LjYwNDE2NjY2NjY2NjUsInkiOjIxODYuNzU4OTc5Nzk3MzYzfSx7IngiOjY0NS42MDQxNjY2NjY2NjY1LCJ5IjoyMjAxLjc1ODk3OTc5NzM2M30seyJ4Ijo4NTIuMDc4MTI0OTk5OTk5OSwieSI6MjIwMS43NTg5Nzk3OTczNjN9LHsieCI6ODUyLjA3ODEyNDk5OTk5OTksInkiOjIyMTYuNzU4OTc5Nzk3MzYzfV0=" data-id="L_CloudAPIGateway_CloudStorage_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 1079.49951171875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAPIGateway_CloudStorage_0" d="M927.1614583333333,1602.226402282715L927.1614583333333,1610.1553344708495Q927.1614583333333,1617.226402282715 920.0903905214677,1617.226402282715L652.675234478532,1617.226402282715Q645.6041666666665,1617.226402282715 645.6041666666665,1624.2974700945806L645.6041666666665,2186.758979797363L645.6041666666665,2194.6879119854975Q645.6041666666665,2201.758979797363 652.675234478532,2201.758979797363L846.5781249999999,2201.758979797363Q852.0781249999999,2201.758979797363 852.0781249999999,2207.258979797363L852.0781249999999,2212.758979797363"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTk2LjI3ODY0NTgzMzMzMzMsInkiOjE3MjguMjI2NDAyMjgyNzE1fSx7IngiOjk5Ni4yNzg2NDU4MzMzMzMzLCJ5IjoxNzU4LjIyNjQwMjI4MjcxNX0seyJ4Ijo5NTUuMzg1NDE2NjY2NjY2NSwieSI6MTc1OC4yMjY0MDIyODI3MTV9LHsieCI6OTU1LjM4NTQxNjY2NjY2NjUsInkiOjE3NzMuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudOrchestrator_CloudAgentsService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 68.15718841552734 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudOrchestrator_CloudAgentsService_0" d="M996.2786458333333,1728.226402282715L996.2786458333333,1751.1553344708495Q996.2786458333333,1758.226402282715 989.2075780214677,1758.226402282715L960.8854166666665,1758.226402282715Q955.3854166666665,1758.226402282715 955.3854166666665,1763.726402282715L955.3854166666665,1769.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTA2MS44ODAyMDgzMzMzMzMsInkiOjE3MjguMjI2NDAyMjgyNzE1fSx7IngiOjEwNjEuODgwMjA4MzMzMzMzLCJ5IjoxNzQzLjIyNjQwMjI4MjcxNX0seyJ4IjoxMjcwLjA3ODEyNSwieSI6MTc0My4yMjY0MDIyODI3MTV9LHsieCI6MTI3MC4wNzgxMjUsInkiOjE3NzMuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudOrchestrator_CloudUserService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 234.8699493408203 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudOrchestrator_CloudUserService_0" d="M1061.880208333333,1728.226402282715L1061.880208333333,1736.1553344708495Q1061.880208333333,1743.226402282715 1068.9512761451986,1743.226402282715L1263.0070571881345,1743.226402282715Q1270.078125,1743.226402282715 1270.078125,1750.2974700945806L1270.078125,1769.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTc0LjQxMTQ1ODMzMzMzMzMsInkiOjE3MjguMjI2NDAyMjgyNzE1fSx7IngiOjk3NC40MTE0NTgzMzMzMzMzLCJ5IjoxNzQzLjIyNjQwMjI4MjcxNX0seyJ4Ijo3NTQuOTA2MjQ5OTk5OTk5OSwieSI6MTc0My4yMjY0MDIyODI3MTV9LHsieCI6NzU0LjkwNjI0OTk5OTk5OTksInkiOjE3NzMuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudOrchestrator_CloudLibraryService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 246.17721557617188 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudOrchestrator_CloudLibraryService_0" d="M974.4114583333333,1728.226402282715L974.4114583333333,1736.1553344708495Q974.4114583333333,1743.226402282715 967.3403905214677,1743.226402282715L761.9773178118653,1743.226402282715Q754.9062499999999,1743.226402282715 754.9062499999999,1750.2974700945806L754.9062499999999,1769.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTA0MC4wMTMwMjA4MzMzMzMsInkiOjE3MjguMjI2NDAyMjgyNzE1fSx7IngiOjEwNDAuMDEzMDIwODMzMzMzLCJ5IjoxNzU4LjIyNjQwMjI4MjcxNX0seyJ4IjoxMTQxLjA5Mzc1LCJ5IjoxNzU4LjIyNjQwMjI4MjcxNX0seyJ4IjoxMTQxLjA5Mzc1LCJ5IjoxODg1LjE4ODQ4OTI3ODE1N31d" data-id="L_CloudOrchestrator_CloudZepService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 239.71478271484375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudOrchestrator_CloudZepService_0" d="M1040.013020833333,1728.226402282715L1040.013020833333,1751.1553344708495Q1040.013020833333,1758.226402282715 1047.0840886451986,1758.226402282715L1134.0226821881345,1758.226402282715Q1141.09375,1758.226402282715 1141.09375,1765.2974700945806L1141.09375,1881.188489278157"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTAxOC4xNDU4MzMzMzMzMzMsInkiOjE3MjguMjI2NDAyMjgyNzE1fSx7IngiOjEwMTguMTQ1ODMzMzMzMzMzLCJ5IjoyMDI4LjExMjY2MzI2OTA0M30seyJ4IjoxMjA3LjYzMDIwODMzMzMzMywieSI6MjAyOC4xMTI2NjMyNjkwNDN9LHsieCI6MTIwNy42MzAyMDgzMzMzMzMsInkiOjIwNTguMTEyNjYzMjY5MDQzfV0=" data-id="L_CloudOrchestrator_CloudSQLDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 501.0425720214844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudOrchestrator_CloudSQLDB_0" d="M1018.145833333333,1728.226402282715L1018.145833333333,2021.0415954571774Q1018.145833333333,2028.112663269043 1025.2169011451986,2028.112663269043L1200.5591405214675,2028.112663269043Q1207.630208333333,2028.112663269043 1207.630208333333,2035.1837310809085L1207.630208333333,2054.112663269043"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTIwOS41Njc3MDgzMzMzMzMsInkiOjE3MjguMjI2NDAyMjgyNzE1fSx7IngiOjEyMDkuNTY3NzA4MzMzMzMzLCJ5IjoxOTk4LjExMjY2MzI2OTA0M30seyJ4IjoxMjY0LjA5ODk1ODMzMzMzMywieSI6MTk5OC4xMTI2NjMyNjkwNDN9LHsieCI6MTI2NC4wOTg5NTgzMzMzMzMsInkiOjIwNTguMTEyNjYzMjY5MDQzfV0=" data-id="L_CloudAuthService_CloudSQLDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 366.0895080566406 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAuthService_CloudSQLDB_0" d="M1209.567708333333,1728.226402282715L1209.567708333333,1991.0415954571774Q1209.567708333333,1998.112663269043 1216.6387761451986,1998.112663269043L1257.0278905214675,1998.112663269043Q1264.098958333333,1998.112663269043 1264.098958333333,2005.1837310809085L1264.098958333333,2054.112663269043"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTI4Ni4yNTI2ODAzMjkyNDUsInkiOjE4MzkuMjI2NDQwNDI5Njg4fSx7IngiOjEyODYuMjUyNjgwMzI5MjQ1LCJ5IjoxODM5LjIyNjQ0MDQyOTY4OH0seyJ4IjoxMjY3LjA4NjUyODUyMTExMSwieSI6MTk0OS43MjAzNjEwMzk4OTN9LHsieCI6MTI0Ni4zMjAwOTY0MjYxNDQsInkiOjIwNTguMjIyNTYyMDAyNzczfV0=" data-id="L_CloudUserService_CloudSQLDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 209.61288452148438 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudUserService_CloudSQLDB_0" d="M1286.252680329245,1839.226440429688L1286.252680329245,1839.226440429688L1276.184888708285,1897.2678135348697Q1267.086528521111,1949.720361039893 1257.0792721569096,2002.0071157481693L1247.0720157927085,2054.2938704564453"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NzA4LjI1NTIwODMzMzMzMzMsInkiOjE4MzkuMjI2NDAyMjgyNzE1fSx7IngiOjcwOC4yNTUyMDgzMzMzMzMzLCJ5IjoxODU0LjIyNjQwMjI4MjcxNX0seyJ4Ijo2NjUuMjcwODMzMzMzMzMzMywieSI6MTg1NC4yMjY0MDIyODI3MTV9LHsieCI6NjY1LjI3MDgzMzMzMzMzMzMsInkiOjIwNDMuMTEyNjYzMjY5MDQzfSx7IngiOjExNzkuMzk1ODMzMzMzMzMzLCJ5IjoyMDQzLjExMjY2MzI2OTA0M30seyJ4IjoxMTc5LjM5NTgzMzMzMzMzMywieSI6MjA1OC4xMTI2NjMyNjkwNDN9XQ==" data-id="L_CloudLibraryService_CloudSQLDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 752.9315795898438 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudLibraryService_CloudSQLDB_0" d="M708.2552083333333,1839.226402282715L708.2552083333333,1847.1553344708495Q708.2552083333333,1854.226402282715 701.1841405214677,1854.226402282715L672.3419011451988,1854.226402282715Q665.2708333333333,1854.226402282715 665.2708333333333,1861.2974700945806L665.2708333333333,2036.0415954571774Q665.2708333333333,2043.112663269043 672.3419011451988,2043.112663269043L1173.895833333333,2043.112663269043Q1179.395833333333,2043.112663269043 1179.395833333333,2048.612663269043L1179.395833333333,2054.112663269043"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NzQ4LjEyODAwMDkwMDE5MzEsInkiOjE4MzkuMjI2NDQwNDI5Njg4fSx7IngiOjc0OC4xMjgwMDA5MDAxOTMxLCJ5IjoxODM5LjIyNjQ0MDQyOTY4OH0seyJ4Ijo3NTYuNjQ5NDY0OTMyNDE4NiwieSI6MTg1NS40OTc3ODU4OTU4NDd9LHsieCI6NzYzLjQ2MzAxNjQyNjY3MTQsInkiOjE4NjkuNzU3ODk0ODMxNjMzfV0=" data-id="L_CloudLibraryService_CloudVectorDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 21.170625686645508 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudLibraryService_CloudVectorDB_0" d="M748.1280009001931,1839.226440429688L748.1280009001931,1839.226440429688L753.9112401583342,1850.2692710426916Q756.6494649324186,1855.497785895847 759.1939992457144,1860.8232518483755L761.7385335590101,1866.1487178009038"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODg4LjUyMDgzMzMzMzMzMzMsInkiOjE4MzkuMjI2NDAyMjgyNzE1fSx7IngiOjg4OC41MjA4MzMzMzMzMzMzLCJ5IjoxODU0LjIyNjQwMjI4MjcxNX0seyJ4Ijo4MjguNTQxNjY2NjY2NjY2NSwieSI6MTg1NC4yMjY0MDIyODI3MTV9LHsieCI6ODI4LjU0MTY2NjY2NjY2NjUsInkiOjE4NjkuMjI2NDAyMjgyNzE1fV0=" data-id="L_CloudAgentsService_CloudVectorDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 72.24313354492188 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAgentsService_CloudVectorDB_0" d="M888.5208333333333,1839.226402282715L888.5208333333333,1847.1553344708495Q888.5208333333333,1854.226402282715 881.4497655214677,1854.226402282715L834.0416666666665,1854.226402282715Q828.5416666666665,1854.226402282715 828.5416666666665,1859.726402282715L828.5416666666665,1865.226402282715"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTc0LjQ4OTU4MzMzMzMzMzMsInkiOjE4MzkuMjI2NDAyMjgyNzE1fSx7IngiOjk3NC40ODk1ODMzMzMzMzMzLCJ5IjoxODU0LjIyNjQwMjI4MjcxNX0seyJ4IjoxMDg3LjYxOTc5MTY2NjY2NywieSI6MTg1NC4yMjY0MDIyODI3MTV9LHsieCI6MTA4Ny42MTk3OTE2NjY2NjcsInkiOjE4ODUuMTg4NDg5Mjc4MTU3fV0=" data-id="L_CloudAgentsService_CloudZepService_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 140.76429748535156 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAgentsService_CloudZepService_0" d="M974.4895833333333,1839.226402282715L974.4895833333333,1847.1553344708495Q974.4895833333333,1854.226402282715 981.5606511451988,1854.226402282715L1080.5487238548014,1854.226402282715Q1087.619791666667,1854.226402282715 1087.619791666667,1861.2974700945806L1087.619791666667,1881.188489278157"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTQ1LjgzMzMzMzMzMzMzMzMsInkiOjE4MzkuMjI2NDAyMjgyNzE1fSx7IngiOjk0NS44MzMzMzMzMzMzMzMzLCJ5IjoyMTg2Ljc1ODk3OTc5NzM2M30seyJ4Ijo5NDUuODMzMzMzMzMzMzMzMywieSI6MjIwMS43NTg5Nzk3OTczNjN9LHsieCI6MTE0My42NTEwNDE2NjY2NjcsInkiOjIyMDEuNzU4OTc5Nzk3MzYzfSx7IngiOjExNDMuNjUxMDQxNjY2NjY3LCJ5IjoyMjE2Ljc1ODk3OTc5NzM2M31d" data-id="L_CloudAgentsService_VertexAI_LLMs_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 557.6141357421875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAgentsService_VertexAI_LLMs_0" d="M945.8333333333333,1839.226402282715L945.8333333333333,2186.758979797363L945.8333333333333,2194.6879119854975Q945.8333333333333,2201.758979797363 952.9044011451988,2201.758979797363L1138.151041666667,2201.758979797363Q1143.651041666667,2201.758979797363 1143.651041666667,2207.258979797363L1143.651041666667,2212.758979797363"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTE3LjE3NzA4MzMzMzMzMzMsInkiOjE4MzkuMjI2NDAyMjgyNzE1fSx7IngiOjkxNy4xNzcwODMzMzMzMzMzLCJ5IjoyMTg2Ljc1ODk3OTc5NzM2M30seyJ4Ijo5MTcuMTc3MDgzMzMzMzMzMywieSI6MjIxNi43NTg5Nzk3OTczNjN9XQ==" data-id="L_CloudAgentsService_CloudStorage_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 364.5325927734375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudAgentsService_CloudStorage_0" d="M917.1770833333333,1839.226402282715L917.1770833333333,2186.758979797363L917.1770833333333,2212.758979797363"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTExNC4zNTY3NzA4MzMzMzMsInkiOjE5NTEuMTg4NDg5Mjc4MTU3fSx7IngiOjExMTQuMzU2NzcwODMzMzMzLCJ5IjoyMDEzLjExMjY2MzI2OTA0M30seyJ4IjoxMjM1Ljg2NDU4MzMzMzMzMywieSI6MjAxMy4xMTI2NjMyNjkwNDN9LHsieCI6MTIzNS44NjQ1ODMzMzMzMzMsInkiOjIwNTguMTEyNjYzMjY5MDQzfV0=" data-id="L_CloudZepService_CloudSQLDB_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 210.10401916503906 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_CloudZepService_CloudSQLDB_0" d="M1114.356770833333,1951.188489278157L1114.356770833333,2006.0415954571774Q1114.356770833333,2013.112663269043 1121.4278386451986,2013.112663269043L1228.7935155214675,2013.112663269043Q1235.864583333333,2013.112663269043 1235.864583333333,2020.1837310809085L1235.864583333333,2054.112663269043"/></g><g class="edgeLabels"><g transform="translate(483.06845, 207.75)" class="edgeLabel"><g transform="translate(-43.171875, -10.5)" data-id="L_UserInterface_LocalAPIGateway_0" class="label"><foreignObject height="21" width="86.34375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTP/HTTPS</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LocalAPIGateway_AuthService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LocalAPIGateway_UserService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LocalAPIGateway_LibraryService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LocalAPIGateway_Orchestrator_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LocalAPIGateway_AgentsService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LocalAPIGateway_LocalVolumeUploads_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Orchestrator_AgentsService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Orchestrator_UserService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Orchestrator_LibraryService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Orchestrator_ZepService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_Orchestrator_PostgresDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_AuthService_PostgresDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_UserService_PostgresDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LibraryService_PostgresDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_LibraryService_QdrantDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_AgentsService_QdrantDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_AgentsService_ZepService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_AgentsService_GoogleCloudAPI_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_AgentsService_LocalVolumeUploads_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_ZepService_PostgresDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(1002.98177, 591.32321)" class="edgeLabel"><g transform="translate(-22.9453125, -10.5)" data-id="L_UserInterface_CloudAPIGateway_0" class="label"><foreignObject height="21" width="45.890625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>HTTPS</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAPIGateway_CloudAuthService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAPIGateway_CloudUserService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAPIGateway_CloudLibraryService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAPIGateway_CloudOrchestrator_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAPIGateway_CloudAgentsService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAPIGateway_CloudStorage_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudOrchestrator_CloudAgentsService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudOrchestrator_CloudUserService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudOrchestrator_CloudLibraryService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudOrchestrator_CloudZepService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudOrchestrator_CloudSQLDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAuthService_CloudSQLDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudUserService_CloudSQLDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudLibraryService_CloudSQLDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudLibraryService_CloudVectorDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAgentsService_CloudVectorDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAgentsService_CloudZepService_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAgentsService_VertexAI_LLMs_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudAgentsService_CloudStorage_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_CloudZepService_CloudSQLDB_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g></g></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="my-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient></svg>