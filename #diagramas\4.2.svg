<svg aria-roledescription="flowchart-v2" role="graphics-document document" viewBox="3.9999847412109375 4.000001907348633 1316 1254.6234130859375" style="max-width: 3840px; background-color: white; max-height: 3840px;" class="flowchart" xmlns:xlink="http://www.w3.org/1999/xlink" xmlns="http://www.w3.org/2000/svg" width="100%" id="my-svg"><style>#my-svg{font-family:arial,sans-serif;font-size:14px;fill:#333;}@keyframes edge-animation-frame{from{stroke-dashoffset:0;}}@keyframes dash{to{stroke-dashoffset:0;}}#my-svg .edge-animation-slow{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 50s linear infinite;stroke-linecap:round;}#my-svg .edge-animation-fast{stroke-dasharray:9,5!important;stroke-dashoffset:900;animation:dash 20s linear infinite;stroke-linecap:round;}#my-svg .error-icon{fill:#ffffff;}#my-svg .error-text{fill:#000000;stroke:#000000;}#my-svg .edge-thickness-normal{stroke-width:2px;}#my-svg .edge-thickness-thick{stroke-width:3.5px;}#my-svg .edge-pattern-solid{stroke-dasharray:0;}#my-svg .edge-thickness-invisible{stroke-width:0;fill:none;}#my-svg .edge-pattern-dashed{stroke-dasharray:3;}#my-svg .edge-pattern-dotted{stroke-dasharray:2;}#my-svg .marker{fill:#000000;stroke:#000000;}#my-svg .marker.cross{stroke:#000000;}#my-svg svg{font-family:arial,sans-serif;font-size:14px;}#my-svg p{margin:0;}#my-svg .label{font-family:arial,sans-serif;color:#333;}#my-svg .cluster-label text{fill:#000000;}#my-svg .cluster-label span{color:#000000;}#my-svg .cluster-label span p{background-color:transparent;}#my-svg .label text,#my-svg span{fill:#333;color:#333;}#my-svg .node rect,#my-svg .node circle,#my-svg .node ellipse,#my-svg .node polygon,#my-svg .node path{fill:#ffffff;stroke:#000000;stroke-width:2px;}#my-svg .rough-node .label text,#my-svg .node .label text,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-anchor:middle;}#my-svg .node .katex path{fill:#000;stroke:#000;stroke-width:1px;}#my-svg .rough-node .label,#my-svg .node .label,#my-svg .image-shape .label,#my-svg .icon-shape .label{text-align:center;}#my-svg .node.clickable{cursor:pointer;}#my-svg .root .anchor path{fill:#000000!important;stroke-width:0;stroke:#000000;}#my-svg .arrowheadPath{fill:#000000;}#my-svg .edgePath .path{stroke:#000000;stroke-width:2px;}#my-svg .flowchart-link{stroke:#000000;fill:none;}#my-svg .edgeLabel{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .edgeLabel p{background-color:hsl(-120, 0%, 80%);}#my-svg .edgeLabel rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .labelBkg{background-color:rgba(204, 204, 204, 0.5);}#my-svg .cluster rect{fill:#ffffff;stroke:hsl(0, 0%, 90%);stroke-width:2px;}#my-svg .cluster text{fill:#000000;}#my-svg .cluster span{color:#000000;}#my-svg div.mermaidTooltip{position:absolute;text-align:center;max-width:200px;padding:2px;font-family:arial,sans-serif;font-size:12px;background:#ffffff;border:1px solid hsl(0, 0%, 90%);border-radius:2px;pointer-events:none;z-index:100;}#my-svg .flowchartTitleText{text-anchor:middle;font-size:18px;fill:#333;}#my-svg rect.text{fill:none;stroke-width:0;}#my-svg .icon-shape,#my-svg .image-shape{background-color:hsl(-120, 0%, 80%);text-align:center;}#my-svg .icon-shape p,#my-svg .image-shape p{background-color:hsl(-120, 0%, 80%);padding:2px;}#my-svg .icon-shape rect,#my-svg .image-shape rect{opacity:0.5;background-color:hsl(-120, 0%, 80%);fill:hsl(-120, 0%, 80%);}#my-svg .node .neo-node{stroke:#000000;}#my-svg [data-look="neo"].node rect,#my-svg [data-look="neo"].cluster rect,#my-svg [data-look="neo"].node polygon{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node path{stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].node .outer-path{filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node .neo-line path{stroke:#000000;filter:none;}#my-svg [data-look="neo"].node circle{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].node circle .state-start{fill:#000000;}#my-svg [data-look="neo"].statediagram-cluster rect{fill:#ffffff;stroke:url(#my-svg-gradient);stroke-width:2;}#my-svg [data-look="neo"].icon-shape .icon{fill:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg [data-look="neo"].icon-shape .icon-neo path{stroke:url(#my-svg-gradient);filter:drop-shadow( 0px 1px 2px rgba(0, 0, 0, 0.25));}#my-svg :root{--mermaid-font-family:"trebuchet ms",verdana,arial,sans-serif;}#my-svg .user&gt;*{fill:#ff79c6!important;stroke:#44475a!important;}#my-svg .user span{fill:#ff79c6!important;stroke:#44475a!important;}#my-svg .service&gt;*{fill:#bd93f9!important;stroke:#44475a!important;}#my-svg .service span{fill:#bd93f9!important;stroke:#44475a!important;}#my-svg .db&gt;*{fill:#f1fa8c!important;stroke:#44475a!important;}#my-svg .db span{fill:#f1fa8c!important;stroke:#44475a!important;}#my-svg .cloud&gt;*{fill:#ffb86c!important;stroke:#44475a!important;}#my-svg .cloud span{fill:#ffb86c!important;stroke:#44475a!important;}#my-svg .decision&gt;*{fill:#50fa7b!important;stroke:#44475a!important;}#my-svg .decision span{fill:#50fa7b!important;stroke:#44475a!important;}</style><g><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="7.75" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="4" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="10.5" markerUnits="userSpaceOnUse" refY="7" refX="11.5" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointEnd-margin"><path style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 0 0 L 11.5 7 L 0 14 z"/></marker><marker orient="auto" markerHeight="14" markerWidth="11.5" markerUnits="userSpaceOnUse" refY="7" refX="1" viewBox="0 0 11.5 14" class="marker flowchart-v2" id="my-svg_flowchart-v2-pointStart-margin"><polygon style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" points="0,7 11.5,14 11.5,0"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="10.75" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="0" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refX="12.25" refY="5" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleEnd-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="14" markerWidth="14" markerUnits="userSpaceOnUse" refY="5" refX="-2" viewBox="0 0 10 10" class="marker flowchart-v2" id="my-svg_flowchart-v2-circleStart-margin"><circle style="stroke-width: 0; stroke-dasharray: 1, 0;" class="arrowMarkerPath" r="5" cy="5" cx="5"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="17.7" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossEnd-margin"><path style="stroke-width: 2.5;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><marker orient="auto" markerHeight="12" markerWidth="12" markerUnits="userSpaceOnUse" refY="7.5" refX="-3.5" viewBox="0 0 15 15" class="marker cross flowchart-v2" id="my-svg_flowchart-v2-crossStart-margin"><path style="stroke-width: 2.5; stroke-dasharray: 1, 0;" class="arrowMarkerPath" d="M 1,1 L 14,14 M 1,14 L 14,1"/></marker><g class="root"><g class="clusters"/><g class="nodes"><g transform="translate(826.7578125, 1083.623382568359)" data-look="neo" data-et="cluster" data-id="Cloud" id="Cloud" class="cluster"><rect height="139.0000610351562" width="192.28125" y="-69.5000305175781" x="-96.140625" style="fill:#ffffff"/><g transform="translate(-57.96875, -69.5000305175781)" class="cluster-label"><foreignObject height="21" width="115.9375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Servicios Externos</p></span></div></foreignObject></g></g><g transform="translate(672.2890319824219, 733.6788635253906)" data-look="neo" data-et="cluster" data-id="Docker" id="Docker" class="cluster"><rect height="425.8889770507812" width="882.9270629882812" y="-212.9444885253906" x="-441.4635314941406" style="fill:#ffffff"/><g transform="translate(-52.515625, -212.9444885253906)" class="cluster-label"><foreignObject height="21" width="105.03125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Servicios Docker</p></span></div></foreignObject></g></g><g transform="translate(439.0911712646484, 579.234375)" data-look="neo" data-et="node" data-node="true" data-id="D1" id="flowchart-D1-0" class="node default service"><rect stroke="url(#gradient)" height="45" width="111.3749694824219" y="-22.5" x="-55.68748474121095" data-id="D1" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-39.6796875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="79.359375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 79.375px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Auth Service</p></span></div></foreignObject></g></g><g transform="translate(810.7578125, 579.234375)" data-look="neo" data-et="node" data-node="true" data-id="D2" id="flowchart-D2-1" class="node default service"><rect stroke="url(#gradient)" height="45" width="112.1405029296875" y="-22.5" x="-56.07025146484375" data-id="D2" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-40.0625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="80.125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 80.1405px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>User Service</p></span></div></foreignObject></g></g><g transform="translate(662, 579.234375)" data-look="neo" data-et="node" data-node="true" data-id="D3" id="flowchart-D3-2" class="node default service"><rect stroke="url(#gradient)" height="45" width="125.375" y="-22.5" x="-62.6875" data-id="D3" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-46.6875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="93.375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 93.375px; text-align: center; width: 93.375px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Library Service</p></span></div></foreignObject></g></g><g transform="translate(1046.846252441406, 579.234375)" data-look="neo" data-et="node" data-node="true" data-id="D4" id="flowchart-D4-3" class="node default service"><rect stroke="url(#gradient)" height="45" width="109.8123779296875" y="-22.5" x="-54.90618896484375" data-id="D4" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-38.8984375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="77.796875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 77.8124px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Orchestrator</p></span></div></foreignObject></g></g><g transform="translate(963.3163757324219, 679.271240234375)" data-look="neo" data-et="node" data-node="true" data-id="D4A" id="flowchart-D4A-4" class="node default service"><rect stroke="url(#gradient)" height="45" width="126.1716918945312" y="-22.5" x="-63.0858459472656" data-id="D4A" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-47.078125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="94.15625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 94.1717px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Agents Service</p></span></div></foreignObject></g></g><g transform="translate(298.1145935058594, 579.234375)" data-look="neo" data-et="node" data-node="true" data-id="D5" id="flowchart-D5-5" class="node default service"><rect stroke="url(#gradient)" height="45" width="110.578125" y="-22.5" x="-55.2890625" data-id="D5" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-39.2890625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="78.578125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 78.5781px; text-align: center; width: 78.5781px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Zep Memory</p></span></div></foreignObject></g></g><g transform="translate(439.0911712646484, 670.9255981445312)" data-look="neo" data-et="node" data-node="true" data-id="D1A" id="flowchart-D1A-6" class="node default db"><path transform="translate(-50.13282775878905, -39.19122314453125)" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container outer-path" d="M0,11.127490261132383 a50.13282775878905,11.127490261132383 0,0,0 100.2656555175781,0 a50.13282775878905,11.127490261132383 0,0,0 -100.2656555175781,0 l0,56.12746576679773 a50.13282775878905,11.127490261132383 0,0,0 100.2656555175781,0 l0,-56.12746576679773"/><g transform="translate(-38.1328125, -2.9999877528326735)" style="" class="label"><rect/><foreignObject height="20.999975505665347" width="76.265625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 76.2657px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(810.7577819824219, 670.9255981445312)" data-look="neo" data-et="node" data-node="true" data-id="D2A" id="flowchart-D2A-7" class="node default db"><path transform="translate(-50.1327819824219, -39.19122314453125)" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container outer-path" d="M0,11.127484623048266 a50.1327819824219,11.127484623048266 0,0,0 100.2655639648438,0 a50.1327819824219,11.127484623048266 0,0,0 -100.2655639648438,0 l0,56.12747704296597 a50.1327819824219,11.127484623048266 0,0,0 100.2655639648438,0 l0,-56.12747704296597"/><g transform="translate(-38.125, -2.9999962099588515)" style="" class="label"><rect/><foreignObject height="20.999992419917703" width="76.25"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 76.2656px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(569.3567504882812, 670.9255981445312)" data-look="neo" data-et="node" data-node="true" data-id="D3A" id="flowchart-D3A-8" class="node default db"><path transform="translate(-50.1328125, -39.19122314453125)" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container outer-path" d="M0,11.127488381771519 a50.1328125,11.127488381771519 0,0,0 100.265625,0 a50.1328125,11.127488381771519 0,0,0 -100.265625,0 l0,56.127469525519466 a50.1328125,11.127488381771519 0,0,0 100.265625,0 l0,-56.127469525519466"/><g transform="translate(-38.1328125, -2.9999905718739726)" style="" class="label"><rect/><foreignObject height="20.999981143747945" width="76.265625"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 76.2656px; text-align: center; width: 76.2656px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(682.8958129882812, 670.9255981445312)" data-look="neo" data-et="node" data-node="true" data-id="D3B" id="flowchart-D3B-9" class="node default db"><path transform="translate(-33.40625, -35.56207275390625)" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container outer-path" d="M0,8.708048224177258 a33.40625,8.708048224177258 0,0,0 66.8125,0 a33.40625,8.708048224177258 0,0,0 -66.8125,0 l0,53.70804905945799 a33.40625,8.708048224177258 0,0,0 66.8125,0 l0,-53.70804905945799"/><g transform="translate(-21.40625, -3)" style="" class="label"><rect/><foreignObject height="21" width="42.8125"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 42.8125px; text-align: center; width: 42.8125px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qdrant</p></span></div></foreignObject></g></g><g transform="translate(876.1640625, 790.6789245605469)" data-look="neo" data-et="node" data-node="true" data-id="D4A2" id="flowchart-D4A2-10" class="node default db"><path transform="translate(-33.40631103515625, -35.562103271484375)" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container outer-path" d="M0,8.708058592439903 a33.40631103515625,8.708058592439903 0,0,0 66.8126220703125,0 a33.40631103515625,8.708058592439903 0,0,0 -66.8126220703125,0 l0,53.70808935808895 a33.40631103515625,8.708058592439903 0,0,0 66.8126220703125,0 l0,-53.70808935808895"/><g transform="translate(-21.40625, -3)" style="" class="label"><rect/><foreignObject height="21" width="42.8125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 42.8126px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qdrant</p></span></div></foreignObject></g></g><g transform="translate(994.8593139648438, 790.6788940429688)" data-look="neo" data-et="node" data-node="true" data-id="D4A3" id="flowchart-D4A3-11" class="node default db"><rect stroke="url(#gradient)" height="45" width="110.5780029296875" y="-22.5" x="-55.28900146484375" data-id="D4A3" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-39.28125, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="78.5625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 78.578px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Zep Memory</p></span></div></foreignObject></g></g><g transform="translate(994.8593139648438, 895.4321899414062)" data-look="neo" data-et="node" data-node="true" data-id="D4A4" id="flowchart-D4A4-12" class="node default db"><path transform="translate(-50.13275146484375, -39.19122314453125)" style="fill:#f1fa8c !important;stroke:#44475a !important" class="basic label-container outer-path" d="M0,11.127480864322969 a50.13275146484375,11.127480864322969 0,0,0 100.2655029296875,0 a50.13275146484375,11.127480864322969 0,0,0 -100.2655029296875,0 l0,56.12748456041656 a50.13275146484375,11.127480864322969 0,0,0 100.2655029296875,0 l0,-56.12748456041656"/><g transform="translate(-38.125, -3)" style="" class="label"><rect/><foreignObject height="21" width="76.25"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 76.2655px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>PostgreSQL</p></span></div></foreignObject></g></g><g transform="translate(826.7578430175781, 1095.623413085938)" data-look="neo" data-et="node" data-node="true" data-id="D4A1" id="flowchart-D4A1-13" class="node default cloud"><polygon style="fill:#ffb86c !important;stroke:#44475a !important" transform="translate(-74.1406555175781,45.5)" class="label-container" points="26,0 122.2813110351562,0 148.2813110351562,-45.5 122.2813110351562,-91 26,-91 0,-45.5"/><g transform="translate(-42.140625, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="84.28125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 84.2813px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Vertex AI Gemini</p></span></div></foreignObject></g></g><g transform="translate(1137.088500976562, 34.49999856948853)" data-look="neo" data-et="node" data-node="true" data-id="A" id="flowchart-A-14" class="node default user"><rect stroke="url(#gradient)" height="44.99999523162842" width="160.015625" y="-22.49999761581421" x="-80.0078125" data-id="A" style="fill:#ff79c6 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-58.7578125, -10.499997615814209)" style="" class="label"><rect/><foreignObject height="20.999995231628418" width="117.515625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 128.016px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p> Usuario interactúa</p></span></div></foreignObject></g></g><g transform="translate(1137.088500976562, 180.3671836853027)" data-look="neo" data-et="node" data-node="true" data-id="B" id="flowchart-B-15" class="node default user decision"><polygon style="fill:#50fa7b !important;stroke:#44475a !important" transform="translate(-88.3671875,88.3671875)" class="label-container" points="88.3671875,0 176.734375,-88.3671875 88.3671875,-176.734375 0,-88.3671875"/><g transform="translate(-58.3671875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="116.734375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 116.734px; text-align: center; width: 116.734px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Acción del Usuario</p></span></div></foreignObject></g></g><g transform="translate(395, 445.2343597412109)" data-look="neo" data-et="node" data-node="true" data-id="C1" id="flowchart-C1-17" class="node default service"><rect stroke="url(#gradient)" height="65.99996948242188" width="232.0000610351562" y="-32.99998474121094" x="-116.0000305175781" data-id="C1" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-100, -20.999984741210938)" style="" class="label"><rect/><foreignObject height="41.999969482421875" width="200"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend → API Gateway /auth/*</p></span></div></foreignObject></g></g><g transform="translate(928.9999389648438, 445.2343597412109)" data-look="neo" data-et="node" data-node="true" data-id="C2" id="flowchart-C2-19" class="node default service"><rect stroke="url(#gradient)" height="65.99996948242188" width="231.9998779296875" y="-32.99998474121094" x="-115.99993896484375" data-id="C2" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-99.9921875, -20.999984741210938)" style="" class="label"><rect/><foreignObject height="41.999969482421875" width="199.984375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend → API Gateway /users/*</p></span></div></foreignObject></g></g><g transform="translate(662.0000305175781, 445.2343597412109)" data-look="neo" data-et="node" data-node="true" data-id="C3" id="flowchart-C3-21" class="node default service"><rect stroke="url(#gradient)" height="65.99996948242188" width="232.0000610351562" y="-32.99998474121094" x="-116.0000305175781" data-id="C3" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-100, -20.999984741210938)" style="" class="label"><rect/><foreignObject height="41.999969482421875" width="200"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend → API Gateway /prompts - CRUD</p></span></div></foreignObject></g></g><g transform="translate(1196, 445.2343597412109)" data-look="neo" data-et="node" data-node="true" data-id="C4" id="flowchart-C4-23" class="node default service"><rect stroke="url(#gradient)" height="65.99996948242188" width="232" y="-32.99998474121094" x="-116" data-id="C4" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-100, -20.999984741210938)" style="" class="label"><rect/><foreignObject height="41.999969482421875" width="200"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 200px; text-align: center; width: 200px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend → API Gateway /prompts - Workflow</p></span></div></foreignObject></g></g><g transform="translate(127.9999842643738, 445.2343597412109)" data-look="neo" data-et="node" data-node="true" data-id="C5" id="flowchart-C5-25" class="node default service"><rect stroke="url(#gradient)" height="65.99996948242188" width="232.0000009536743" y="-32.99998474121094" x="-116.00000047683714" data-id="C5" style="fill:#bd93f9 !important;stroke:#44475a !important" class="basic label-container"/><g transform="translate(-100, -20.999984741210938)" style="" class="label"><rect/><foreignObject height="41.999969482421875" width="200"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Frontend → API Gateway /suggest</p></span></div></foreignObject></g></g><g transform="translate(378.2734680175781, 1079.873413085938)" data-look="neo" data-et="node" data-node="true" data-id="D1B" id="flowchart-D1B-31" class="node default"><rect stroke="url(#gradient)" height="45" width="98.9375" y="-22.5" x="-49.46875" data-id="D1B" style="" class="basic label-container"/><g transform="translate(-33.46875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="66.9375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 66.9375px; text-align: center; width: 66.9375px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Respuesta</p></span></div></foreignObject></g></g><g transform="translate(512.2109527587891, 1228.123413085938)" data-look="neo" data-et="node" data-node="true" data-id="Z" id="flowchart-Z-33" class="node default"><rect stroke="url(#gradient)" height="45" width="80.24996948242188" y="-22.5" x="-40.12498474121094" data-id="Z" style="" class="basic label-container"/><g transform="translate(-24.1171875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="48.234375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 48.25px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Usuario</p></span></div></foreignObject></g></g><g transform="translate(646.1484375, 1079.873413085938)" data-look="neo" data-et="node" data-node="true" data-id="D2B" id="flowchart-D2B-39" class="node default"><rect stroke="url(#gradient)" height="45" width="98.9375" y="-22.5" x="-49.46875" data-id="D2B" style="" class="basic label-container"/><g transform="translate(-33.46875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="66.9375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 66.9375px; text-align: center; width: 66.9375px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Respuesta</p></span></div></foreignObject></g></g><g transform="translate(512.2109375, 1079.873413085938)" data-look="neo" data-et="node" data-node="true" data-id="D3C" id="flowchart-D3C-48" class="node default"><rect stroke="url(#gradient)" height="45" width="98.9375" y="-22.5" x="-49.46875" data-id="D3C" style="" class="basic label-container"/><g transform="translate(-33.46875, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="66.9375"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 66.9375px; text-align: center; width: 66.9375px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Respuesta</p></span></div></foreignObject></g></g><g transform="translate(1065.1484375, 1079.873413085938)" data-look="neo" data-et="node" data-node="true" data-id="D4B" id="flowchart-D4B-55" class="node default"><rect stroke="url(#gradient)" height="45" width="136.296875" y="-22.5" x="-68.1484375" data-id="D4B" style="" class="basic label-container"/><g transform="translate(-52.1484375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="104.296875"><div style="display: table; white-space: break-spaces; line-height: 1.5; max-width: 104.297px; text-align: center; width: 104.297px;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Actualiza Estado</p></span></div></foreignObject></g></g><g transform="translate(89.33331871032715, 556.2964477539062)" data-look="neo" data-et="node" data-node="true" data-id="D5A" id="flowchart-D5A-66" class="node default"><path transform="translate(-33.40622901916504, -35.56207275390625)" style="" class="basic label-container outer-path" d="M0,8.708044660083925 a33.40622901916504,8.708044660083925 0,0,0 66.81245803833008,0 a33.40622901916504,8.708044660083925 0,0,0 -66.81245803833008,0 l0,53.70805618764466 a33.40622901916504,8.708044660083925 0,0,0 66.81245803833008,0 l0,-53.70805618764466"/><g transform="translate(-21.3984375, -3)" style="" class="label"><rect/><foreignObject height="21" width="42.796875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 42.8125px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Qdrant</p></span></div></foreignObject></g></g><g transform="translate(212.8203277587891, 1079.873413085938)" data-look="neo" data-et="node" data-node="true" data-id="D5B" id="flowchart-D5B-68" class="node default"><rect stroke="url(#gradient)" height="45" width="161.9687805175781" y="-22.5" x="-80.98439025878905" data-id="D5B" style="" class="basic label-container"/><g transform="translate(-64.984375, -10.5)" style="" class="label"><rect/><foreignObject height="21" width="129.96875"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 129.969px; text-align: center;" xmlns="http://www.w3.org/1999/xhtml"><span class="nodeLabel"><p>Combina Resultados</p></span></div></foreignObject></g></g></g><g class="edgePaths"><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTEzNy4wODg1MDA5NzY1NjIsInkiOjU2Ljk5OTk5NjE4NTMwMjczfSx7IngiOjExMzcuMDg4NTAwOTc2NTYyLCJ5Ijo3NC43NDk5OTQyNzc5NTQwNn0seyJ4IjoxMTM3LjU4ODUwMDk3NjU2MiwieSI6NzQuNzQ5OTk0Mjc3OTU0MDZ9LHsieCI6MTEzNy41ODg1MDA5NzY1NjIsInkiOjkyLjQ5OTk5MjM3MDYwNTM4fV0=" data-id="L_A_B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 22.811792373657227 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_A_B_0" d="M1137.088500976562,56.99999618530273L1137.088500976562,74.49999427795406Q1137.088500976562,74.74999427795406 1137.338500976562,74.74999427795406L1137.338500976562,74.74999427795406Q1137.588500976562,74.74999427795406 1137.588500976562,74.99999427795406L1137.588500976562,88.49999237060538"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTExNS41MDkyNDQ3OTE2NjYsInkiOjI0Ny4xMDUwNzgxMjQ5OTk0fSx7IngiOjExMDcuNjMyODEyNSwieSI6MjY4LjczNDM3NX0seyJ4IjoxMTA3LjYzMjgxMjUsInkiOjMwMy43MzQzNzV9LHsieCI6Mzk0Ljk5OTk5OTk5OTk5OTksInkiOjMwMy43MzQzNzV9LHsieCI6Mzk0Ljk5OTk5OTk5OTk5OTksInkiOjQxMi4yMzQzNzV9XQ==" data-id="L_B_C1_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 860.5904541015625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C1_0" d="M1115.509244791666,247.1050781249994L1111.571028645833,257.9197265624997Q1107.6328125,268.734375 1107.6328125,280.24376973812707L1107.6328125,296.6633071881345Q1107.6328125,303.734375 1100.5617446881345,303.734375L402.07106781186536,303.734375Q394.9999999999999,303.734375 394.9999999999999,310.8054428118655L394.9999999999999,408.234375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTE1OS42Njc4Mzg1NDE2NjYsInkiOjI0Ny4xMDUwNzgxMjUwMDAyfSx7IngiOjExNjYuNTQ0MjcwODMzMzMzLCJ5IjoyNjguNzM0Mzc1fSx7IngiOjExNjYuNTQ0MjcwODMzMzMzLCJ5IjozMzguNzM0Mzc1fSx7IngiOjkyOC45OTk5OTk5OTk5OTk4LCJ5IjozMzguNzM0Mzc1fSx7IngiOjkyOC45OTk5OTk5OTk5OTk4LCJ5Ijo0MTIuMjM0Mzc1fV0=" data-id="L_B_C2_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 385.2335510253906 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C2_0" d="M1159.667838541666,247.1050781250002L1163.1060546874996,257.9197265625001Q1166.544270833333,268.734375 1166.544270833333,280.08241232335195L1166.544270833333,331.6633071881345Q1166.544270833333,338.734375 1159.4732030214675,338.734375L936.0710678118653,338.734375Q928.9999999999998,338.734375 928.9999999999998,345.8054428118655L928.9999999999998,408.234375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTEzNy4wODg1NDE2NjY2NjcsInkiOjI2OC43MzQzNzV9LHsieCI6MTEzNy4wODg1NDE2NjY2NjcsInkiOjMyMS4yMzQzNzV9LHsieCI6NjYxLjk5OTk5OTk5OTk5OTksInkiOjMyMS4yMzQzNzV9LHsieCI6NjYxLjk5OTk5OTk5OTk5OTksInkiOjQxMi4yMzQzNzV9XQ==" data-id="L_B_C3_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 600.26025390625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C3_0" d="M1137.088541666667,268.734375L1137.088541666667,314.1633071881345Q1137.088541666667,321.234375 1130.0174738548014,321.234375L669.0710678118653,321.234375Q661.9999999999999,321.234375 661.9999999999999,328.3054428118655L661.9999999999999,408.234375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTE3Mi45MTU0MTY2NjY2NjYsInkiOjIzMy44NTc1MDAwMDAwMDA1fSx7IngiOjExOTYsInkiOjI2OC43MzQzNzV9LHsieCI6MTE5NiwieSI6NDEyLjIzNDM3NX1d" data-id="L_B_C4_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 171.34751892089844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C4_0" d="M1172.915416666666,233.8575000000005L1186.4242887866235,254.26710573190925Q1196,268.734375 1196,286.0836149060427L1196,408.234375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTEwMi4yNjE2NjY2NjY2NjcsInkiOjIzMy44NTc0OTk5OTk5OTk4fSx7IngiOjEwNzguMTc3MDgzMzMzMzMzLCJ5IjoyNjguNzM0Mzc1fSx7IngiOjEwNzguMTc3MDgzMzMzMzMzLCJ5IjoyODYuMjM0Mzc1fSx7IngiOjEyOCwieSI6Mjg2LjIzNDM3NX0seyJ4IjoxMjgsInkiOjQxMi4yMzQzNzV9XQ==" data-id="L_B_C5_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 1117.2073974609375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_B_C5_0" d="M1102.261666666667,233.8574999999998L1083.1491619136234,261.5343100981107Q1078.177083333333,268.734375 1078.177083333333,277.484375L1078.177083333333,279.1633071881345Q1078.177083333333,286.234375 1071.1060155214675,286.234375L135.07106781186548,286.234375Q128,286.234375 128,293.3054428118655L128,408.234375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Mzk0Ljk5OTk5OTk5OTk5OTksInkiOjQ3OC4yMzQzNzV9LHsieCI6Mzk0Ljk5OTk5OTk5OTk5OTksInkiOjQ5NS43MzQzNzV9LHsieCI6NDM5LjA5MTE0NTgzMzMzMzMsInkiOjQ5NS43MzQzNzV9LHsieCI6NDM5LjA5MTE0NTgzMzMzMzMsInkiOjUxMy4yMzQzNzV9LHsieCI6NDM5LjA5MTE0NTgzMzMzMzMsInkiOjU1Ni43MzQzNzV9XQ==" data-id="L_C1_D1_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 104.26287841796875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C1_D1_0" d="M394.9999999999999,478.234375L394.9999999999999,488.6633071881345Q394.9999999999999,495.734375 402.07106781186536,495.734375L432.02007802146784,495.734375Q439.0911458333333,495.734375 439.0911458333333,502.8054428118655L439.0911458333333,513.234375L439.0911458333333,552.734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDM5LjA5MTE3MTI2NDY0ODQsInkiOjYwMS43MzQzNzV9LHsieCI6NDM5LjA5MTE3MTI2NDY0ODQsInkiOjYwMS43MzQzNzV9LHsieCI6NDQwLjA5MTE3MTI2NDY0ODQsInkiOjYxNy43MzQzNzV9LHsieCI6NDM5LjA5MTE3MTI2NDY0ODQsInkiOjYzMS43MzQzNzV9XQ==" data-id="L_D1_D1A_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 17.052000045776367 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1_D1A_0" d="M439.0911712646484,601.734375L439.0911712646484,601.734375L439.7781673530807,612.7263124149172Q440.0911712646484,617.734375 439.7336653646242,622.7394576003385L439.3761594646,627.7445402006771"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NDM5LjA5MTE0NTgzMzMzMzMsInkiOjcxMC4xMTY4MzY1NDc4NTE2fSx7IngiOjQzOS4wOTExNDU4MzMzMzMzLCJ5Ijo5NTQuMTIzNDQzNjAzNTE1Nn0seyJ4Ijo0MzkuMDkxMTQ1ODMzMzMzMywieSI6OTcxLjYyMzQ0MzYwMzUxNTZ9LHsieCI6Mzc4LjI3MzQzNzQ5OTk5OTksInkiOjk3MS42MjM0NDM2MDM1MTU2fSx7IngiOjM3OC4yNzM0Mzc0OTk5OTk5LCJ5IjoxMDU3LjM3MzQ0MzYwMzUxNn1d" data-id="L_D1A_D1B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 389.7460632324219 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1A_D1B_0" d="M439.0911458333333,710.1168365478516L439.0911458333333,954.1234436035156L439.0911458333333,964.5523757916501Q439.0911458333333,971.6234436035156 432.02007802146784,971.6234436035156L385.34450531186536,971.6234436035156Q378.2734374999999,971.6234436035156 378.2734374999999,978.6945114153812L378.2734374999999,1053.373443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Mzc4LjI3MzQzNzQ5OTk5OTksInkiOjExMDIuMzczNDQzNjAzNTE2fSx7IngiOjM3OC4yNzM0Mzc0OTk5OTk5LCJ5IjoxMTcwLjYyMzQ0MzYwMzUxNn0seyJ4Ijo0OTguODM1OTM3NDk5OTk5OSwieSI6MTE3MC42MjM0NDM2MDM1MTZ9LHsieCI6NDk4LjgzNTkzNzQ5OTk5OTksInkiOjEyMDUuNjIzNDQzNjAzNTE2fV0=" data-id="L_D1B_Z_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 205.48435974121094 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D1B_Z_0" d="M378.2734374999999,1102.373443603516L378.2734374999999,1163.5523757916505Q378.2734374999999,1170.623443603516 385.34450531186536,1170.623443603516L491.7648696881344,1170.623443603516Q498.8359374999999,1170.623443603516 498.8359374999999,1177.6945114153816L498.8359374999999,1201.623443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTI4Ljk5OTk5OTk5OTk5OTgsInkiOjQ3OC4yMzQzNzV9LHsieCI6OTI4Ljk5OTk5OTk5OTk5OTgsInkiOjQ5NS43MzQzNzV9LHsieCI6ODEwLjc1NzgxMjQ5OTk5OTksInkiOjQ5NS43MzQzNzV9LHsieCI6ODEwLjc1NzgxMjQ5OTk5OTksInkiOjUxMy4yMzQzNzV9LHsieCI6ODEwLjc1NzgxMjQ5OTk5OTksInkiOjU1Ni43MzQzNzV9XQ==" data-id="L_C2_D2_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 178.41390991210938 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C2_D2_0" d="M928.9999999999998,478.234375L928.9999999999998,488.6633071881345Q928.9999999999998,495.734375 921.9289321881342,495.734375L817.8288803118653,495.734375Q810.7578124999999,495.734375 810.7578124999999,502.8054428118655L810.7578124999999,513.234375L810.7578124999999,552.734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODEwLjc1NzgwNTAxMTMyNzgsInkiOjYwMS43MzQzNzV9LHsieCI6ODEwLjc1NzgwNTAxMTMyNzgsInkiOjYwMS43MzQzNzV9LHsieCI6ODExLjc1NzgwMDAxODg3OTgsInkiOjYxNy43MzQzNzUwMDAwMDAyfSx7IngiOjgxMS40OTQ1OTM5NTQzNjg2LCJ5Ijo2MzEuNzM1NTc2ODc3NDgzMX1d" data-id="L_D2_D2A_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 17.029399871826172 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2_D2A_0" d="M810.7578050113278,601.734375L810.7578050113278,601.734375L811.4457954996373,612.7422777693336Q811.7578000188798,617.7343750000002 811.6637879836262,622.7353292407112L811.5697759483727,627.7362834814221"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6ODEwLjc1NzgxMjQ5OTk5OTksInkiOjcxMC4xMTY4MzY1NDc4NTE2fSx7IngiOjgxMC43NTc4MTI0OTk5OTk5LCJ5Ijo5NTQuMTIzNDQzNjAzNTE1Nn0seyJ4Ijo4MTAuNzU3ODEyNDk5OTk5OSwieSI6OTg5LjEyMzQ0MzYwMzUxNTZ9LHsieCI6NjQ2LjE0ODQzNzQ5OTk5OTksInkiOjk4OS4xMjM0NDM2MDM1MTU2fSx7IngiOjY0Ni4xNDg0Mzc0OTk5OTk5LCJ5IjoxMDU3LjM3MzQ0MzYwMzUxNn1d" data-id="L_D2A_D2B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 493.5376892089844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2A_D2B_0" d="M810.7578124999999,710.1168365478516L810.7578124999999,954.1234436035156L810.7578124999999,982.0523757916501Q810.7578124999999,989.1234436035156 803.6867446881345,989.1234436035156L653.2195053118653,989.1234436035156Q646.1484374999999,989.1234436035156 646.1484374999999,996.1945114153812L646.1484374999999,1053.373443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjQ2LjE0ODQzNzQ5OTk5OTksInkiOjExMDIuMzczNDQzNjAzNTE2fSx7IngiOjY0Ni4xNDg0Mzc0OTk5OTk5LCJ5IjoxMTcwLjYyMzQ0MzYwMzUxNn0seyJ4Ijo1MjUuNTg1OTM3NDk5OTk5OSwieSI6MTE3MC42MjM0NDM2MDM1MTZ9LHsieCI6NTI1LjU4NTkzNzQ5OTk5OTksInkiOjEyMDUuNjIzNDQzNjAzNTE2fV0=" data-id="L_D2B_Z_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 205.48439025878906 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D2B_Z_0" d="M646.1484374999999,1102.373443603516L646.1484374999999,1163.5523757916505Q646.1484374999999,1170.623443603516 639.0773696881345,1170.623443603516L532.6570053118653,1170.623443603516Q525.5859374999999,1170.623443603516 525.5859374999999,1177.6945114153816L525.5859374999999,1201.623443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjYxLjk5OTk5OTk5OTk5OTksInkiOjQ3OC4yMzQzNzV9LHsieCI6NjYxLjk5OTk5OTk5OTk5OTksInkiOjUxMy4yMzQzNzV9LHsieCI6NjYxLjk5OTk5OTk5OTk5OTksInkiOjU1Ni43MzQzNzV9XQ==" data-id="L_C3_D3_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 65.5 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C3_D3_0" d="M661.9999999999999,478.234375L661.9999999999999,513.234375L661.9999999999999,552.734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjQxLjEwNDE2NjY2NjY2NjUsInkiOjYwMS43MzQzNzV9LHsieCI6NjQxLjEwNDE2NjY2NjY2NjUsInkiOjYxNi43MzQzNzV9LHsieCI6NTY5LjM1Njc3MDgzMzMzMzMsInkiOjYxNi43MzQzNzV9LHsieCI6NTY5LjM1Njc3MDgzMzMzMzMsInkiOjYzMS43MzQzNzV9XQ==" data-id="L_D3_D3A_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 84.01103973388672 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_D3A_0" d="M641.1041666666665,601.734375L641.1041666666665,609.6633071881345Q641.1041666666665,616.734375 634.033098854801,616.734375L574.8567708333333,616.734375Q569.3567708333333,616.734375 569.3567708333333,622.234375L569.3567708333333,627.734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjY3LjEyNzU5ODY1MjQzODUsInkiOjYwMS43MzQzNzV9LHsieCI6NjY3LjEyNzU5ODY1MjQzODUsInkiOjYwMS43MzQzNzV9LHsieCI6NjcxLjk1OTUyNzIzNTIyOTksInkiOjYxOS42NzkwMjA1ODIwMTc0fSx7IngiOjY3NS4zMDY2ODIwMDQyMDU0LCJ5Ijo2MzUuNTkxMjEwNDYxMzY4MX1d" data-id="L_D3_D3B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 21.841054916381836 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3_D3B_0" d="M667.1275986524385,601.734375L667.1275986524385,601.734375L670.3656268679109,613.7596497356745Q671.9595272352299,619.6790205820174 673.2214110793986,625.6779470494197L674.4832949235675,631.676873516822"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NjgyLjg5NTgzMzMzMzMzMzMsInkiOjcwNi40ODc2Nzg1Mjc4MzJ9LHsieCI6NjgyLjg5NTgzMzMzMzMzMzMsInkiOjk1NC4xMjM0NDM2MDM1MTU2fSx7IngiOjY4Mi44OTU4MzMzMzMzMzMzLCJ5Ijo5NzEuNjIzNDQzNjAzNTE1Nn0seyJ4Ijo1MTIuMjEwOTM3NDk5OTk5OSwieSI6OTcxLjYyMzQ0MzYwMzUxNTZ9LHsieCI6NTEyLjIxMDkzNzQ5OTk5OTksInkiOjEwNTcuMzczNDQzNjAzNTE2fV0=" data-id="L_D3B_D3C_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 503.242431640625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3B_D3C_0" d="M682.8958333333333,706.487678527832L682.8958333333333,954.1234436035156L682.8958333333333,964.5523757916501Q682.8958333333333,971.6234436035156 675.8247655214677,971.6234436035156L519.2820053118653,971.6234436035156Q512.2109374999999,971.6234436035156 512.2109374999999,978.6945114153812L512.2109374999999,1053.373443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6NTEyLjIxMDkzOTgxNTgzNjUsInkiOjExMDIuMzczNDEzMDg1OTM4fSx7IngiOjUxMi4yMTA5Mzk4MTU4MzY1LCJ5IjoxMTUzLjk5ODQxMzA4NTkzOH0seyJ4Ijo1MTIuMjEwOTUwNDQyOTUyNiwieSI6MTE1My45OTg0MTMwODU5Mzh9LHsieCI6NTEyLjIxMDk1MDQ0Mjk1MjYsInkiOjEyMDUuNjIzNDEzMDg1OTM4fV0=" data-id="L_D3C_Z_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 90.25 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D3C_Z_0" d="M512.2109398158365,1102.373413085938L512.2109398158365,1153.9984077723798Q512.2109398158365,1153.998413085938 512.2109451293945,1153.998413085938L512.2109451293945,1153.998413085938Q512.2109504429526,1153.998413085938 512.2109504429526,1153.9984183994961L512.2109504429526,1201.623413085938"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTE5NiwieSI6NDc4LjIzNDM3NX0seyJ4IjoxMTk2LCJ5Ijo0OTUuNzM0Mzc1fSx7IngiOjEwNDYuODQ2MzU0MTY2NjY3LCJ5Ijo0OTUuNzM0Mzc1fSx7IngiOjEwNDYuODQ2MzU0MTY2NjY3LCJ5Ijo1MTMuMjM0Mzc1fSx7IngiOjEwNDYuODQ2MzU0MTY2NjY3LCJ5Ijo1NTYuNzM0Mzc1fV0=" data-id="L_C4_D4_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 209.32525634765625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C4_D4_0" d="M1196,478.234375L1196,488.6633071881345Q1196,495.734375 1188.9289321881345,495.734375L1053.9174219785325,495.734375Q1046.846354166667,495.734375 1046.846354166667,502.8054428118655L1046.846354166667,513.234375L1046.846354166667,552.734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTAyOC41NDQyNzA4MzMzMzMsInkiOjYwMS43MzQzNzV9LHsieCI6MTAyOC41NDQyNzA4MzMzMzMsInkiOjYxNi43MzQzNzV9LHsieCI6OTYzLjMxNjQwNjI0OTk5OTksInkiOjYxNi43MzQzNzV9LHsieCI6OTYzLjMxNjQwNjI0OTk5OTksInkiOjY1Ni43NzEyMjExNjA4ODg3fV0=" data-id="L_D4_D4A_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 101.93646240234375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_D4A_0" d="M1028.544270833333,601.734375L1028.544270833333,609.6633071881345Q1028.544270833333,616.734375 1021.4732030214675,616.734375L970.3874740618653,616.734375Q963.3164062499999,616.734375 963.3164062499999,623.8054428118655L963.3164062499999,652.7712211608887"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTA2NS4xNDg0Mzc1LCJ5Ijo2MDEuNzM0Mzc1fSx7IngiOjEwNjUuMTQ4NDM3NSwieSI6OTU0LjEyMzQ0MzYwMzUxNTZ9LHsieCI6MTA2NS4xNDg0Mzc1LCJ5IjoxMDU3LjM3MzQ0MzYwMzUxNn1d" data-id="L_D4_D4B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 442.6390380859375 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4_D4B_0" d="M1065.1484375,601.734375L1065.1484375,954.1234436035156L1065.1484375,1053.373443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTMxLjc3MzQzNzQ5OTk5OTksInkiOjcwMS43NzEyMjExNjA4ODg3fSx7IngiOjkzMS43NzM0Mzc0OTk5OTk5LCJ5Ijo3MjUuMTE2ODM2NTQ3ODUxNn0seyJ4Ijo4MjYuNzU3ODEyNDk5OTk5OSwieSI6NzI1LjExNjgzNjU0Nzg1MTZ9LHsieCI6ODI2Ljc1NzgxMjQ5OTk5OTksInkiOjk1NC4xMjM0NDM2MDM1MTU2fSx7IngiOjgyNi43NTc4MTI0OTk5OTk5LCJ5IjoxMDA2LjYyMzQ0MzYwMzUxNn0seyJ4Ijo4MjYuNzU3ODEyNDk5OTk5OSwieSI6MTA1MC4xMjM0NDM2MDM1MTZ9XQ==" data-id="L_D4A_D4A1_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 435.0394592285156 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4A_D4A1_0" d="M931.7734374999999,701.7712211608887L931.7734374999999,718.045768735986Q931.7734374999999,725.1168365478516 924.7023696881345,725.1168365478516L833.8288803118653,725.1168365478516Q826.7578124999999,725.1168365478516 826.7578124999999,732.1879043597171L826.7578124999999,954.1234436035156L826.7578124999999,1006.623443603516L826.7578124999999,1046.123443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTYzLjMxNjQwNjI0OTk5OTksInkiOjcwMS43NzEyMjExNjA4ODg3fSx7IngiOjk2My4zMTY0MDYyNDk5OTk5LCJ5Ijo3NDAuMTE2ODM2NTQ3ODUxNn0seyJ4Ijo4NzYuMTY0MDYyNDk5OTk5OSwieSI6NzQwLjExNjgzNjU0Nzg1MTZ9LHsieCI6ODc2LjE2NDA2MjQ5OTk5OTksInkiOjc1NS4xMTY4MzY1NDc4NTE2fV0=" data-id="L_D4A_D4A2_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 122.76155090332031 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4A_D4A2_0" d="M963.3164062499999,701.7712211608887L963.3164062499999,733.045768735986Q963.3164062499999,740.1168365478516 956.2453384381345,740.1168365478516L881.6640624999999,740.1168365478516Q876.1640624999999,740.1168365478516 876.1640624999999,745.6168365478516L876.1640624999999,751.1168365478516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTY5LjY4NjgxODc0OTI4LCJ5Ijo3MDEuNzcxMjQwMjM0Mzc1fSx7IngiOjk2OS42ODY4MTg3NDkyOCwieSI6NzAxLjc3MTI0MDIzNDM3NX0seyJ4Ijo5ODAuMDg3ODQ0ODQ4NjMyOCwieSI6NzM1Ljk3NTA2NzEzODY3MTl9LHsieCI6OTg4LjQ4ODg3MDk0Nzk4NTcsInkiOjc2OC4xNzg4OTQwNDI5Njg4fV0=" data-id="L_D4A_D4A3_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 56.0279541015625 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4A_D4A3_0" d="M969.68681874928,701.771240234375L969.68681874928,701.771240234375L975.8283197725831,721.9675973944336Q980.0878448486328,735.9750671386719 983.7835125411893,750.141746453721L987.4791802337458,764.3084257687702"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6OTk0Ljg1OTMxMzk2NDg0MzgsInkiOjgxMy4xNzg4OTQwNDI5Njg4fSx7IngiOjk5NC44NTkzMTM5NjQ4NDM4LCJ5Ijo4MTMuMTc4ODk0MDQyOTY4OH0seyJ4Ijo5OTUuODU5MzEzOTY0ODQzOCwieSI6ODM1LjcwOTkzMDQxOTkyMTl9LHsieCI6OTk0Ljg1OTMxMzk2NDg0MzgsInkiOjg1Ni4yNDA5NjY3OTY4NzV9XQ==" data-id="L_D4A3_D4A4_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 30.096702575683594 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4A3_D4A4_0" d="M994.8593139648438,813.1788940429688L994.8593139648438,813.1788940429688L995.492284833868,827.440383718506Q995.8593139648438,835.7099304199219 995.4566121152532,843.977816742932L995.0539102656626,852.2457030659422"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTA2NS4xNDg0Mzc1LCJ5IjoxMTAyLjM3MzQ0MzYwMzUxNn0seyJ4IjoxMDY1LjE0ODQzNzUsInkiOjExODguMTIzNDQzNjAzNTE2fSx7IngiOjUzOC45NjA5Mzc0OTk5OTk5LCJ5IjoxMTg4LjEyMzQ0MzYwMzUxNn0seyJ4Ijo1MzguOTYwOTM3NDk5OTk5OSwieSI6MTIwNS42MjM0NDM2MDM1MTZ9XQ==" data-id="L_D4B_Z_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 611.2302856445312 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D4B_Z_0" d="M1065.1484375,1102.373443603516L1065.1484375,1181.0523757916505Q1065.1484375,1188.123443603516 1058.0773696881345,1188.123443603516L545.7109374999999,1188.123443603516Q538.9609374999999,1188.123443603516 538.9609374999999,1194.873443603516L538.9609374999999,1201.623443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTY2LjY2NjY2NjY2NjY2NjcsInkiOjQ3OC4yMzQzNzV9LHsieCI6MTY2LjY2NjY2NjY2NjY2NjcsInkiOjQ5NS43MzQzNzV9LHsieCI6Mjk4LjExNDU4MzMzMzMzMzMsInkiOjQ5NS43MzQzNzV9LHsieCI6Mjk4LjExNDU4MzMzMzMzMzMsInkiOjUxMy4yMzQzNzV9LHsieCI6Mjk4LjExNDU4MzMzMzMzMzMsInkiOjU1Ni43MzQzNzV9XQ==" data-id="L_C5_D5_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 191.61964416503906 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C5_D5_0" d="M166.6666666666667,478.234375L166.6666666666667,488.6633071881345Q166.6666666666667,495.734375 173.7377344785322,495.734375L291.04351552146784,495.734375Q298.1145833333333,495.734375 298.1145833333333,502.8054428118655L298.1145833333333,513.234375L298.1145833333333,552.734375"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MTE2LjUxMDkyMDgzOTQ5NzUsInkiOjQ3OC4yMzQzNDQ0ODI0MjE5fSx7IngiOjExNi41MTA5MjA4Mzk0OTc1LCJ5Ijo0OTkuNzk0NDM1NjY3OTk1Nn0seyJ4IjoxMDEuNzE0MzgyMjk1NDcwNSwieSI6NDk5Ljc5NDQzNTY2Nzk5NTZ9LHsieCI6MTAxLjcxNDM4MjI5NTQ3MDUsInkiOjUyMS4zNTQ1MjY4NTM1Njk0fV0=" data-id="L_C5_D5A_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 39.588462829589844 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_C5_D5A_0" d="M116.5109208394975,478.2343444824219L116.5109208394975,492.7233678561301Q116.5109208394975,499.7944356679956 109.43985302763203,499.7944356679956L108.78545010733598,499.7944356679956Q101.7143822954705,499.7944356679956 101.7143822954705,506.86550347986105L101.7143822954705,517.3545268535694"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6Mjk4LjExNDU4MzMzMzMzMzMsInkiOjYwMS43MzQzNzV9LHsieCI6Mjk4LjExNDU4MzMzMzMzMzMsInkiOjk1NC4xMjM0NDM2MDM1MTU2fSx7IngiOjI5OC4xMTQ1ODMzMzMzMzMzLCJ5Ijo5NzEuNjIzNDQzNjAzNTE1Nn0seyJ4IjoyMTIuODIwMzEyNDk5OTk5OSwieSI6OTcxLjYyMzQ0MzYwMzUxNTZ9LHsieCI6MjEyLjgyMDMxMjQ5OTk5OTksInkiOjEwNTcuMzczNDQzNjAzNTE2fV0=" data-id="L_D5_D5B_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 522.6050415039062 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D5_D5B_0" d="M298.1145833333333,601.734375L298.1145833333333,954.1234436035156L298.1145833333333,964.5523757916501Q298.1145833333333,971.6234436035156 291.04351552146784,971.6234436035156L219.89138031186536,971.6234436035156Q212.8203124999999,971.6234436035156 212.8203124999999,978.6945114153812L212.8203124999999,1053.373443603516"/><path marker-end="url(#my-svg_flowchart-v2-pointEnd-margin)" data-points="W3sieCI6MjEyLjgyMDMxMjQ5OTk5OTksInkiOjExMDIuMzczNDQzNjAzNTE2fSx7IngiOjIxMi44MjAzMTI0OTk5OTk5LCJ5IjoxMTg4LjEyMzQ0MzYwMzUxNn0seyJ4Ijo0ODUuNDYwOTM3NDk5OTk5OSwieSI6MTE4OC4xMjM0NDM2MDM1MTZ9LHsieCI6NDg1LjQ2MDkzNzQ5OTk5OTksInkiOjEyMDUuNjIzNDQzNjAzNTE2fV0=" data-id="L_D5B_Z_0" data-et="edge" data-edge="true" style="stroke-dasharray: 0 0 357.6834716796875 9; stroke-dashoffset: 0;;" class="edge-thickness-normal edge-pattern-solid edge-thickness-normal edge-pattern-solid flowchart-link" id="L_D5B_Z_0" d="M212.8203124999999,1102.373443603516L212.8203124999999,1181.0523757916505Q212.8203124999999,1188.123443603516 219.89138031186536,1188.123443603516L478.7109374999999,1188.123443603516Q485.4609374999999,1188.123443603516 485.4609374999999,1194.873443603516L485.4609374999999,1201.623443603516"/></g><g class="edgeLabels"><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_A_B_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g transform="translate(726.0758, 303.73438)" class="edgeLabel"><g transform="translate(-45.140625, -10.5)" data-id="L_B_C1_0" class="label"><foreignObject height="21" width="90.28125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Login/Registro</p></span></div></foreignObject></g></g><g transform="translate(1057.37017, 338.73438)" class="edgeLabel"><g transform="translate(-50.9765625, -10.5)" data-id="L_B_C2_0" class="label"><foreignObject height="21" width="101.953125"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Gestión de perfil</p></span></div></foreignObject></g></g><g transform="translate(880.29427, 321.23438)" class="edgeLabel"><g transform="translate(-65.7421875, -10.5)" data-id="L_B_C3_0" class="label"><foreignObject height="21" width="131.484375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Buscar/Crear Prompt</p></span></div></foreignObject></g></g><g transform="translate(1196, 319.57209)" class="edgeLabel"><g transform="translate(-56.28125, -10.5)" data-id="L_B_C4_0" class="label"><foreignObject height="21" width="112.5625"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Ejecutar Workflow</p></span></div></foreignObject></g></g><g transform="translate(570.0309, 286.23438)" class="edgeLabel"><g transform="translate(-59.546875, -10.5)" data-id="L_B_C5_0" class="label"><foreignObject height="21" width="119.09375"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"><p>Sugerencia Rápida</p></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C1_D1_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D1_D1A_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D1A_D1B_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D1B_Z_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C2_D2_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D2_D2A_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D2A_D2B_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D2B_Z_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C3_D3_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D3_D3A_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D3_D3B_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D3B_D3C_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D3C_Z_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C4_D4_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4_D4A_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4_D4B_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4A_D4A1_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4A_D4A2_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4A_D4A3_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4A3_D4A4_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D4B_Z_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C5_D5_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_C5_D5A_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D5_D5B_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g><g class="edgeLabel"><g transform="translate(0, 0)" data-id="L_D5B_Z_0" class="label"><foreignObject height="0" width="0"><div style="display: table-cell; white-space: normal; line-height: 1.5; max-width: 200px; text-align: center;" class="labelBkg" xmlns="http://www.w3.org/1999/xhtml"><span class="edgeLabel"></span></div></foreignObject></g></g></g></g></g><defs><filter width="130%" height="130%" id="drop-shadow"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="4" dx="4"/></filter></defs><defs><filter width="150%" height="150%" id="drop-shadow-small"><feDropShadow flood-color="#FFFFFF" flood-opacity="0.06" stdDeviation="0" dy="2" dx="2"/></filter></defs><linearGradient y2="0%" x2="100%" y1="0%" x1="0%" gradientUnits="objectBoundingBox" id="my-svg-gradient"><stop stop-opacity="1" stop-color="#0042eb" offset="0%"/><stop stop-opacity="1" stop-color="#eb0042" offset="100%"/></linearGradient></svg>