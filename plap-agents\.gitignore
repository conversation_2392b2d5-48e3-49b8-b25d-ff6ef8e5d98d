# === Archivos de Entorno Virtual Python ===
venv/
*.venv/
env/
ENV/
.env/ # También ignora la carpeta si la llamas así
.venv/

# === Archivos Compilados / Cache de Python ===
__pycache__/
*.py[cod] # *.pyc, *.pyo, *.pyd
*.so # Archivos compilados de C

# === Archivos de Distribución / Build de Python ===
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# === Archivos de Configuración Local/Sensible ===
.env
.env*.local
.env.*.local

# === Archivos del Sistema Operativo ===
.DS_Store
Thumbs.db
ehthumbs.db

# === Archivos de IDEs / Editores ===
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# === Archivos de Log y Debug ===
*.log
logs/
*.log.*
debug.log*

# === Archivos Temporales ===
*~
*.tmp
*.swp
*.swo

# === Archivos de Testing ===
.pytest_cache/
.coverage
.coverage.*
htmlcov/
nosetests.xml
coverage.xml
*.cover

# === Credenciales (Aunque no deberían estar aquí) ===
*.pem
*.key
*.cer
*.p12
*.pfx
*.jks
google-cloud-key.json # Por si acaso

# === Archivos específicos de Windows ===
desktop.ini