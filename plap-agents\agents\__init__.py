# PLAP/plap-agents/agents/__init__.py
print("[AGENTS/__INIT__] Initializing 'agents' package...")

# Opcionalmente, puedes importar clases de agentes aquí para facilitar el acceso
# desde otros módulos, aunque la AgentFactory es el punto de entrada principal.
# Por ejemplo:
# from .generator_agent import GeneratorAgent
# from .extractor_agent import ExtractorAgent
# ... y así sucesivamente para los demás agentes.

# Por ahora, puede estar vacío o solo con el print.
# La AgentFactory se encargará de importar los agentes específicos que necesite.