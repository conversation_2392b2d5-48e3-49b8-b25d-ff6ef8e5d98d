# PLAP/plap-agents/agents/agent_factory.py
print("[AGENT_FACTORY] Loading agent_factory.py (v2 - absolute imports)")

from typing import Dict, Any, Optional, Type, cast
# --- CORRECCIÓN DE IMPORTACIÓN ---
# Importar desde la raíz del servicio (/app)
from schemas.agent_schemas import AgentType, AgentExecutionResponse # Enum local y schemas
from config import Settings # Importar la instancia 'settings'
from clients.vertex_ai_client import VertexAIClient
from clients.zep_client_wrapper import ZepClientWrapper
from clients.qdrant_client_wrapper import QdrantClientWrapper
from document_processing.storage_handler import StorageHandler
from clients.internal_services_client import InternalServicesClient
# --- FIN DE CORRECCIÓN ---

# Importar las clases de agente específicas usando importación relativa DENTRO del paquete 'agents'
from .base_agent import BaseAgent
from .generator_agent import GeneratorAgent
from .extractor_agent import ExtractorAgent
from .compliance_agent import ComplianceAgent
from .reviewer_agent import ReviewerAgent
from .recommender_agent import RecommenderAgent
from .suggester_agent import SuggesterAgent
import traceback


class AgentFactory:
    """
    Fábrica para crear instancias de agentes PLAP específicos.
    Se inicializa con todas las dependencias (configuración y clientes)
    que los agentes podrían necesitar.
    """

    def __init__(self,
                 config: Settings, # Usar el tipo Settings de config.py
                 vertex_client: Optional[VertexAIClient] = None,
                 zep_client: Optional[ZepClientWrapper] = None,
                 qdrant_client: Optional[QdrantClientWrapper] = None,
                 storage_handler: Optional[StorageHandler] = None,
                 internal_services_client: Optional[InternalServicesClient] = None
                 ):
        self._config = config
        self._vertex_client = vertex_client
        self._zep_client = zep_client
        self._qdrant_client = qdrant_client
        self._storage_handler = storage_handler
        self._internal_services_client = internal_services_client
        print("[AgentFactory] Initialized with configuration and client instances.")

        self._agent_map: Dict[AgentType, Type[BaseAgent]] = {
            AgentType.GENERATOR: GeneratorAgent,
            AgentType.EXTRACTOR: ExtractorAgent,
            AgentType.COMPLIANCE: ComplianceAgent,
            AgentType.REVIEWER: ReviewerAgent,
            AgentType.RECOMMENDER: RecommenderAgent,
            AgentType.SUGGESTER: SuggesterAgent,
        }
        print(f"[AgentFactory] Agent map configured for types: {list(self._agent_map.keys())}")


    def get_agent(self, agent_type: AgentType) -> BaseAgent:
        agent_class = self._agent_map.get(agent_type)
        if not agent_class:
            error_msg = f"Unknown or unmapped agent type requested: {agent_type.value if isinstance(agent_type, Enum) else agent_type}"
            print(f"[AgentFactory] ERROR: {error_msg}")
            raise ValueError(error_msg)
        print(f"[AgentFactory] Attempting to create instance of agent: {agent_type.value}")
        try:
            agent_instance = agent_class(
                config=self._config,
                vertex_client=self._vertex_client,
                zep_client=self._zep_client,
                qdrant_client=self._qdrant_client,
                storage_handler=self._storage_handler,
                internal_services_client=self._internal_services_client
            )
            print(f"[AgentFactory] Agent instance created successfully for type: {agent_type.value}")
            return agent_instance
        except TypeError as e_type:
             print(f"[AgentFactory] ERROR: Failed to instantiate agent {agent_type.value}. Constructor signature mismatch or missing dependency? Error: {e_type}")
             traceback.print_exc()
             raise TypeError(f"Failed to instantiate agent {agent_type.value} due to constructor mismatch: {e_type}") from e_type
        except Exception as e_inst:
             print(f"[AgentFactory] UNEXPECTED ERROR instantiating agent {agent_type.value}: {e_inst}")
             traceback.print_exc()
             raise RuntimeError(f"Unexpected error creating agent {agent_type.value}: {e_inst}") from e_inst

print("[AGENT_FACTORY] AgentFactory class defined (v2).")