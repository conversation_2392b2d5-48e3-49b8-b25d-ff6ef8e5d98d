# PLAP/plap-agents/agents/base_agent.py
print("[BASE_AGENT] Loading base_agent.py (v4 - config type name fix)")

from typing import Dict, Any, Optional, List, cast, Union, Type
from enum import Enum
import traceback
import json
import os

# --- CORRECCIÓN DE IMPORTACIÓN ---
# Importar la CLASE Settings y la INSTANCIA settings desde config.py
from config import settings, Settings # <--- CAMBIADO Config a Settings
from clients.vertex_ai_client import VertexAIClient
from clients.zep_client_wrapper import ZepClientWrapper
from clients.qdrant_client_wrapper import QdrantClientWrapper
from document_processing.storage_handler import StorageHandler
from document_processing.file_parser import parse_file_content_to_text
from clients.internal_services_client import InternalServicesClient
from schemas.agent_schemas import AgentType, AgentExecutionResponse
from utils import clean_text_for_llm, sanitize_llm_output, truncate_text, is_valid_uuid
# --- FIN DE CORRECCIÓN ---

from fastapi import UploadFile


class BaseAgent:
    """
    Clase base abstracta para todos los agentes de PLAP.
    """
    agent_type: AgentType

    def __init__(self,
                 config: Settings, # <--- CORREGIDO: Usar Settings para el tipo
                 vertex_client: Optional[VertexAIClient] = None,
                 zep_client: Optional[ZepClientWrapper] = None,
                 qdrant_client: Optional[QdrantClientWrapper] = None,
                 storage_handler: Optional[StorageHandler] = None,
                 internal_services_client: Optional[InternalServicesClient] = None,
                 ):
        self._config: Settings = config # <--- CORREGIDO: Usar Settings para el tipo del atributo
        self._vertex_client: Optional[VertexAIClient] = vertex_client
        self._zep_client: Optional[ZepClientWrapper] = zep_client
        self._qdrant_client: Optional[QdrantClientWrapper] = qdrant_client
        self._storage_handler: Optional[StorageHandler] = storage_handler
        self._internal_services_client: Optional[InternalServicesClient] = internal_services_client

        agent_name_for_log = "UnknownAgent"
        if hasattr(self, 'agent_type'):
            agent_name_for_log = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        else:
            print(f"[BaseAgent] CRITICAL WARNING: Subclass {self.__class__.__name__} did not set 'self.agent_type'.")

        print(f"[BaseAgent - {agent_name_for_log}] Initialized.")
        print(f"  - Config Type: {type(self._config)}") # Log para verificar tipo de config
        print(f"  - VertexAI Client: {'Available' if self._vertex_client and self._vertex_client.is_available else 'Not Available/Initialized'}")
        print(f"  - Zep Client: {'Available' if self._zep_client and self._zep_client.is_available else 'Not Available/Initialized'}")
        print(f"  - Qdrant Client: {'Available' if self._qdrant_client and self._qdrant_client.is_available else 'Not Available/Initialized'}")
        print(f"  - Storage Handler: {'Available' if self._storage_handler else 'Not Available/Initialized'}")
        print(f"  - Internal Services Client: {'Available' if self._internal_services_client else 'Not Available/Initialized'}")


    async def execute(self, user_id: str, session_id: Optional[str], input_data: Dict[str, Any]) -> AgentExecutionResponse:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        error_message = f"Agent type '{agent_name}' does not have a standard 'execute' method or it's not implemented."
        print(f"[BaseAgent] {error_message}")
        return AgentExecutionResponse(
            agent_used=self.agent_type, output_data={}, status="error_not_implemented", error_message=error_message
        )

    async def _call_llm(self, model_name: str, prompt_text: str, **kwargs: Any) -> str:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._vertex_client or not self._vertex_client.is_available:
            msg = f"[{agent_name}] Vertex AI client not available. Cannot call LLM '{model_name}'."
            print(msg); return f"[MOCK RESPONSE - LLM UNAVAILABLE: {model_name}]"
        cleaned_prompt = clean_text_for_llm(prompt_text)
        print(f"[{agent_name}] Calling LLM '{model_name}' (Prompt len: {len(cleaned_prompt)}, Start: '{cleaned_prompt[:100].replace(chr(10), ' ')}...')")
        response_text = await self._vertex_client.call_generative_model(model_name, cleaned_prompt, **kwargs)
        sanitized_response = sanitize_llm_output(response_text, strategy="basic")
        print(f"[{agent_name}] LLM Response (Sanitized, len: {len(sanitized_response)}, Start: '{sanitized_response[:100].replace(chr(10), ' ')}...')")
        return sanitized_response

    async def _get_embedding(self, text: str, model_name: Optional[str] = None) -> Optional[List[float]]:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._vertex_client or not self._vertex_client.is_available:
            print(f"[{agent_name}] Vertex AI client not available. Cannot generate embedding."); return None
        model_to_use = model_name or self._config.TEXT_EMBEDDING_MODEL_NAME
        cleaned_text = clean_text_for_llm(text)
        max_embedding_input_chars = 8000
        truncated_text_for_embedding = truncate_text(cleaned_text, max_embedding_input_chars)
        if len(cleaned_text) > max_embedding_input_chars: print(f"[{agent_name}] WARNING: Truncated text for embedding.")
        print(f"[{agent_name}] Generating embedding with model '{model_to_use}' for text (len: {len(truncated_text_for_embedding)}).")
        embeddings_list = await self._vertex_client.generate_embedding([truncated_text_for_embedding], model_to_use)
        if embeddings_list and embeddings_list[0] and len(embeddings_list[0]) == self._config.TEXT_EMBEDDING_DIMENSION:
            print(f"[{agent_name}] Embedding generated (dim: {len(embeddings_list[0])})."); return embeddings_list[0]
        else: print(f"[{agent_name}] Failed to generate valid embedding. Embedding list: {embeddings_list}"); return None

    async def _get_zep_memory_context(self, session_id: str, user_id: str) -> str:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._zep_client or not self._zep_client.is_available:
            print(f"[{agent_name}] Zep client not available for session '{session_id}'."); return ""
        return await self._zep_client.get_memory_context(session_id, user_id)

    async def _add_to_zep_memory(self, session_id: str, user_id: str, user_message: str, ai_message: str):
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._zep_client or not self._zep_client.is_available:
            print(f"[{agent_name}] Zep client not available for session '{session_id}'."); return
        await self._zep_client.add_messages_to_memory(session_id, user_id, user_message, ai_message)

    async def _search_qdrant(self, collection_name: str, query_vector: List[float], limit: int, user_id_filter: Optional[str] = None, **kwargs: Any) -> List[Dict[str, Any]]:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._qdrant_client or not self._qdrant_client.is_available:
            print(f"[{agent_name}] Qdrant client not available for collection '{collection_name}'."); return []
        return await self._qdrant_client.search_vectors(collection_name, query_vector, limit, user_id_filter=user_id_filter, **kwargs)

    async def _add_to_qdrant(self, collection_name: str, points: List[Dict[str, Any]]) -> bool:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._qdrant_client or not self._qdrant_client.is_available:
            print(f"[{agent_name}] Qdrant client not available for collection '{collection_name}'."); return False
        return await self._qdrant_client.add_vectors(collection_name, points)

    async def _parse_file_from_path(self, local_file_path: str) -> str:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        print(f"[{agent_name}] Parsing file from path: {local_file_path}")
        vision_client_instance = None
        if self._vertex_client and hasattr(self._vertex_client, '_vision_client_instance_for_parsing'): # Chequeo hipotético
            vision_client_instance = self._vertex_client._vision_client_instance_for_parsing # type: ignore
        return await parse_file_content_to_text(local_file_path, vision_client_instance=vision_client_instance)

    async def _save_file_locally(self, file_upload: UploadFile, user_id: str, session_id: Optional[str] = None) -> Optional[str]:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._storage_handler: print(f"[{agent_name}] StorageHandler not available."); return None
        return await self._storage_handler.save_uploaded_file_locally(file_upload, user_id, session_id)

    async def _upload_local_file_to_gcs(self, local_file_path: str, user_id: str) -> Optional[str]:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        if not self._storage_handler: print(f"[{agent_name}] StorageHandler not available."); return None
        return await self._storage_handler.upload_local_file_to_gcs(local_file_path, user_id)

    def _create_standard_error_response(self, error_message: str, model_used: Optional[str] = None) -> AgentExecutionResponse:
        agent_name = self.agent_type.value if isinstance(self.agent_type, Enum) else str(self.agent_type)
        return AgentExecutionResponse(
            agent_used=self.agent_type, output_data={}, model_used=model_used,
            status="error", error_message=f"{agent_name} execution failed: {error_message}"
        )

    def _create_standard_success_response(self, output_data: Dict[str, Any], model_used: Optional[str] = None, status: str = "success") -> AgentExecutionResponse:
        return AgentExecutionResponse(
            agent_used=self.agent_type, output_data=output_data, model_used=model_used,
            status=status, error_message=None
        )

print("[BASE_AGENT] BaseAgent class defined (v4 - config type name fix).")