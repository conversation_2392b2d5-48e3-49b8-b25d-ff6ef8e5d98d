# PLAP/plap-agents/agents/compliance_agent.py
print("[COMPLIANCE_AGENT] Loading compliance_agent.py (v2 - absolute imports)")

# --- CORRECCIÓN DE IMPORTACIÓN ---
from .base_agent import BaseAgent
# Importar schemas y config usando rutas absolutas desde /app
from schemas.agent_schemas import (
    AgentExecutionResponse,
    ComplianceAgentInput,
    # ComplianceAgentOutput, # El tipo de output_data se define como ComplianceOutputData
    ComplianceOutputData, # Este es el schema para la estructura de output_data
    ComplianceViolationDetail,
    AgentType
)
# from config import settings # No es necesario si se accede vía self._config de BaseAgent
# --- FIN DE CORRECCIÓN ---

from typing import Dict, Any, Optional, List
import traceback
import re # Para reglas basadas en regex


class ComplianceAgent(BaseAgent):
    """
    Agente responsable de verificar contenido contra un conjunto de reglas
    predefinidas (seguridad, ética, legales, etc.).
    Puede usar reglas programáticas (regex, keywords) y/o LLMs.
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.agent_type = AgentType.COMPLIANCE
        print(f"[ComplianceAgent] Initialized. Vertex Client for LLM rules: {'OK' if self._vertex_client and self._vertex_client.is_available else 'NA'}.")

        # Definir reglas de compliance.
        self._compliance_rules: Dict[str, Dict[str, Any]] = {
            "no_email_addresses": {
                "type": "regex",
                "regex_pattern": r"\b[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\.[A-Z|a-z]{2,}\b",
                "description": "Contiene una o más direcciones de email.",
                "severity": "medium"
            },
            "no_us_phone_numbers": {
                "type": "regex",
                "regex_pattern": r"\b(?:\+?1\s*(?:[.-]\s*)?)?(?:\(\s*\d{3}\s*\)|[2-9]\d{2})\s*(?:[.-]\s*)?\d{3}\s*(?:[.-]\s*)?\d{4}\b",
                "description": "Contiene un posible número de teléfono de EE. UU.",
                "severity": "medium"
            },
            "prohibited_keywords": {
                "type": "keywords",
                "keywords_list": ["palabra_prohibida_1", "termino_sensible_2"], # Ejemplo, poner keywords reales
                "description": "Contiene palabras clave prohibidas.",
                "severity": "high"
            },
            # Ejemplo de regla basada en LLM (placeholder)
            # "check_for_hate_speech_llm": {
            #     "type": "llm",
            #     "llm_prompt_template": "Analyze the following text for hate speech. Respond ONLY with 'Compliant' or 'NonCompliant: [Reason for non-compliance]'. Text: \"{text_to_check}\"",
            #     "llm_model": self._config.COMPLIANCE_AGENT_DEFAULT_MODEL or self._config.GEMINI_FLASH_MODEL_NAME,
            #     "description": "Evaluación de discurso de odio mediante LLM.",
            #     "severity": "critical"
            # }
        }
        print(f"[ComplianceAgent] Loaded {len(self._compliance_rules)} compliance rules.")


    async def execute(self, user_id: str, session_id: Optional[str], input_data: Dict[str, Any]) -> AgentExecutionResponse:
        agent_name = self.agent_type.value
        print(f"[{agent_name}] Executing for user_id: {user_id}, session_id: {session_id}")
        model_used_for_response: Optional[str] = None
        violations_found: List[ComplianceViolationDetail] = []
        overall_is_compliant = True

        try:
            try:
                agent_input = ComplianceAgentInput(**input_data)
                print(f"[{agent_name}] Input validated: {agent_input.model_dump_json(exclude_none=True)}")
            except Exception as e_val:
                print(f"[{agent_name}] Input validation error: {e_val}"); traceback.print_exc()
                return self._create_standard_error_response(f"Input validation failed: {str(e_val)[:200]}")

            text_to_check = agent_input.text_to_check
            rules_to_apply_filter = agent_input.rules_to_apply

            if not text_to_check or not isinstance(text_to_check, str):
                 print(f"[{agent_name}] No valid text_to_check provided.")
                 return self._create_standard_error_response("No text provided for compliance check.")

            for rule_name, rule_def in self._compliance_rules.items():
                if rules_to_apply_filter is not None and rule_name not in rules_to_apply_filter:
                    continue
                print(f"[{agent_name}] Applying rule: '{rule_name}' (type: {rule_def.get('type')})")
                rule_violated = False; match_details: Optional[str] = None
                rule_type = rule_def.get("type")

                if rule_type == "regex" and "regex_pattern" in rule_def:
                    try:
                        matches = re.findall(rule_def["regex_pattern"], text_to_check)
                        if matches: rule_violated = True; match_details = f"Found {len(matches)} match(es). Example: '{str(matches[0])[:50]}...'"
                    except Exception as e_regex: print(f"[{agent_name}] Error applying regex rule '{rule_name}': {e_regex}")
                elif rule_type == "keywords" and "keywords_list" in rule_def:
                    if isinstance(rule_def["keywords_list"], list):
                        found_kws = [kw for kw in rule_def["keywords_list"] if kw.lower() in text_to_check.lower()]
                        if found_kws: rule_violated = True; match_details = f"Found keywords: {', '.join(found_kws[:5])}" + ("..." if len(found_kws) > 5 else "")
                elif rule_type == "llm" and "llm_prompt_template" in rule_def and "llm_model" in rule_def:
                    if self._vertex_client and self._vertex_client.is_available:
                        model_to_use_llm = rule_def["llm_model"]
                        model_used_for_response = model_to_use_llm
                        llm_prompt = rule_def["llm_prompt_template"].format(text_to_check=text_to_check[:2000])
                        print(f"[{agent_name}] Calling LLM '{model_to_use_llm}' for rule '{rule_name}'.")
                        llm_response = await self._call_llm(model_to_use_llm, llm_prompt, temperature=0.2)
                        if "noncompliant" in llm_response.lower(): rule_violated = True; match_details = f"LLM Assessment: {llm_response[:200]}"
                        elif "[ERROR CALLING LLM" in llm_response or "[MOCK RESPONSE" in llm_response: print(f"[{agent_name}] LLM call for rule '{rule_name}' failed/mocked.")
                    else: print(f"[{agent_name}] LLM-based rule '{rule_name}' skipped: Vertex client unavailable.")

                if rule_violated:
                    overall_is_compliant = False
                    violations_found.append(ComplianceViolationDetail(
                        rule=rule_name, description=rule_def.get("description"),
                        match=match_details, severity=rule_def.get("severity")
                    ))
                    print(f"[{agent_name}] Rule '{rule_name}' VIOLATED. Details: {match_details}")
                else: print(f"[{agent_name}] Rule '{rule_name}' PASSED.")

            if session_id and self._zep_client and self._zep_client.is_available:
                zep_summary = f"Compliance check: {'Compliant' if overall_is_compliant else 'Non-Compliant'}. Violations: {len(violations_found)}"
                await self._add_to_zep_memory(session_id, user_id, f"Compliance check for content (len: {len(text_to_check)}).", zep_summary)

            output_agent_data = ComplianceOutputData(is_compliant=overall_is_compliant, violations=violations_found if violations_found else None).model_dump()
            final_status = "success" if overall_is_compliant else "non_compliant"
            return self._create_standard_success_response(output_data=output_agent_data, model_used=model_used_for_response, status=final_status)

        except Exception as e:
            print(f"[{agent_name}] CRITICAL ERROR during execution for user {user_id}: {e}"); traceback.print_exc()
            error_output = ComplianceOutputData(is_compliant=False, violations=[ComplianceViolationDetail(rule="internal_error", description=f"Agent execution failed: {e}", severity="critical")]).model_dump()
            return self._create_standard_error_response(f"Unexpected error in Compliance Agent: {str(e)}", model_used=model_used_for_response)

print("[COMPLIANCE_AGENT] ComplianceAgent class defined (v2).")