# PLAP/plap-agents/agents/extractor_agent.py
print("[EXTRACTOR_AGENT] Loading extractor_agent.py (v2 - absolute imports)")

# --- CORRECCIÓN DE IMPORTACIÓN ---
from .base_agent import BaseAgent
from schemas.agent_schemas import ( # Importar desde la raíz del servicio (/app)
    AgentExecutionResponse,
    ExtractorAgentInput,
    ExtractorAgentOutput,
    AgentType
)
# from config import settings # No es necesario si se accede vía self._config de BaseAgent
# --- FIN DE CORRECCIÓN ---

from typing import Dict, Any, Optional, List
import traceback
import json
import os


class ExtractorAgent(BaseAgent):
    """
    Agente responsable de extraer información estructurada de diversos tipos de input
    (texto directo, documentos PDF, DOCX, imágenes con OCR).
    Puede opcionalmente indexar el contenido procesado en Qdrant.
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.agent_type = AgentType.EXTRACTOR
        print(f"[ExtractorAgent] Initialized. Vertex: {'OK' if self._vertex_client and self._vertex_client.is_available else 'NA'}, "
              f"Storage: {'OK' if self._storage_handler else 'NA'}, Qdrant: {'OK' if self._qdrant_client and self._qdrant_client.is_available else 'NA'}")
        if not self._storage_handler:
            print("[ExtractorAgent] CRITICAL WARNING: StorageHandler not available. File processing will fail.")
        if not self._vertex_client or not self._vertex_client.is_available:
            print("[ExtractorAgent] WARNING: VertexAIClient not available. LLM-based extraction and embedding will be mocked or fail.")

    async def execute(self, user_id: str, session_id: Optional[str], input_data: Dict[str, Any]) -> AgentExecutionResponse:
        agent_name = self.agent_type.value
        print(f"[{agent_name}] Executing for user_id: {user_id}, session_id: {session_id}")
        model_used_for_response: Optional[str] = None
        qdrant_point_id_response: Optional[str] = None
        status_val = "success"
        error_msg: Optional[str] = None

        try:
            try:
                agent_input = ExtractorAgentInput(**input_data)
                print(f"[{agent_name}] Input validated: {agent_input.model_dump_json(exclude_none=True)}")
            except Exception as e_val:
                print(f"[{agent_name}] Input validation error: {e_val}"); traceback.print_exc()
                return self._create_standard_error_response(f"Input validation failed: {str(e_val)[:200]}")

            text_to_process: Optional[str] = None
            source_description = "direct text input"

            if agent_input.text_to_analyze:
                text_to_process = agent_input.text_to_analyze
                print(f"[{agent_name}] Analyzing text provided directly.")
            elif agent_input.file_id:
                source_description = f"file '{agent_input.file_id}'"
                if not self._storage_handler:
                    return self._create_standard_error_response("Storage handler not available to process file_id.")
                
                local_file_path = os.path.join(self._config.LOCAL_UPLOADS_DIR, user_id, agent_input.file_id)
                if not os.path.exists(local_file_path):
                    print(f"[{agent_name}] File not found locally: {local_file_path}")
                    return self._create_standard_error_response(f"File specified by file_id '{agent_input.file_id}' not found.")
                
                print(f"[{agent_name}] Parsing content from local file: {local_file_path}")
                text_to_process = await self._parse_file_from_path(local_file_path)
            else:
                return self._create_standard_error_response("No content provided for extraction.")

            if not text_to_process or "[Error:" in text_to_process or not text_to_process.strip():
                status_val = "success_no_content" if not "[Error:" in text_to_process else "error_parsing"
                error_msg = text_to_process if "[Error:" in text_to_process else "No parsable content found."
                print(f"[{agent_name}] {error_msg}")
                output_data = ExtractorAgentOutput(extracted_data={}, summary=error_msg, sensitive_content_detected=False).model_dump()
                return self._create_standard_success_response(output_data=output_data, status=status_val)

            filtered_text_for_llm = text_to_process # Placeholder PII
            pii_detected_flag = False # Placeholder PII

            model_to_use_for_extraction = agent_input.extraction_spec.get("model", self._config.EXTRACTOR_AGENT_DEFAULT_MODEL)
            model_used_for_response = model_to_use_for_extraction
            extraction_instruction = (
                f"Analyze the following content and extract information strictly according to this JSON specification: "
                f"\n\nSPECIFICATION:\n```json\n{json.dumps(agent_input.extraction_spec, indent=2)}\n```\n\n"
                f"Respond ONLY with a valid JSON object matching the specification. If some information is not found, use null for its value or omit the key if allowed by the spec."
            )
            llm_extraction_prompt = f"{extraction_instruction}\n\nCONTENT TO ANALYZE:\n---\n{filtered_text_for_llm}\n---\n\nEXTRACTED JSON:"
            print(f"[{agent_name}] Calling LLM '{model_to_use_for_extraction}' for extraction. Prompt length: {len(llm_extraction_prompt)}")
            extracted_json_str = await self._call_llm(model_to_use_for_extraction, llm_extraction_prompt)
            print(f"[{agent_name}] LLM extraction raw response (first 100 chars): {extracted_json_str[:100]}")

            extracted_data_dict: Dict[str, Any] = {}
            summary_text_from_llm: Optional[str] = None
            cleaned_json_str = extracted_json_str.strip()
            if cleaned_json_str.startswith("```json"):
                 cleaned_json_str = cleaned_json_str.split("```json", 1)[1].strip()
                 if cleaned_json_str.endswith("```"): cleaned_json_str = cleaned_json_str[:-3].strip()
            elif cleaned_json_str.startswith("```"):
                 cleaned_json_str = cleaned_json_str.split("```", 1)[1].strip()
                 if cleaned_json_str.endswith("```"): cleaned_json_str = cleaned_json_str[:-3].strip()
            try:
                 extracted_data_dict = json.loads(cleaned_json_str)
                 print(f"[{agent_name}] LLM extraction output successfully parsed as JSON.")
            except json.JSONDecodeError as e_json:
                 print(f"[{agent_name}] WARNING: LLM extraction output was not valid JSON: {e_json}. Raw: '{extracted_json_str[:200]}'")
                 status_val = "success_with_warning"; error_msg = f"LLM output was not valid JSON: {e_json}"
                 extracted_data_dict = {"raw_output_from_llm": extracted_json_str, "parse_error": str(e_json)}; summary_text_from_llm = extracted_json_str
            except Exception as e_parse:
                 print(f"[{agent_name}] Unexpected error parsing LLM extraction output: {e_parse}"); traceback.print_exc()
                 status_val = "success_with_warning"; error_msg = f"Unexpected error parsing LLM output: {e_parse}"
                 extracted_data_dict = {"raw_output_from_llm": extracted_json_str, "parse_error": str(e_parse)}; summary_text_from_llm = extracted_json_str

            if self._qdrant_client and self._qdrant_client.is_available and self._vertex_client and self._vertex_client.is_available:
                text_for_embedding = filtered_text_for_llm
                if text_for_embedding:
                    print(f"[{agent_name}] Generating embedding for extracted content...")
                    embedding_vector = await self._get_embedding(text_for_embedding)
                    if embedding_vector:
                        qdrant_point_id = str(uuid.uuid4())
                        qdrant_payload = {
                            "user_id": user_id, "session_id": session_id,
                            "source_file_id": agent_input.file_id if agent_input.file_id else "direct_text",
                            "original_filename": os.path.basename(agent_input.file_id) if agent_input.file_id else None,
                            "extracted_data_preview": {k: str(v)[:100] + "..." if isinstance(v,str) and len(str(v)) > 100 else v for k,v in extracted_data_dict.items()},
                            "text_snippet": text_to_process[:500] + "..." if text_to_process else None,
                            "created_at": datetime.now(timezone.utc).isoformat(),
                            "agent_version": self._config.SERVICE_VERSION,
                            "extraction_spec_info": agent_input.extraction_spec.get("description", "custom")
                        }
                        points_to_add = [{"id": qdrant_point_id, "vector": embedding_vector, "payload": qdrant_payload}]
                        index_success = await self._add_to_qdrant(self._config.QDRANT_DOCS_COLLECTION, points_to_add)
                        if index_success:
                            qdrant_point_id_response = qdrant_point_id
                            print(f"[{agent_name}] Content indexed in Qdrant '{self._config.QDRANT_DOCS_COLLECTION}' ID: {qdrant_point_id}")
                        else:
                            print(f"[{agent_name}] WARNING: Failed to index content in Qdrant.")
                            if status_val == "success": status_val = "success_with_warning"
                            error_msg = (error_msg + "; " if error_msg else "") + "Failed to index content."
                    else:
                        print(f"[{agent_name}] WARNING: Embedding generation failed, skipping Qdrant indexing.")
                        if status_val == "success": status_val = "success_with_warning"
                        error_msg = (error_msg + "; " if error_msg else "") + "Embedding generation failed for indexing."

            if session_id and self._zep_client and self._zep_client.is_available:
                zep_message_content = f"Extracted information from {source_description}. "
                if summary_text_from_llm: zep_message_content += f"Summary: {summary_text_from_llm[:200]}... "
                if qdrant_point_id_response: zep_message_content += f"Indexed as: {qdrant_point_id_response}."
                await self._add_to_zep_memory(session_id, user_id, f"Extraction request for {source_description}", zep_message_content)

            output_agent_data = ExtractorAgentOutput(
                extracted_data=extracted_data_dict, summary=summary_text_from_llm,
                sensitive_content_detected=pii_detected_flag, indexed_qdrant_point_id=qdrant_point_id_response
            ).model_dump()

            response = AgentExecutionResponse(
                agent_used=self.agent_type, output_data=output_agent_data, model_used=model_used_for_response,
                status=status_val, error_message=error_msg if error_msg else None
            )
            print(f"[{agent_name}] Response prepared. Status: {response.status}, Error: {response.error_message}")
            return response

        except Exception as e:
            print(f"[{agent_name}] CRITICAL ERROR during execution for user {user_id}: {e}"); traceback.print_exc()
            return self._create_standard_error_response(f"Unexpected error in Extractor Agent: {str(e)}", model_used=model_used_for_response)

print("[EXTRACTOR_AGENT] ExtractorAgent class defined (v2).")