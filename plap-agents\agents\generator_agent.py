# PLAP/plap-agents/agents/generator_agent.py
print("[GENERATOR_AGENT] Loading generator_agent.py (v2 - absolute imports)")

# --- CORRECCIÓN DE IMPORTACIÓN ---
# Importar BaseAgent desde el mismo paquete 'agents'
from .base_agent import BaseAgent
# Importar schemas y config usando rutas absolutas desde /app
from schemas.agent_schemas import (
    AgentExecutionResponse,
    GeneratorAgentInput,
    GeneratorAgentOutput,
    AgentType
)
# from config import settings # No es necesario importar 'settings' aquí si se accede vía self._config
# --- FIN DE CORRECCIÓN ---

from typing import Dict, Any, Optional
import traceback


class GeneratorAgent(BaseAgent):
    """
    Agente responsable de expandir ideas en prompts detallados o generar contenido,
    utilizando modelos LLM y memoria conversacional de Zep.
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.agent_type = AgentType.GENERATOR
        # El log de inicialización de BaseAgent ya indica la disponibilidad de clientes.
        print(f"[GeneratorAgent] Initialized.")


    async def execute(self, user_id: str, session_id: Optional[str], input_data: Dict[str, Any]) -> AgentExecutionResponse:
        agent_name = self.agent_type.value
        print(f"[{agent_name}] Executing for user_id: {user_id}, session_id: {session_id}")
        model_used_for_response: Optional[str] = None
        status_val = "success"
        error_msg: Optional[str] = None

        try:
            try:
                agent_input = GeneratorAgentInput(**input_data)
                print(f"[{agent_name}] Input validated: {agent_input.model_dump_json(exclude_none=True)}")
            except Exception as e_val:
                print(f"[{agent_name}] Input validation error: {e_val}"); traceback.print_exc()
                return self._create_standard_error_response(f"Input validation failed: {str(e_val)[:200]}")

            model_to_use = agent_input.target_llm_model or self._config.GENERATOR_AGENT_DEFAULT_MODEL
            model_used_for_response = model_to_use
            print(f"[{agent_name}] Using LLM model: {model_to_use}")

            zep_context_str = ""
            if session_id:
                zep_context_str = await self._get_zep_memory_context(session_id, user_id)
                if zep_context_str and not zep_context_str.startswith("[Error:"):
                    print(f"[{agent_name}] Zep context obtained (first 200 chars): {zep_context_str[:200]}...")
                elif zep_context_str.startswith("[Error:"):
                    print(f"[{agent_name}] Warning/error getting Zep context: {zep_context_str}"); zep_context_str = ""
                    status_val = "success_with_warning"; error_msg = (error_msg + "; " if error_msg else "") + "Zep context retrieval issue."
                else: print(f"[{agent_name}] No Zep context for session {session_id}.")
            else: print(f"[{agent_name}] No Zep session_id, skipping Zep context.")

            user_llm_instruction = (
                f"Based on the following idea: \"{agent_input.prompt_idea}\", "
                f"transform it into a detailed, effective, and high-quality prompt "
                f"suitable for the target LLM ({model_to_use}). "
                "The generated prompt should be clear, specific, and adhere to ethical AI principles. "
                "Avoid biases and harmful content. "
                f"If the idea seems ambiguous or incomplete, try to make reasonable assumptions or suggest areas for clarification in the generated prompt itself."
            )
            system_instruction_for_gemini = (
                "You are an expert AI Prompt Engineer and a helpful assistant. Your task is to expand a user's core idea into a "
                "comprehensive, well-structured, and optimized prompt. "
                "Consider the target LLM and the user's likely intent. "
                "Focus on clarity, providing sufficient context, actionable instructions, and specifying the desired output format if implied by the idea. "
                "If the idea is very short, try to elaborate significantly."
            )
            meta_prompt_parts = [system_instruction_for_gemini]
            if zep_context_str: meta_prompt_parts.append(f"PREVIOUS CONVERSATION CONTEXT (Use this to inform your generation, maintaining coherence if relevant):\n{zep_context_str}")
            meta_prompt_parts.append(f"USER'S PROMPT IDEA TO EXPAND:\n```\n{agent_input.prompt_idea}\n```")
            meta_prompt_parts.append(f"YOUR TASK:\n{user_llm_instruction}")
            meta_prompt_parts.append("\nDETAILED GENERATED PROMPT (Output only the prompt itself, ready to be used):")
            meta_prompt_for_gemini = "\n\n---\n\n".join(meta_prompt_parts)
            print(f"[{agent_name}] Calling LLM with meta-prompt (first 300 chars, newlines replaced for log): {meta_prompt_for_gemini[:300].replace(chr(10), ' ')}...")

            generated_text = await self._call_llm(
                model_name=model_to_use,
                prompt_text=meta_prompt_for_gemini,
                temperature=0.7,
                max_output_tokens=self._config.GENERATOR_MAX_OUTPUT_TOKENS
            )

            if "[MOCK RESPONSE" in generated_text or "[ERROR CALLING LLM" in generated_text or "[LLM Response: Empty" in generated_text or "[LLM blocked response" in generated_text:
                if status_val == "success": status_val = "success_with_warning"
                current_error = generated_text
                error_msg = (error_msg + "; " if error_msg else "") + current_error
                print(f"[{agent_name}] LLM call resulted in a non-ideal response: {generated_text}")
            else:
                print(f"[{agent_name}] LLM generation successful.")

            if session_id and self._zep_client and self._zep_client.is_available:
                if status_val.startswith("success"):
                    user_input_for_zep = f"Idea to expand: {agent_input.prompt_idea}"
                    ai_output_for_zep = generated_text
                    await self._add_to_zep_memory(session_id, user_id, user_input_for_zep, ai_output_for_zep)
                    print(f"[{agent_name}] Interaction saved to Zep session {session_id}.")
            elif session_id:
                print(f"[{agent_name}] Zep client not available or no session_id, skipping Zep memory update for session {session_id}.")

            output_payload = GeneratorAgentOutput(generated_prompt=generated_text).model_dump()
            response = AgentExecutionResponse(
                agent_used=self.agent_type, output_data=output_payload, model_used=model_used_for_response,
                status=status_val, error_message=error_msg if error_msg else None
            )
            print(f"[{agent_name}] Response prepared. Status: {response.status}, Error: {response.error_message}")
            return response

        except Exception as e:
            print(f"[{agent_name}] CRITICAL ERROR during execution for user {user_id}: {e}"); traceback.print_exc()
            return self._create_standard_error_response(f"Unexpected error in Generator Agent: {str(e)}", model_used=model_used_for_response)

print("[GENERATOR_AGENT] GeneratorAgent class defined (v2).")