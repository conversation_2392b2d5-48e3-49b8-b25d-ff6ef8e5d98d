# PLAP/plap-agents/agents/recommender_agent.py
print("[RECOMMENDER_AGENT] Loading recommender_agent.py (v2 - syntax fix & absolute imports)")

# --- CORRECCIÓN DE IMPORTACIÓN ---
from .base_agent import BaseAgent
# Importar schemas y config usando rutas absolutas desde /app
from schemas.agent_schemas import (
    AgentExecutionResponse,
    RecommenderAgentInput,
    RecommenderAgentOutput,
    RecommendationItem,
    AgentType,
    PromptSchemaShared # Importar para type hinting
)
# from config import settings # No es necesario si se accede vía self._config de BaseAgent
# --- FIN DE CORRECCIÓN ---

from typing import Dict, Any, Optional, List, cast
import traceback
import uuid # Para generar IDs si es necesario
from utils import clean_text_for_llm, truncate_text # Importar utilidades


class RecommenderAgent(BaseAgent):
    """
    Agente responsable de recomendar prompts, workflows o estrategias
    basadas en la idea/contexto del usuario.
    Utiliza embeddings para búsqueda semántica en Qdrant y contexto de Zep.
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.agent_type = AgentType.RECOMMENDER
        print(f"[RecommenderAgent] Initialized. Vertex: {'OK' if self._vertex_client and self._vertex_client.is_available else 'NA'}, "
              f"Qdrant: {'OK' if self._qdrant_client and self._qdrant_client.is_available else 'NA'}, "
              f"Zep: {'OK' if self._zep_client and self._zep_client.is_available else 'NA'}")
        if not (self._vertex_client and self._vertex_client.is_available):
            print("[RecommenderAgent] CRITICAL WARNING: Vertex AI client not available.")
        if not (self._qdrant_client and self._qdrant_client.is_available):
            print("[RecommenderAgent] CRITICAL WARNING: Qdrant client not available.")

    async def execute(self, user_id: str, session_id: Optional[str], input_data: Dict[str, Any]) -> AgentExecutionResponse:
        agent_name = self.agent_type.value
        print(f"[{agent_name}] Executing for user_id: {user_id}, session_id: {session_id}")
        model_used_for_embedding: Optional[str] = None
        model_used_for_justification: Optional[str] = None
        status_val = "success"
        error_msg: Optional[str] = None

        try:
            try:
                agent_input = RecommenderAgentInput(**input_data)
                print(f"[{agent_name}] Input validated: {agent_input.model_dump_json(exclude_none=True)}")
            except Exception as e_val:
                print(f"[{agent_name}] Input validation error: {e_val}"); traceback.print_exc()
                return self._create_standard_error_response(f"Input validation failed: {str(e_val)[:200]}")

            prompt_idea = agent_input.prompt_idea
            limit = agent_input.limit
            context_from_input = agent_input.context

            if not prompt_idea or not isinstance(prompt_idea, str):
                 return self._create_standard_error_response("No prompt idea provided for recommendations.")

            zep_context_str = ""
            if session_id and self._zep_client and self._zep_client.is_available:
                 zep_context_str = await self._get_zep_memory_context(session_id, user_id)
                 if zep_context_str and not zep_context_str.startswith("[Error:"):
                     print(f"[{agent_name}] Zep context obtained (first 100 chars): {zep_context_str[:100]}...")

            if not self._vertex_client or not self._vertex_client.is_available:
                return self._create_standard_error_response("Embedding service (Vertex AI) unavailable.")
            model_used_for_embedding = self._config.TEXT_EMBEDDING_MODEL_NAME
            text_for_embedding = prompt_idea
            if context_from_input: text_for_embedding += "\n\nContext: " + context_from_input
            if zep_context_str: text_for_embedding += "\n\nZep Context: " + zep_context_str[:500]
            query_vector = await self._get_embedding(text_for_embedding, model_used_for_embedding)
            if not query_vector:
                return self._create_standard_error_response("Failed to generate query embedding.", model_used=model_used_for_embedding)
            print(f"[{agent_name}] Query embedding generated (model: {model_used_for_embedding}).")

            if not self._qdrant_client or not self._qdrant_client.is_available:
                return self._create_standard_error_response("Vector database (Qdrant) unavailable.")

            recommendations: List[RecommendationItem] = []
            qdrant_hits: List[Dict[str, Any]] = []

            user_prompt_hits = await self._search_qdrant(
                self._config.QDRANT_PROMPTS_COLLECTION, query_vector, limit=max(1, int(limit * 0.5)), user_id_filter=user_id
            )
            if user_prompt_hits:
                print(f"[{agent_name}] Found {len(user_prompt_hits)} user prompts from Qdrant.")
                for hit in user_prompt_hits:
                    payload = hit.get("payload", {})
                    # --- CORRECCIÓN DE LÓGICA DE SNIPPET ---
                    snippet = payload.get("final_text", payload.get("prompt_text"))
                    if snippet and isinstance(snippet, str):
                        snippet = truncate_text(snippet, 200)
                    elif snippet: # Si no es string pero existe, convertir a string truncado
                        snippet = truncate_text(str(snippet), 200)
                    else: # Fallback si no hay texto
                        snippet = "No content preview available."
                    # --- FIN DE CORRECCIÓN ---
                    recommendations.append(RecommendationItem(
                        id=str(hit.get("id", payload.get("id", uuid.uuid4()))),
                        type="user_prompt", title=payload.get("name", payload.get("title", "User Prompt")),
                        content_snippet=snippet, score=hit.get("score"),
                        source=self._config.QDRANT_PROMPTS_COLLECTION
                    ))

            kb_hits = await self._search_qdrant(
                self._config.QDRANT_KB_COLLECTION, query_vector, limit=max(1, int(limit * 0.3))
            )
            if kb_hits:
                print(f"[{agent_name}] Found {len(kb_hits)} KB items from Qdrant.")
                for hit in kb_hits:
                    payload = hit.get("payload", {})
                    # --- CORRECCIÓN DE LÓGICA DE SNIPPET ---
                    snippet = payload.get("text_snippet", payload.get("text"))
                    if snippet and isinstance(snippet, str):
                        snippet = truncate_text(snippet, 200)
                    elif snippet:
                        snippet = truncate_text(str(snippet), 200)
                    else:
                        snippet = "No content preview available."
                    # --- FIN DE CORRECCIÓN ---
                    recommendations.append(RecommendationItem(
                        id=str(hit.get("id", payload.get("id", uuid.uuid4()))),
                        type="kb_document", title=payload.get("title", "KB Document"),
                        content_snippet=snippet, score=hit.get("score"),
                        source=self._config.QDRANT_KB_COLLECTION
                    ))

            recommendations.sort(key=lambda x: x.score if x.score is not None else -1.0, reverse=True)
            final_recommendations = recommendations[:limit]
            print(f"[{agent_name}] Total {len(final_recommendations)} recommendations after combining and limiting.")

            justification_text: Optional[str] = None
            if final_recommendations and self._vertex_client and self._vertex_client.is_available:
                rec_titles = [rec.title for rec in final_recommendations if rec.title]
                justification_prompt_text = (
                    f"The user had an idea: '{prompt_idea}'.\n"
                    f"Based on this, the following items were recommended: {'; '.join(rec_titles)}.\n"
                    f"Provide a brief, helpful justification for why these recommendations might be relevant or useful."
                )
                model_to_use_for_justification = self._config.RECOMMENDER_AGENT_DEFAULT_MODEL
                model_used_for_response = f"{model_used_for_embedding} (embedding), {model_to_use_for_justification} (justification)"
                print(f"[{agent_name}] Calling LLM '{model_to_use_for_justification}' for recommendation justification.")
                justification_text = await self._call_llm(model_to_use_for_justification, justification_prompt_text, temperature=0.5)
                print(f"[{agent_name}] LLM justification (first 100 chars): {justification_text[:100]}")
            else:
                model_used_for_response = model_used_for_embedding

            if session_id and self._zep_client and self._zep_client.is_available:
                zep_rec_summary = f"Provided {len(final_recommendations)} recommendations for idea: '{prompt_idea[:100]}...'"
                if justification_text: zep_rec_summary += f" Justification: {justification_text[:100]}..."
                await self._add_to_zep_memory(session_id, user_id, f"Recommendation request for: '{prompt_idea[:100]}...'", zep_rec_summary)

            output_agent_data = RecommenderAgentOutput(recommendations=final_recommendations, justification=justification_text).model_dump()
            return self._create_standard_success_response(output_data=output_agent_data, model_used=model_used_for_response)

        except Exception as e:
            print(f"[{agent_name}] CRITICAL ERROR during execution for user {user_id}: {e}"); traceback.print_exc()
            return self._create_standard_error_response(f"Unexpected error in Recommender Agent: {str(e)}", model_used=model_used_for_embedding or model_used_for_justification)

print("[RECOMMENDER_AGENT] RecommenderAgent class defined (v2).")