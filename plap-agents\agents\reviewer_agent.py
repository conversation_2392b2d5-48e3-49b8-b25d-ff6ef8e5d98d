# PLAP/plap-agents/agents/reviewer_agent.py
print("[REVIEWER_AGENT] Loading reviewer_agent.py (v2 - absolute imports)")

# --- CORRECCIÓN DE IMPORTACIÓN ---
from .base_agent import BaseAgent
# Importar schemas y config usando rutas absolutas desde /app
from schemas.agent_schemas import (
    AgentExecutionResponse,
    ReviewerAgentInput,
    ReviewerAgentOutput, # Schema para el output_data
    ReviewIssueDetail,
    AgentType
)
# from config import settings # No es necesario si se accede vía self._config de BaseAgent
# --- FIN DE CORRECCIÓN ---

from typing import Dict, Any, Optional, List
import traceback
import json # Para parsear la salida JSON esperada del LLM revisor


class ReviewerAgent(BaseAgent):
    """
    Agente responsable de evaluar prompts o respuestas de LLM según un conjunto de criterios,
    utilizando un LLM para realizar la revisión.
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.agent_type = AgentType.REVIEWER
        print(f"[ReviewerAgent] Initialized. Vertex Client: {'OK' if self._vertex_client and self._vertex_client.is_available else 'NA'}.")
        if not (self._vertex_client and self._vertex_client.is_available):
            print("[ReviewerAgent] CRITICAL WARNING: Vertex AI client not available. LLM-based review will fail or be mocked.")

        self._default_review_criteria = [
            "clarity", "completeness", "safety (harmful content, PII)",
            "bias (gender, race, etc.)", "ethical considerations (alignment with PLAP AI Constitution)",
            "effectiveness (for the intended task, if context provided)"
        ]
        print(f"[ReviewerAgent] Default review criteria set: {self._default_review_criteria}")


    async def execute(self, user_id: str, session_id: Optional[str], input_data: Dict[str, Any]) -> AgentExecutionResponse:
        agent_name = self.agent_type.value
        print(f"[{agent_name}] Executing for user_id: {user_id}, session_id: {session_id}")
        model_used_for_response: Optional[str] = None
        status_val = "success"
        error_msg: Optional[str] = None

        try:
            try:
                agent_input = ReviewerAgentInput(**input_data)
                print(f"[{agent_name}] Input validated: {agent_input.model_dump_json(exclude_none=True)}")
            except Exception as e_val:
                print(f"[{agent_name}] Input validation error: {e_val}"); traceback.print_exc()
                return self._create_standard_error_response(f"Input validation failed: {str(e_val)[:200]}")

            content_to_review = agent_input.content_to_review
            criteria_to_apply = agent_input.review_criteria or self._default_review_criteria
            additional_context = agent_input.context or ""

            if not content_to_review or not isinstance(content_to_review, str):
                 return self._create_standard_error_response("No content provided for review.")
            if not self._vertex_client or not self._vertex_client.is_available:
                 return self._create_standard_error_response("LLM service unavailable for review.", model_used=None)

            model_to_use_for_review = self._config.REVIEWER_AGENT_DEFAULT_MODEL
            model_used_for_response = model_to_use_for_review
            print(f"[{agent_name}] Using LLM model for review: {model_to_use_for_review}")

            system_instruction = (
                "You are an expert AI Quality and Safety Reviewer. Evaluate the provided text based on the "
                "specified criteria. For each criterion, provide a brief assessment and, if applicable, a severity level "
                "('low', 'medium', 'high', 'critical'). Provide an overall quality score (0.0 to 5.0, where 5.0 is excellent) "
                "and a general feedback summary. Respond ONLY with a single, valid JSON object with keys: "
                "'overall_score' (float), 'feedback' (string), and 'issues' (a list of objects, where each object has "
                "'criterion' (string), 'assessment' (string), 'severity' (string, optional), 'suggestion' (string, optional))."
            )
            criteria_string = "\n - ".join(criteria_to_apply)
            review_prompt = (f"{system_instruction}\n\nREVIEW CRITERIA:\n - {criteria_string}\n\n")
            if additional_context: review_prompt += f"ADDITIONAL CONTEXT FOR REVIEW:\n{additional_context}\n\n"
            review_prompt += f"TEXT TO REVIEW:\n---\n{content_to_review}\n---\n\nJSON ASSESSMENT:"
            print(f"[{agent_name}] Calling LLM '{model_to_use_for_review}' for review. Prompt length: {len(review_prompt)}")
            llm_response_text = await self._call_llm(model_to_use_for_review, review_prompt, temperature=0.3)
            print(f"[{agent_name}] LLM review raw response (first 200 chars): {llm_response_text[:200]}")

            overall_score: Optional[float] = None
            feedback_summary: Optional[str] = llm_response_text
            issues_list: List[ReviewIssueDetail] = []

            cleaned_json_str = llm_response_text.strip()
            if cleaned_json_str.startswith("```json"):
                 cleaned_json_str = cleaned_json_str.split("```json", 1)[1].strip()
                 if cleaned_json_str.endswith("```"): cleaned_json_str = cleaned_json_str[:-3].strip()
            elif cleaned_json_str.startswith("```"):
                 cleaned_json_str = cleaned_json_str.split("```", 1)[1].strip()
                 if cleaned_json_str.endswith("```"): cleaned_json_str = cleaned_json_str[:-3].strip()

            try:
                 parsed_review_data = json.loads(cleaned_json_str)
                 print(f"[{agent_name}] LLM review response successfully parsed as JSON.")
                 overall_score_raw = parsed_review_data.get("overall_score")
                 if isinstance(overall_score_raw, (int, float)): overall_score = float(overall_score_raw)
                 feedback_summary = parsed_review_data.get("feedback")
                 if not isinstance(feedback_summary, str): feedback_summary = str(feedback_summary) if feedback_summary is not None else None
                 raw_issues = parsed_review_data.get("issues")
                 if isinstance(raw_issues, list):
                    for issue_item in raw_issues:
                        if isinstance(issue_item, dict):
                            try: issues_list.append(ReviewIssueDetail.model_validate(issue_item))
                            except Exception as e_issue: print(f"[{agent_name}] WARNING: Could not parse issue item: {issue_item}. Error: {e_issue}")
                        else: print(f"[{agent_name}] WARNING: Issue item is not a dict: {issue_item}")
            except json.JSONDecodeError as e_json:
                 print(f"[{agent_name}] WARNING: LLM review output not valid JSON: {e_json}. Raw: '{llm_response_text[:200]}'")
                 status_val = "success_with_warning"; error_msg = f"LLM review output not valid JSON: {e_json}"
                 feedback_summary = f"LLM Review (raw output due to parsing error):\n{llm_response_text}"
                 issues_list.append(ReviewIssueDetail(criterion="parsing_error", finding=f"LLM response was not valid JSON: {e_json}"))
            except Exception as e_parse:
                 print(f"[{agent_name}] Unexpected error parsing LLM review output: {e_parse}"); traceback.print_exc()
                 status_val = "success_with_warning"; error_msg = f"Unexpected error parsing LLM review output: {e_parse}"
                 feedback_summary = f"LLM Review (raw output due to parsing error):\n{llm_response_text}"
                 issues_list.append(ReviewIssueDetail(criterion="parsing_error", finding=f"Unexpected parsing error: {e_parse}"))

            if "[MOCK RESPONSE" in llm_response_text or "[ERROR CALLING LLM" in llm_response_text or "[LLM blocked response" in llm_response_text:
                if status_val == "success": status_val = "success_with_warning"
                current_error = llm_response_text
                error_msg = (error_msg + "; " if error_msg else "") + current_error
                if not feedback_summary: feedback_summary = llm_response_text

            if session_id and self._zep_client and self._zep_client.is_available:
                zep_review_summary = f"Review of content. Score: {overall_score}. Issues: {len(issues_list)}. Feedback: {(feedback_summary or '')[:100]}..."
                await self._add_to_zep_memory(session_id, user_id, f"Review request for content: {content_to_review[:100]}...", zep_review_summary)

            output_agent_data = ReviewerAgentOutput(
                overall_score=overall_score, feedback=feedback_summary,
                issues=issues_list if issues_list else None
            ).model_dump()

            response = AgentExecutionResponse(
                agent_used=self.agent_type, output_data=output_agent_data, model_used=model_used_for_response,
                status=status_val, error_message=error_msg if error_msg else None
            )
            print(f"[{agent_name}] Response prepared. Status: {response.status}, Error: {response.error_message}")
            return response

        except Exception as e:
            print(f"[{agent_name}] CRITICAL ERROR during execution for user {user_id}: {e}"); traceback.print_exc()
            return self._create_standard_error_response(f"Unexpected error in Reviewer Agent: {str(e)}", model_used=model_used_for_response)

print("[REVIEWER_AGENT] ReviewerAgent class defined (v2).")