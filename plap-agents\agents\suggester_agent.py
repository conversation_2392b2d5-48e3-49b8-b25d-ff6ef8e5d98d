# PLAP/plap-agents/agents/suggester_agent.py
print("[SUGGESTER_AGENT] Loading suggester_agent.py (v2 - absolute imports & syntax check)")

# --- CORRECCIÓN DE IMPORTACIÓN ---
from .base_agent import BaseAgent
# Importar schemas y config usando rutas absolutas desde /app
from schemas.agent_schemas import (
    SuggestionRequest, # Input directo para el endpoint /process/suggest
    SuggestionResponse, # Output directo para el endpoint /process/suggest
    AgentType
)
# from config import settings # No es necesario si se accede vía self._config de BaseAgent
# --- FIN DE CORRECCIÓN ---

from typing import Dict, Any, Optional, List
import traceback
import asyncio # Para ejecutar múltiples búsquedas en paralelo
# Importar utilidades
from utils import clean_text_for_llm, truncate_text


class SuggesterAgent(BaseAgent):
    """
    Agente responsable de generar sugerencias en tiempo real mientras el usuario escribe un prompt.
    Optimizado para baja latencia.
    """

    def __init__(self, **kwargs: Any):
        super().__init__(**kwargs)
        self.agent_type = AgentType.SUGGESTER
        print(f"[SuggesterAgent] Initialized. Vertex: {'OK' if self._vertex_client and self._vertex_client.is_available else 'NA'}, "
              f"Qdrant: {'OK' if self._qdrant_client and self._qdrant_client.is_available else 'NA'}, "
              f"Zep: {'OK' if self._zep_client and self._zep_client.is_available else 'NA'}")
        if not (self._vertex_client and self._vertex_client.is_available):
            print("[SuggesterAgent] WARNING: Vertex AI client not available.")
        if not (self._qdrant_client and self._qdrant_client.is_available):
            print("[SuggesterAgent] WARNING: Qdrant client not available.")
        if not (self._zep_client and self._zep_client.is_available):
            print("[SuggesterAgent] WARNING: Zep client not available.")

    async def generate_suggestions(
        self,
        user_id: str,
        session_id: Optional[str],
        current_prompt_text: str,
        context_text: Optional[str] = None
    ) -> List[SuggestionResponse]:
        agent_name = self.agent_type.value
        print(f"[{agent_name}] Generating suggestions for user_id: {user_id}, session_id: {session_id}, current_text_len: {len(current_prompt_text)}")
        model_used_for_llm_suggestion: Optional[str] = None
        suggestions: List[SuggestionResponse] = []

        # Acceder a la configuración a través de self._config
        min_text_len = getattr(self._config, 'SUGGESTER_MIN_TEXT_LENGTH_THRESHOLD', 3)
        qdrant_prefix = getattr(self._config, 'SUGGESTER_QDRANT_PREFIX', "Consider: ")
        max_qdrant_suggs = getattr(self._config, 'SUGGESTER_MAX_QDRANT_SUGGESTIONS', 2)
        max_total_suggs = getattr(self._config, 'SUGGESTER_MAX_TOTAL_SUGGESTIONS', 3)

        if not current_prompt_text or len(current_prompt_text.strip()) < min_text_len:
            print(f"[{agent_name}] Current text too short ('{current_prompt_text}'), skipping suggestions.")
            return []

        try: # Bloque try principal
            zep_context_str = ""
            qdrant_search_results: List[Dict[str, Any]] = []
            context_tasks = []

            if session_id and self._zep_client and self._zep_client.is_available:
                context_tasks.append(self._get_zep_memory_context(session_id, user_id))
            else:
                context_tasks.append(asyncio.sleep(0, result="")) # Placeholder para Zep si no disponible

            if self._qdrant_client and self._qdrant_client.is_available and self._vertex_client and self._vertex_client.is_available:
                async def _fetch_qdrant_context():
                    embedding_model = self._config.TEXT_EMBEDDING_MODEL_NAME
                    query_vector = await self._get_embedding(current_prompt_text, embedding_model)
                    if not query_vector: return []
                    user_docs_hits = await self._search_qdrant(self._config.QDRANT_DOCS_COLLECTION, query_vector, limit=2, user_id_filter=user_id)
                    kb_hits = await self._search_qdrant(self._config.QDRANT_KB_COLLECTION, query_vector, limit=1)
                    return user_docs_hits + kb_hits
                context_tasks.append(_fetch_qdrant_context())
            else:
                context_tasks.append(asyncio.sleep(0, result=[])) # Placeholder para Qdrant

            try:
                print(f"[{agent_name}] Fetching context from Zep and Qdrant concurrently...")
                results = await asyncio.gather(*context_tasks, return_exceptions=True)
                if isinstance(results[0], str): zep_context_str = results[0]
                elif isinstance(results[0], Exception): print(f"[{agent_name}] Error fetching Zep context: {results[0]}")
                if isinstance(results[1], list): qdrant_search_results = results[1]
                elif isinstance(results[1], Exception): print(f"[{agent_name}] Error fetching Qdrant context: {results[1]}")
                if zep_context_str: print(f"[{agent_name}] Zep context obtained (len: {len(zep_context_str)}).")
                if qdrant_search_results: print(f"[{agent_name}] Qdrant search returned {len(qdrant_search_results)} results.")
            except Exception as e_context:
                print(f"[{agent_name}] Error during concurrent context fetching: {e_context}"); traceback.print_exc()

            if qdrant_search_results:
                for hit in qdrant_search_results:
                    payload = hit.get("payload", {})
                    suggestion_text = payload.get("text_snippet", payload.get("final_text", payload.get("title")))
                    if suggestion_text and isinstance(suggestion_text, str) and len(suggestion_text) > 5:
                         clean_suggestion = qdrant_prefix + " " + truncate_text(clean_text_for_llm(suggestion_text), max_length=100)
                         suggestions.append(SuggestionResponse(
                             suggestion=clean_suggestion, agent_used=self.agent_type,
                             model_used=f"qdrant:{hit.get('payload',{}).get('source_collection', 'unknown')}"
                         ))
                    if len(suggestions) >= max_qdrant_suggs: break

            if len(suggestions) < max_total_suggs and self._vertex_client and self._vertex_client.is_available:
                model_to_use_for_llm_suggestion = self._config.SUGGESTER_AGENT_DEFAULT_MODEL
                model_used_for_llm_suggestion = model_to_use_for_llm_suggestion
                llm_prompt_context = "Relevant context:\n"
                if context_text: llm_prompt_context += f"- Provided Context: {context_text[:200]}...\n"
                if zep_context_str: llm_prompt_context += f"- Conversation History (Zep): {zep_context_str[:300]}...\n"
                if qdrant_search_results:
                    qdrant_snippets = [truncate_text(str(hit.get("payload",{}).get("text_snippet","")),50) for hit in qdrant_search_results[:2]]
                    llm_prompt_context += f"- Related Snippets (Qdrant): {'; '.join(qdrant_snippets)}\n"
                system_instruction = (
                    "You are a super-fast AI assistant providing concise suggestions for completing or improving a prompt. "
                    "Given the user's current text and some context, offer 1-2 highly relevant and short suggestions (max 15 words each). "
                    "Output ONLY the suggestions, one per line. Do NOT add any other text."
                )
                llm_suggester_prompt = f"{system_instruction}\n\n{llm_prompt_context if llm_prompt_context != 'Relevant context:\n' else ''}User is writing: \"{current_prompt_text}\"\n\nSuggestions:"
                print(f"[{agent_name}] Calling LLM '{model_to_use_for_llm_suggestion}' for suggestions. Prompt length: {len(llm_suggester_prompt)}")
                llm_response_text = await self._call_llm(model_to_use_for_llm_suggestion, llm_suggester_prompt, temperature=0.4, max_output_tokens=60)
                print(f"[{agent_name}] LLM suggestions raw response: {llm_response_text}")
                if llm_response_text and not ("[MOCK" in llm_response_text or "[ERROR" in llm_response_text):
                    llm_suggestions = [s.strip() for s in llm_response_text.strip().split('\n') if s.strip() and len(s.strip()) > 3]
                    for sugg_text in llm_suggestions:
                        if len(suggestions) < max_total_suggs:
                            suggestions.append(SuggestionResponse(suggestion=truncate_text(sugg_text, 80), agent_used=self.agent_type, model_used=model_to_use_for_llm_suggestion))
                        else: break

            if not suggestions: # Fallback if no suggestions generated
                print(f"[{agent_name}] No suggestions generated from Qdrant or LLM, providing placeholder.")
                suggestions.append(SuggestionResponse(suggestion="Type to get more suggestions...", agent_used=self.agent_type, model_used="heuristic_placeholder"))


            final_suggestions = suggestions[:max_total_suggs]
            print(f"[{agent_name}] Returning {len(final_suggestions)} suggestions.")
            return final_suggestions

        except Exception as e: # Este es el except que estaba causando el SyntaxError
            print(f"[{agent_name}] CRITICAL ERROR during suggestion generation for user {user_id}: {e}")
            traceback.print_exc()
            # Devolver una lista con una única sugerencia de error
            return [SuggestionResponse(
                suggestion=f"[Error generating suggestions: {str(e)[:100]}]",
                agent_used=self.agent_type,
                model_used=model_used_for_llm_suggestion, # Puede ser None si el error fue antes
                status="error" # Asegurar que el schema SuggestionResponse tenga 'status'
            )]

print("[SUGGESTER_AGENT] SuggesterAgent class defined (v2).")