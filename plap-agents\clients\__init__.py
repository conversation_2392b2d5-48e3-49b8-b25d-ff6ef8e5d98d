# PLAP/plap-agents/clients/__init__.py
print("[CLIENTS/__INIT__] Initializing 'clients' package...")

# Opcionalmente, importar los clientes para facilitar el acceso si se usan directamente
# (aunque la AgentFactory o BaseAgent los inyectarán usualmente).
# from .vertex_ai_client import VertexAIClient
# from .zep_client_wrapper import ZepClientWrapper
# from .qdrant_client_wrapper import QdrantClientWrapper
# from .internal_services_client import InternalServicesClient

# Por ahora, puede estar vacío o solo con el print.