# PLAP/plap-agents/clients/internal_services_client.py
print("[INTERNAL_SERVICES_CLIENT] Loading internal_services_client.py (v2 - absolute imports)")

import httpx
from typing import Dict, Any, Optional, List # Asegurar que List está aquí si se usa en type hints
import os
import traceback

# --- CORRECCIÓN DE IMPORTACIÓN ---
# Importar la instancia 'settings' desde config.py en /app (raíz del servicio)
from config import settings
# --- FIN DE CORRECCIÓN ---


class InternalServicesClient:
    """
    Cliente wrapper para llamar a otros servicios internos de PLAP.
    Utiliza un cliente httpx.AsyncClient para realizar las llamadas.
    """

    def __init__(self,
                 user_service_url: Optional[str] = None, # Hacerlos opcionales por si no todos están configurados
                 prompt_library_service_url: Optional[str] = None,
                 # Añadir otras URLs de servicio si los agentes necesitan llamarlas
                 ):
        """
        Inicializa el cliente con las URLs de los servicios internos.
        Estas URLs deben ser las direcciones internas accesibles desde la red Docker.
        Toma las URLs de `settings` si no se pasan explícitamente.
        """
        self._user_service_url = user_service_url or settings.USER_SERVICE_URL
        self._prompt_library_service_url = prompt_library_service_url or settings.PROMPT_LIBRARY_SERVICE_URL
        # self._orchestrator_service_url = settings.ORCHESTRATOR_SERVICE_URL # Agentes usualmente no llaman al orchestrator

        # Crear un cliente httpx asíncrono reutilizable para las llamadas
        # Definir un timeout por defecto, puede ser sobrescrito por llamada si es necesario
        self._httpx_client = httpx.AsyncClient(timeout=15.0)

        print("[InternalServicesClient] Initialized.")
        print(f"  User Service URL configured: {self._user_service_url if self._user_service_url else 'Not Configured'}")
        print(f"  Prompt Library Service URL configured: {self._prompt_library_service_url if self._prompt_library_service_url else 'Not Configured'}")


    async def __aenter__(self):
        """Permitir usar con 'async with' para la gestión del ciclo de vida del cliente httpx."""
        return self

    async def __aexit__(self, exc_type, exc, tb):
        """Cierra el cliente httpx al salir del bloque 'async with'."""
        await self.close()


    async def close(self):
        """Cierra el cliente httpx subyacente. Debe llamarse en el shutdown del servicio."""
        if self._httpx_client and not self._httpx_client.is_closed:
            print("[InternalServicesClient] Closing internal httpx client...")
            try:
                await self._httpx_client.aclose()
                print("[InternalServicesClient] Internal httpx client closed.")
            except Exception as e:
                print(f"[InternalServicesClient] Error closing httpx client: {e}")
                traceback.print_exc()


    async def _call_service(
        self,
        service_base_url: Optional[str],
        method: str,
        path: str,
        user_id: str, # User ID es mandatorio para X-User-ID
        json_payload: Optional[Dict[str, Any]] = None,
        query_params: Optional[Dict[str, Any]] = None,
        extra_headers: Optional[Dict[str, str]] = None
    ) -> httpx.Response:
        """Realiza una llamada genérica a un servicio interno."""
        if not service_base_url:
            error_msg = "[InternalServicesClient] ERROR: Base URL for the target service is not configured."
            print(error_msg)
            # Crear un objeto Request simulado para la respuesta de error
            dummy_request = httpx.Request(method, "http://dummy-service/error")
            return httpx.Response(status_code=503, text=error_msg, request=dummy_request)

        headers = {"Content-Type": "application/json", "X-User-ID": user_id}
        if extra_headers: headers.update(extra_headers)

        _base = service_base_url.rstrip('/')
        _path_segment = path.lstrip('/')
        full_url = f"{_base}/{_path_segment}"

        print(f"[InternalServicesClient] Calling {method} {full_url} for user {user_id}")

        try:
            response = await self._httpx_client.request(
                method=method, url=full_url, headers=headers,
                json=json_payload, params=query_params
            )
            print(f"[InternalServicesClient] Response from {full_url}: {response.status_code}")
            return response
        except httpx.TimeoutException as e:
            print(f"[InternalServicesClient] Timeout calling {full_url}: {e}")
            return httpx.Response(status_code=504, text=f"Timeout calling service: {e}", request=httpx.Request(method, full_url))
        except httpx.ConnectError as e:
            print(f"[InternalServicesClient] ConnectError calling {full_url}: {e}")
            return httpx.Response(status_code=503, text=f"Service unavailable: {e}", request=httpx.Request(method, full_url))
        except Exception as e:
            print(f"[InternalServicesClient] Unexpected error calling {full_url}: {e}"); traceback.print_exc()
            return httpx.Response(status_code=500, text=f"Internal error calling service: {e}", request=httpx.Request(method, full_url))


    async def get_user_profile_from_user_service(self, user_id: str) -> Optional[Dict[str, Any]]:
        """Llama a User Service para obtener el perfil de usuario."""
        if not self._user_service_url:
            print("[InternalServicesClient] User Service URL not configured. Cannot get user profile.")
            return None
        path = f"/internal/users/{user_id}/profile" # Ruta interna del user-service
        response = await self._call_service(self._user_service_url, "GET", path, user_id)
        if response.status_code == 200:
            try: return response.json()
            except Exception as e: print(f"[InternalServicesClient] ERROR parsing JSON UserSvc profile: {e}. Resp: {response.text[:200]}"); return None
        else: print(f"[InternalServicesClient] ERROR UserSvc profile {user_id}: {response.status_code} - {response.text[:200]}"); return None

    async def get_prompt_from_library_service(self, prompt_id: str, user_id: str) -> Optional[Dict[str, Any]]:
        """Llama a Prompt Library Service para obtener un prompt específico."""
        if not self._prompt_library_service_url:
            print("[InternalServicesClient] Prompt Library Service URL not configured. Cannot get prompt."); return None
        path = f"/internal/prompts/{prompt_id}" # Ruta interna del library-service
        response = await self._call_service(self._prompt_library_service_url, "GET", path, user_id)
        if response.status_code == 200:
            try: return response.json()
            except Exception as e: print(f"[InternalServicesClient] ERROR parsing JSON PromptLib get_prompt: {e}. Resp: {response.text[:200]}"); return None
        else: print(f"[InternalServicesClient] ERROR PromptLib get_prompt {prompt_id} (user {user_id}): {response.status_code} - {response.text[:200]}"); return None

print("[INTERNAL_SERVICES_CLIENT] InternalServicesClient class defined (v2).")