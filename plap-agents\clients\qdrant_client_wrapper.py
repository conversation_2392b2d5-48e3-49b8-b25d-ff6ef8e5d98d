# PLAP/plap-agents/clients/qdrant_client_wrapper.py
print("[QDRANT_CLIENT_WRAPPER] Loading qdrant_client_wrapper.py (v2 - typing import fix)")

import os
# --- CORRECCIÓN DE IMPORTACIÓN ---
from typing import Optional, List, Dict, Any, cast, Union # Asegurar que Union y otros tipos de typing están aquí
# --- FIN DE CORRECCIÓN ---
import traceback
import uuid # Qdrant usa UUIDs o strings como IDs de puntos

# Importar la instancia de configuración
from config import settings

# --- Intento de Importación del SDK de Qdrant ---
QDRANT_SDK_AVAILABLE = False
QdrantClient = None
# AsyncQdrantClient = None
qdrant_models = None

try:
    print("[QDRANT_CLIENT_WRAPPER] Attempting to import Qdrant SDK...")
    from qdrant_client import QdrantClient
    from qdrant_client.http import models as imported_qdrant_models
    qdrant_models = imported_qdrant_models
    QDRANT_SDK_AVAILABLE = True
    print("[QDRANT_CLIENT_WRAPPER] Qdrant SDK (QdrantClient, models) imported successfully.")
except ImportError as e_imp:
    print(f"[QDRANT_CLIENT_WRAPPER] CRITICAL WARNING: Qdrant SDK NOT FOUND (ImportError): {e_imp}. Qdrant functionality will be disabled.")
    QDRANT_SDK_AVAILABLE = False
except Exception as e_gen:
    print(f"[QDRANT_CLIENT_WRAPPER] CRITICAL WARNING: UNEXPECTED ERROR occurred while attempting to import Qdrant SDK: {e_gen}. Qdrant functionality will be disabled.")
    traceback.print_exc()
    QDRANT_SDK_AVAILABLE = False


class QdrantClientWrapper:
    """Cliente wrapper para interactuar con la API de Qdrant."""

    def __init__(self, url: str, api_key: Optional[str] = None, prefer_grpc: bool = False, grpc_port: int = 6334):
        self._qdrant_client: Optional[QdrantClient] = None # type: ignore
        self._is_initialized = False

        if QDRANT_SDK_AVAILABLE and QdrantClient is not None:
            try:
                parsed_url_for_grpc = url # Default para HTTP
                if prefer_grpc:
                    if url.startswith("http://"): parsed_url_for_grpc = url.split("http://")[1].split(":")[0]
                    elif url.startswith("https://"): parsed_url_for_grpc = url.split("https://")[1].split(":")[0]
                    print(f"[QdrantClientWrapper] Attempting gRPC connection to host '{parsed_url_for_grpc}' on port {grpc_port}")
                    self._qdrant_client = QdrantClient(host=parsed_url_for_grpc, grpc_port=grpc_port, api_key=api_key)
                else:
                    self._qdrant_client = QdrantClient(url=url, api_key=api_key)
                self._is_initialized = True
                print(f"[QdrantClientWrapper] Qdrant client initialized. Mode: {'gRPC' if prefer_grpc else 'HTTP'}. Target: {url if not prefer_grpc else f'{parsed_url_for_grpc}:{grpc_port}'}")
            except Exception as e:
                print(f"[QdrantClientWrapper] ERROR initializing Qdrant client for URL {url}: {e}"); traceback.print_exc()
                self._qdrant_client = None; self._is_initialized = False
        else:
            print("[QdrantClientWrapper] WARNING: Qdrant SDK not available or QdrantClient not imported. Qdrant client will not be initialized.")

    @property
    def is_available(self) -> bool:
        return self._is_initialized and QDRANT_SDK_AVAILABLE and qdrant_models is not None

    def ensure_collection_exists(self, collection_name: str, vector_dim: int, distance_metric: str = "Cosine"):
        if not self.is_available or not self._qdrant_client or not qdrant_models:
            print(f"[QdrantClientWrapper] ensure_collection_exists: Client/models not available for '{collection_name}'.")
            return False
        try:
            print(f"[QdrantClientWrapper] Checking for collection '{collection_name}'...")
            self._qdrant_client.get_collection(collection_name=collection_name)
            print(f"[QdrantClientWrapper] Collection '{collection_name}' already exists.")
            return True
        except Exception as e_get:
            if hasattr(e_get, 'status_code') and e_get.status_code == 404 or "not found" in str(e_get).lower(): # type: ignore
                print(f"[QdrantClientWrapper] Collection '{collection_name}' not found. Creating...")
            else:
                print(f"[QdrantClientWrapper] Error checking collection '{collection_name}': {e_get}. Not creating."); traceback.print_exc(); return False
            try:
                qdrant_distance = qdrant_models.Distance.COSINE
                if distance_metric.lower() == "euclid": qdrant_distance = qdrant_models.Distance.EUCLID
                elif distance_metric.lower() == "dot": qdrant_distance = qdrant_models.Distance.DOT
                self._qdrant_client.recreate_collection(
                    collection_name=collection_name,
                    vectors_config=qdrant_models.VectorParams(size=vector_dim, distance=qdrant_distance)
                )
                print(f"[QdrantClientWrapper] Collection '{collection_name}' created successfully.")
                return True
            except Exception as e_create:
                print(f"[QdrantClientWrapper] ERROR creating collection '{collection_name}': {e_create}"); traceback.print_exc(); return False

    async def add_vectors(self, collection_name: str, points: List[Dict[str, Any]]) -> bool:
        if not self.is_available or not self._qdrant_client or not qdrant_models:
             print(f"[QdrantClientWrapper] add_vectors: Client/models not available for '{collection_name}'."); return False
        try:
            qdrant_points = [
                qdrant_models.PointStruct(id=p.get("id", str(uuid.uuid4())), vector=cast(List[float], p["vector"]), payload=p.get("payload", {}))
                for p in points if "vector" in p and p["vector"] is not None
            ]
            if not qdrant_points: print(f"[QdrantClientWrapper] add_vectors: No valid points for '{collection_name}'."); return True
            print(f"[QdrantClientWrapper] Adding/updating {len(qdrant_points)} points to '{collection_name}'.")
            from fastapi.concurrency import run_in_threadpool
            await run_in_threadpool(self._qdrant_client.upsert, collection_name=collection_name, wait=True, points=qdrant_points)
            print(f"[QdrantClientWrapper] Successfully added/updated {len(qdrant_points)} points to '{collection_name}'."); return True
        except Exception as e: print(f"[QdrantClientWrapper] ERROR adding points to '{collection_name}': {e}"); traceback.print_exc(); return False

    async def search_vectors(
        self, collection_name: str, query_vector: List[float], limit: int,
        user_id_filter: Optional[str] = None, other_filters: Optional[List[Dict[str, Any]]] = None, **kwargs: Any
    ) -> List[Dict[str, Any]]:
        if not self.is_available or not self._qdrant_client or not qdrant_models:
             print(f"[QdrantClientWrapper] search_vectors: Client/models not available for '{collection_name}'."); return []
        try:
            print(f"[QdrantClientWrapper] Searching '{collection_name}' (limit: {limit}). User filter: {user_id_filter}")
            filter_conditions: List[qdrant_models.Condition] = []
            if user_id_filter:
                 filter_conditions.append(qdrant_models.FieldCondition(key="user_id", match=qdrant_models.MatchValue(value=user_id_filter)))
            if other_filters:
                for f_cond in other_filters:
                    if "key" in f_cond and "match_value" in f_cond: filter_conditions.append(qdrant_models.FieldCondition(key=f_cond["key"], match=qdrant_models.MatchValue(value=f_cond["match_value"])))
                    elif "key" in f_cond and "match_any" in f_cond: filter_conditions.append(qdrant_models.FieldCondition(key=f_cond["key"], match=qdrant_models.MatchAny(any=f_cond["match_any"])))
            qdrant_filter: Optional[qdrant_models.Filter] = qdrant_models.Filter(must=filter_conditions) if filter_conditions else None
            from fastapi.concurrency import run_in_threadpool
            search_result = await run_in_threadpool(
                self._qdrant_client.search, collection_name=collection_name, query_vector=query_vector,
                query_filter=qdrant_filter, limit=limit,
                with_payload=kwargs.get("with_payload", True), with_vectors=kwargs.get("with_vectors", False)
            )
            print(f"[QdrantClientWrapper] Search in '{collection_name}' found {len(search_result)} results.")
            formatted_results: List[Dict[str, Any]] = []
            for hit in search_result:
                 result_item: Dict[str, Any] = {"id": hit.id, "score": hit.score, "payload": hit.payload}
                 if kwargs.get("with_vectors", False) and hit.vector: result_item["vector"] = hit.vector
                 formatted_results.append(result_item)
            return formatted_results
        except Exception as e: print(f"[QdrantClientWrapper] ERROR searching '{collection_name}': {e}"); traceback.print_exc(); return []

    async def get_point(self, collection_name: str, point_id: Union[str, int, uuid.UUID]) -> Optional[Dict[str, Any]]:
        if not self.is_available or not self._qdrant_client:
            print(f"[QdrantClientWrapper] get_point: Client not available for '{collection_name}'."); return None
        try:
            from fastapi.concurrency import run_in_threadpool
            retrieved_points = await run_in_threadpool(
                self._qdrant_client.retrieve, collection_name=collection_name, ids=[point_id],
                with_payload=True, with_vectors=False
            )
            if retrieved_points:
                point_record = retrieved_points[0]
                return {"id": point_record.id, "payload": point_record.payload, "vector": point_record.vector if point_record.vector else None}
            return None
        except Exception as e: print(f"[QdrantClientWrapper] ERROR retrieving point '{point_id}' from '{collection_name}': {e}"); traceback.print_exc(); return None

    async def delete_points(self, collection_name: str, point_ids: List[Union[str, int, uuid.UUID]]) -> bool:
        if not self.is_available or not self._qdrant_client:
            print(f"[QdrantClientWrapper] delete_points: Client not available for '{collection_name}'."); return False
        try:
            from fastapi.concurrency import run_in_threadpool
            await run_in_threadpool(self._qdrant_client.delete, collection_name=collection_name, points_selector=point_ids, wait=True)
            print(f"[QdrantClientWrapper] Successfully deleted {len(point_ids)} points from '{collection_name}'."); return True
        except Exception as e: print(f"[QdrantClientWrapper] ERROR deleting points from '{collection_name}': {e}"); traceback.print_exc(); return False

    async def close(self):
        if self._qdrant_client and hasattr(self._qdrant_client, 'close') and callable(self._qdrant_client.close):
             print("[QdrantClientWrapper] Closing Qdrant client...")
             try:
                 from fastapi.concurrency import run_in_threadpool
                 await run_in_threadpool(self._qdrant_client.close)
                 print("[QdrantClientWrapper] Qdrant client closed.")
             except Exception as e: print(f"[QdrantClientWrapper] Error closing Qdrant client: {e}")
        else: print("[QdrantClientWrapper] Qdrant client does not require explicit close or close method not found.")

print("[QDRANT_CLIENT_WRAPPER] QdrantClientWrapper class defined (v2).")