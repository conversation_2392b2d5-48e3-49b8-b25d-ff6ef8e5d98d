# PLAP/plap-agents/clients/vertex_ai_client.py
print("[VERTEX_AI_CLIENT] Loading vertex_ai_client.py (v3 - absolute imports corrected)")

import vertexai # type: ignore
from vertexai.generative_models import GenerativeModel, Part, FinishReason, HarmCategory, HarmBlockThreshold, SafetySetting
try:
    from vertexai.language_models import TextEmbeddingModel
    print("[VERTEX_AI_CLIENT] TextEmbeddingModel imported from vertexai.language_models.")
except ImportError:
    print("[VERTEX_AI_CLIENT] WARNING: TextEmbeddingModel not found in vertexai.language_models. Trying alternative import...")
    try:
        from vertexai.preview.language_models import TextEmbeddingModel
        print("[VERTEX_AI_CLIENT] TextEmbeddingModel imported from vertexai.preview.language_models.")
    except ImportError:
        print("[VERTEX_AI_CLIENT] CRITICAL WARNING: TextEmbeddingModel not found in any common SDK path. Embedding generation will fail.")
        TextEmbeddingModel = None # type: ignore

try:
     from google.cloud import vision
     print("[VERTEX_AI_CLIENT] Google Cloud Vision SDK imported.")
except ImportError:
     print("[VERTEX_AI_CLIENT] WARNING: Google Cloud Vision SDK not found.")
     vision = None # type: ignore

from typing import Optional, List, Dict, Any, Union, cast
import traceback

# --- CORRECCIÓN DE IMPORTACIÓN ---
# Asumiendo que 'config.py' está en la raíz del directorio '/app' del servicio
# y '/app' está en PYTHONPATH.
from config import settings
# --- FIN DE CORRECCIÓN ---


class VertexAIClient:
    """
    Cliente wrapper para interactuar con Google Cloud Vertex AI APIs.
    Esta clase ASUME que `vertexai.init(project=project_id, location=location)`
    ha sido llamado UNA VEZ al inicio de la aplicación (ej. en main.py startup event).
    """

    def __init__(self, project_id: Optional[str], location: Optional[str]):
        """
        Inicializa el cliente Vertex AI.
        project_id y location se guardan para referencia, pero no se usan para re-inicializar el SDK.
        """
        self._project_id = project_id
        self._location = location
        self._is_sdk_initialized_globally = False

        if hasattr(vertexai, 'init') and hasattr(vertexai, 'generative_models') and \
           (TextEmbeddingModel is not None or (hasattr(vertexai, 'language_models') and hasattr(vertexai.language_models, 'TextEmbeddingModel')) or \
            (hasattr(vertexai, 'preview') and hasattr(vertexai.preview, 'language_models') and hasattr(vertexai.preview.language_models, 'TextEmbeddingModel'))):
            if hasattr(vertexai, '_did_init') and vertexai._did_init: # type: ignore
                self._is_sdk_initialized_globally = True
                print(f"[VertexAIClient] Vertex AI SDK components imported and global initialization confirmed.")
            else:
                print(f"[VertexAIClient] WARNING: Vertex AI SDK components imported, but global 'vertexai.init()' status unknown or not confirmed. Calls might fail if not initialized.")
        else:
            print("[VertexAIClient] CRITICAL WARNING: Essential Vertex AI SDK components (GenerativeModel or TextEmbeddingModel) not imported correctly. Calls will fail or return mocks.")


    @property
    def is_available(self) -> bool:
        """Verifica si el cliente Vertex AI está listo para hacer llamadas (SDK importable y asumimos inicializado)."""
        return self._is_sdk_initialized_globally


    async def call_generative_model(
        self,
        model_name: str,
        prompt_text: str,
        contents: Optional[List[Union[str, Part]]] = None,
        generation_config_override: Optional[Dict[str, Any]] = None,
        safety_settings_override: Optional[Dict[HarmCategory, HarmBlockThreshold]] = None,
        tools_override: Optional[List[Any]] = None
    ) -> str:
        """
        Llama a un modelo generativo de Vertex AI (ej. Gemini).
        Permite pasar 'prompt_text' para un prompt simple, o 'contents' para multimodal/chat.
        Retorna la respuesta generada o un mensaje de error/mock.
        """
        if not self.is_available or not hasattr(vertexai.generative_models, 'GenerativeModel'): # type: ignore
            mock_response = f"[MOCK RESPONSE from {model_name} - Vertex AI GenerativeModels not available or SDK not initialized]"
            print(f"[VertexAIClient] {mock_response}")
            return mock_response

        try:
            print(f"[VertexAIClient] Calling Generative Model: {model_name} (prompt start: '{str(prompt_text or contents)[:100]}...')")

            default_generation_config = {
                "max_output_tokens": 2048,
                "temperature": 0.7,
                "top_p": 0.9,
                "top_k": 40,
            }
            current_generation_config = {**default_generation_config, **(generation_config_override or {})}

            default_safety_settings = {
                HarmCategory.HARM_CATEGORY_HARASSMENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_HATE_SPEECH: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_SEXUALLY_EXPLICIT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
                HarmCategory.HARM_CATEGORY_DANGEROUS_CONTENT: HarmBlockThreshold.BLOCK_MEDIUM_AND_ABOVE,
            }
            current_safety_settings = safety_settings_override if safety_settings_override is not None else default_safety_settings

            model_instance = GenerativeModel(model_name) # type: ignore

            request_contents: List[Union[str, Part]]
            if contents:
                request_contents = contents
            elif prompt_text:
                request_contents = [prompt_text]
            else:
                print("[VertexAIClient] ERROR: Neither 'prompt_text' nor 'contents' provided for LLM call.")
                return "[ERROR: No prompt content provided to LLM]"

            response = await model_instance.generate_content_async(
                request_contents,
                generation_config=current_generation_config,
                safety_settings=current_safety_settings,
                tools=tools_override
            )

            print(f"[VertexAIClient] Response from {model_name} received.")

            generated_text_to_return = "[LLM Response: Empty or Unexpected Format]"
            if response and response.candidates:
                candidate = response.candidates[0]
                if candidate.finish_reason == FinishReason.SAFETY: # type: ignore
                    generated_text_to_return = f"[LLM blocked response due to Safety reasons: {candidate.finish_message or 'No details'}]"
                    print(f"[VertexAIClient] Response from {model_name} BLOCKED due to SAFETY. Reason: {candidate.finish_message}")
                elif candidate.finish_reason not in [FinishReason.STOP, FinishReason.FINISH_REASON_UNSPECIFIED, FinishReason.MAX_TOKENS]: # type: ignore
                     reason_name = candidate.finish_reason.name if hasattr(candidate.finish_reason, 'name') else str(candidate.finish_reason)
                     generated_text_to_return = f"[LLM finished unexpectedly: {reason_name}. Message: {candidate.finish_message or 'N/A'}]"
                     print(f"[VertexAIClient] LLM finished with non-STOP/MAX_TOKENS reason: {reason_name}. Message: {candidate.finish_message}")
                elif candidate.content and candidate.content.parts:
                    parts_text = [part.text for part in candidate.content.parts if hasattr(part, 'text') and part.text is not None]
                    generated_text_to_return = "".join(parts_text)
                    if not generated_text_to_return and candidate.finish_reason == FinishReason.STOP: # type: ignore
                        print(f"[VertexAIClient] LLM from {model_name} returned STOP but content parts were empty or non-textual.")
                        generated_text_to_return = "[LLM Response: STOP but no text content]"
                else:
                    generated_text_to_return = "[LLM Response: Empty or Unexpected Format]"
                    print(f"[VertexAIClient] Warning: LLM response from {model_name} structure was unexpected or empty. Full candidate: {candidate}")
            elif hasattr(response, 'text') and response.text is not None:
                generated_text_to_return = response.text
            else:
                generated_text_to_return = "[LLM Response: No valid candidates or text field]"
                print(f"[VertexAIClient] Warning: LLM response from {model_name} had no valid candidates or text. Full response: {response}")

            return generated_text_to_return

        except Exception as e:
            print(f"[VertexAIClient] ERROR calling Generative Model {model_name}: {e}")
            traceback.print_exc()
            return f"[ERROR CALLING LLM {model_name}: {str(e)}]"


    async def generate_embedding(self, texts: List[str], model_name: Optional[str] = None) -> List[List[float]]:
        """
        Genera embeddings para una lista de textos.
        Retorna una lista de vectores de embedding. Si falla, retorna una lista de listas de ceros.
        """
        if not self.is_available or TextEmbeddingModel is None:
            mock_embeddings = [[0.0] * settings.TEXT_EMBEDDING_DIMENSION] * len(texts)
            print(f"[VertexAIClient] MOCK EMBEDDING: Vertex AI TextEmbeddingModel not available/initialized. Returning {len(texts)} mock embeddings.")
            return mock_embeddings

        model_to_use = model_name or settings.TEXT_EMBEDDING_MODEL_NAME

        try:
            print(f"[VertexAIClient] Generating embeddings for {len(texts)} texts using model: {model_to_use}")
            embedding_model = TextEmbeddingModel.from_pretrained(model_to_use) # type: ignore

            from fastapi.concurrency import run_in_threadpool
            embeddings_result = await run_in_threadpool(embedding_model.get_embeddings, texts) # type: ignore

            print(f"[VertexAIClient] Embeddings generated. Received {len(embeddings_result)} embedding objects.")

            vectors: List[List[float]] = []
            for embedding_obj in embeddings_result:
                if hasattr(embedding_obj, 'values') and isinstance(embedding_obj.values, list):
                    current_vector = [float(v) for v in embedding_obj.values if isinstance(v, (int, float))]
                    if len(current_vector) == settings.TEXT_EMBEDDING_DIMENSION:
                         vectors.append(current_vector)
                    else:
                         print(f"[VertexAIClient] WARNING: Embedding vector dimension mismatch. Expected {settings.TEXT_EMBEDDING_DIMENSION}, got {len(current_vector)}. Filling with zeros.")
                         vectors.append([0.0] * settings.TEXT_EMBEDDING_DIMENSION)
                else:
                     print(f"[VertexAIClient] WARNING: Received embedding object without 'values' list for text: '{texts[embeddings_result.index(embedding_obj)][:50]}...'. Filling with zeros.")
                     vectors.append([0.0] * settings.TEXT_EMBEDDING_DIMENSION)

            return vectors

        except Exception as e:
            print(f"[VertexAIClient] ERROR generating embeddings with model {model_to_use}: {e}")
            traceback.print_exc()
            error_embeddings = [[0.0] * settings.TEXT_EMBEDDING_DIMENSION] * len(texts)
            return error_embeddings

print("[VERTEX_AI_CLIENT] VertexAIClient class defined (v3).")