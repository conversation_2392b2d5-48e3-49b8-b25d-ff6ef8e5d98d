# PLAP/plap-agents/clients/zep_client_wrapper.py
print("[ZEP_CLIENT_WRAPPER] Loading zep_client_wrapper.py (v8 - Graph placeholder syntax fix)")

import os
from typing import Optional, List, Dict, Any, cast, Union, Type
import traceback
from datetime import datetime, timezone
import uuid

from config import settings
from pydantic import BaseModel, ConfigDict, Field

# --- Intento de Importación del SDK de Zep Cloud ---
ZEP_SDK_AVAILABLE = False
AsyncZep = None
Message: Optional[Type[BaseModel]] = None
Memory: Optional[Type[BaseModel]] = None
ZepSession: Optional[Type[BaseModel]] = None
ZepUser: Optional[Type[BaseModel]] = None
ZepNotFoundError: Optional[Type[Exception]] = None
ZepAPIError: Optional[Type[Exception]] = None
EntityNode: Optional[Type[BaseModel]] = None # Placeholder type
EntityEdge: Optional[Type[BaseModel]] = None # Placeholder type
GraphSearchResult: Optional[Type[BaseModel]] = None # Placeholder type

try:
    print("[ZEP_CLIENT_WRAPPER] Attempting to import Zep Cloud SDK...")
    from zep_cloud.client import AsyncZep
    from zep_cloud.types import Message as ImportedMessage, Memory as ImportedMemory, Session as ImportedZepSession, User as ImportedZepUser
    Message = ImportedMessage
    Memory = ImportedMemory
    ZepSession = ImportedZepSession
    ZepUser = ImportedZepUser
    try:
        from zep_cloud.core import NotFoundError as ImportedZepNotFoundError, APIError as ImportedZepAPIError
        ZepNotFoundError = ImportedZepNotFoundError
        ZepAPIError = ImportedZepAPIError
        print("[ZEP_CLIENT_WRAPPER] Zep exceptions (NotFoundError, APIError) imported from zep_cloud.core.")
    except ImportError:
        print("[ZEP_CLIENT_WRAPPER] WARNING: Could not import NotFoundError or APIError from zep_cloud.core.")
    ZEP_SDK_AVAILABLE = True
    print("[ZEP_CLIENT_WRAPPER] Zep Cloud SDK (AsyncZep, main types) imported successfully.")
except ImportError as e_imp:
    print(f"[ZEP_CLIENT_WRAPPER] CRITICAL WARNING: Zep Cloud SDK NOT FOUND (ImportError: {e_imp}). Zep functionality will be disabled.")
    ZEP_SDK_AVAILABLE = False
except Exception as e_gen_outer:
     print(f"[ZEP_CLIENT_WRAPPER] CRITICAL WARNING: UNEXPECTED ERROR during initial Zep SDK import: {e_gen_outer}."); traceback.print_exc(); ZEP_SDK_AVAILABLE = False

class BasePlaceholder(BaseModel): model_config = ConfigDict(protected_namespaces=())

if Message is None:
    class Message(BasePlaceholder): # type: ignore
        role_type: str = Field(...)
        content: str = Field(...)
        role: Optional[str] = None; uuid: Optional[str] = None; created_at: Optional[datetime] = None; metadata: Optional[Dict[str, Any]] = None; token_count: Optional[int] = None
if Memory is None:
    class SummaryPlaceholder(BasePlaceholder): uuid: Optional[str] = None; created_at: Optional[datetime] = None; content: Optional[str] = None; summary_template: Optional[str] = None
    class MemoryContextPlaceholder(BasePlaceholder): facts: Optional[List[str]] = None; summary: Optional[SummaryPlaceholder] = None
    class Memory(BasePlaceholder): context: Optional[MemoryContextPlaceholder] = None; messages: Optional[List[Message]] = None; uuid: Optional[str] = None; created_at: Optional[datetime] = None # type: ignore
if ZepSession is None:
    class ZepSession(BasePlaceholder): session_id: str = Field(...); user_id: Optional[str] = None; metadata: Optional[Dict[str, Any]] = None; uuid: Optional[str] = None; created_at: Optional[datetime] = None; updated_at: Optional[datetime] = None; deleted_at: Optional[datetime] = None # type: ignore
if ZepUser is None:
    class ZepUser(BasePlaceholder): user_id: str = Field(...); metadata: Optional[Dict[str, Any]] = None; uuid: Optional[str] = None; created_at: Optional[datetime] = None; updated_at: Optional[datetime] = None; deleted_at: Optional[datetime] = None; sessions: Optional[List[ZepSession]] = None # type: ignore
if ZepNotFoundError is None: ZepNotFoundError = type('ZepNotFoundError', (Exception,), {}) # type: ignore
if ZepAPIError is None: ZepAPIError = type('ZepAPIError', (Exception,), {}) # type: ignore

# --- CORRECCIÓN DE SINTAXIS AQUÍ ---
if EntityNode is None:
    class EntityNode(BasePlaceholder): # type: ignore
        id: str = Field(...)
        label: str = Field(...)
        metadata: Optional[Dict[str,Any]] = None
if EntityEdge is None:
    class EntityEdge(BasePlaceholder): # type: ignore
        source_node_id: str = Field(...)
        target_node_id: str = Field(...)
        relationship: str = Field(...)
        metadata: Optional[Dict[str,Any]] = None
if GraphSearchResult is None:
    class GraphSearchResult(BasePlaceholder): # type: ignore
        nodes: List[EntityNode] = Field(...) # type: ignore
        edges: List[EntityEdge] = Field(...) # type: ignore
# --- FIN DE CORRECCIÓN DE SINTAXIS ---

class ZepClientWrapper:
    """Cliente wrapper para interactuar con la API de Zep Cloud."""
    def __init__(self, api_key: Optional[str], api_url: Optional[str]):
        self._zep_client: Optional[AsyncZep] = None # type: ignore
        self._is_initialized = False
        if ZEP_SDK_AVAILABLE and api_key and AsyncZep:
            try:
                self._zep_client = AsyncZep(api_key=api_key)
                self._is_initialized = True
                print("[ZepClientWrapper] AsyncZep client initialized (api_url not passed to constructor).")
                effective_url_used = api_url
                if self._zep_client and hasattr(self._zep_client, '_client') and hasattr(self._zep_client._client, 'base_url'):
                     effective_url_used = str(self._zep_client._client.base_url)
                print(f"[ZepClientWrapper] Zep API Key: {'********' if api_key else 'Not Set'}")
                print(f"Configured ZEP_API_URL (from .env via settings): {settings.ZEP_API_URL if settings.ZEP_API_URL else 'Not Set/SDK Default'}")
                print(f"Effective Zep API URL used by SDK (approx): {effective_url_used if effective_url_used else 'SDK Default'}")
            except Exception as e: print(f"[ZepClientWrapper] ERROR initializing AsyncZep client: {e}"); traceback.print_exc(); self._zep_client = None; self._is_initialized = False
        elif ZEP_SDK_AVAILABLE: print("[ZepClientWrapper] WARNING: ZEP_API_KEY not configured.")
        else: print("[ZepClientWrapper] WARNING: Zep SDK not available.")

    @property
    def is_available(self) -> bool:
        return self._is_initialized and ZEP_SDK_AVAILABLE and ZepNotFoundError is not None and ZepAPIError is not None and Message is not None and Memory is not None # type: ignore

    async def ensure_user_and_session(self, user_id: str, session_id: str) -> bool:
        if not self.is_available or not self._zep_client or not ZepUser or not ZepSession or not ZepNotFoundError: return False # type: ignore
        zep_user_id = user_id; zep_session_id = session_id
        try:
            try: await self._zep_client.user.get(user_id=zep_user_id)
            except ZepNotFoundError: # type: ignore
                user_payload = ZepUser(user_id=zep_user_id, metadata={"plap_source": "agents_service", "created_at": datetime.now(timezone.utc).isoformat()}) # type: ignore
                await self._zep_client.user.add(user_payload) # type: ignore
            try: await self._zep_client.memory.get_session(session_id=zep_session_id)
            except ZepNotFoundError: # type: ignore
                 session_payload = ZepSession(session_id=zep_session_id, user_id=zep_user_id, metadata={"plap_source": "agents_service", "created_at": datetime.now(timezone.utc).isoformat()}) # type: ignore
                 await self._zep_client.memory.add_session(session_payload) # type: ignore
            return True
        except ZepAPIError as e_api: print(f"[ZepClientWrapper] Zep API Error in ensure_user_and_session: {e_api}"); traceback.print_exc(); return False # type: ignore
        except Exception as e_other: print(f"[ZepClientWrapper] Unexpected error in ensure_user_and_session: {e_other}"); traceback.print_exc(); return False

    async def get_memory_context(self, session_id: str, user_id: str) -> str:
        if not self.is_available or not self._zep_client or not Memory or not Message: return "[Error: Zep Memory service unavailable]" # type: ignore
        if not await self.ensure_user_and_session(user_id, session_id): return "[Error: Failed to ensure Zep user/session]"
        try:
            memory_data: Optional[Memory] = await self._zep_client.memory.get(session_id=session_id) # type: ignore
            if not memory_data: return ""
            context_parts = []
            if memory_data.context: # type: ignore
                current_context = cast(Any, memory_data.context) # type: ignore
                if hasattr(current_context, 'facts') and current_context.facts: context_parts.append("Contexto Zep (Hechos):\n" + "\n".join(current_context.facts))
                if hasattr(current_context, 'summary') and current_context.summary and hasattr(current_context.summary, 'content') and current_context.summary.content: context_parts.append("Resumen Zep:\n" + current_context.summary.content)
            if memory_data.messages: # type: ignore
                 recent_messages = memory_data.messages[-6:]; message_lines = ["Historial Reciente (Zep):"] # type: ignore
                 for msg_item in recent_messages:
                     role_name = str(msg_item.role or msg_item.role_type or "unknown").capitalize() # type: ignore
                     message_lines.append(f"{role_name}: {msg_item.content}") # type: ignore
                 context_parts.append("\n".join(message_lines))
            if not context_parts: return ""
            return "\n\n---\n\n".join(filter(None, context_parts)).strip()
        except ZepNotFoundError: return "" # type: ignore
        except ZepAPIError as e: traceback.print_exc(); return f"[Error API Zep: {e}]" # type: ignore
        except Exception as e: traceback.print_exc(); return f"[Error Zep: {str(e)}]"

    async def add_messages_to_memory(self, session_id: str, user_id: str, user_message: str, ai_message: str):
        if not self.is_available or not self._zep_client or not Message: return # type: ignore
        if not await self.ensure_user_and_session(user_id, session_id): return
        messages_to_save = [ Message(role_type="user", content=user_message, role="Human"), Message(role_type="assistant", content=ai_message, role="AI")] # type: ignore
        try: await self._zep_client.memory.add(session_id=session_id, messages=messages_to_save)
        except Exception as e: print(f"Error Zep add_messages: {e}"); traceback.print_exc()

    async def search_graph_nodes(self, user_id: str, query: str, limit: int = 5) -> List[Dict[str, Any]]:
        if not self.is_available or not self._zep_client: return []
        temp_session_id = f"graph_search_temp_{uuid.uuid4().hex}"
        if not await self.ensure_user_and_session(user_id, temp_session_id): return []
        print(f"[ZepClientWrapper] Searching Zep graph nodes for user '{user_id}' with query: '{query}' (limit: {limit})")
        try:
            print("[ZepClientWrapper] WARNING: search_graph_nodes using placeholder memory search.")
            search_results_raw = await self._zep_client.memory.search(session_id=temp_session_id, query=query, user_id=user_id, limit=limit)
            formatted_results = []
            if Message: # type: ignore
                for result in search_results_raw:
                    if hasattr(result, 'message') and isinstance(result.message, Message) and hasattr(result.message, 'content'): # type: ignore
                        formatted_results.append({"score": getattr(result, 'dist', getattr(result, 'score', 0.0)), "text": result.message.content, "type": "memory_message", "metadata": getattr(result.message, 'metadata', {})})
            return formatted_results
        except Exception as e: print(f"[ZepClientWrapper] Unexpected error during Zep graph search: {e}"); traceback.print_exc(); return []

    async def get_user_graph_data_for_visualization(self, user_id: str) -> Optional[Dict[str, Any]]:
        if not self.is_available or not self._zep_client: return None
        temp_session_id = f"graph_data_temp_{uuid.uuid4().hex}"
        if not await self.ensure_user_and_session(user_id, temp_session_id): return None
        print(f"[ZepClientWrapper] Getting graph data for user '{user_id}' from Zep for visualization.")
        try:
            print("[ZepClientWrapper] WARNING: get_user_graph_data_for_visualization placeholder returning mock data.")
            node_id_user = str(uuid.uuid4()); node_id_idea1 = str(uuid.uuid4()); node_id_tech = str(uuid.uuid4()); node_id_fact = str(uuid.uuid4())
            mock_graph_data = {
                "nodes": [
                    {"id": node_id_user, "label": f"Usuario {user_id[:8]}", "type": "User", "group": 0, "details": {"user_id": user_id}},
                    {"id": node_id_idea1, "label": "Idea: Mejorar UI", "type": "Idea", "group": 1, "details": {"source": "conversación X"}},
                    {"id": node_id_tech, "label": "Entidad: FastAPI", "type": "Tecnología", "group": 2, "details": {"relevance": 0.8}},
                    {"id": node_id_fact, "label": "Hecho: Usa Python", "type": "Hecho", "group": 3, "details": {"source": "Documento Y"}},
                ], "edges": [
                    {"from": node_id_user, "to": node_id_idea1, "label": "Tuvo idea", "arrows": "to"},
                    {"from": node_id_user, "to": node_id_tech, "label": "Mencionó", "arrows": "to"},
                    {"from": node_id_tech, "to": node_id_fact, "label": "Relacionado con", "arrows": "to"},
                ]}
            return mock_graph_data
        except Exception as e: print(f"[ZepClientWrapper] Unexpected error getting Zep graph data: {e}"); traceback.print_exc(); return None

    async def close(self):
        if self._zep_client and hasattr(self._zep_client, 'close') and callable(self._zep_client.close):
             print("[ZepClientWrapper] Closing AsyncZep client...")
             try: await self._zep_client.close(); print("[ZepClientWrapper] AsyncZep client closed.")
             except Exception as e: print(f"[ZepClientWrapper] Error closing AsyncZep client: {e}"); traceback.print_exc()

print("[ZEP_CLIENT_WRAPPER] ZepClientWrapper class defined (v8 - Graph placeholder syntax fixed).")