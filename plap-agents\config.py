# PLAP/plap-agents/config.py
print("[CONFIG] Loading config.py (v2 - GCS_BUCKET_NAME uncommented)")

import os
from dotenv import load_dotenv
from typing import Optional, List, cast # Quité Dict y Any si no se usan aquí directamente

if os.getenv("DOCKER_ENV") != "true":
    print("[CONFIG] Attempting to load .env file for local development...")
    load_dotenv()


class Settings:
    """
    Centraliza la configuración del servicio de Agentes, leída desde variables de entorno.
    """
    SERVICE_TITLE: str = "PLAP Agents Service"
    SERVICE_VERSION: str = os.getenv("AGENTS_SERVICE_VERSION", "0.9.12_ModularRefactor")
    SERVICE_DESCRIPTION: str = "Servicio para la lógica modular de los agentes de IA de PLAP."
    CORS_ORIGINS_STRING: str = os.getenv("CORS_ORIGINS_AGENTS_SERVICE", "*")
    CORS_ORIGINS: List[str] = [origin.strip() for origin in CORS_ORIGINS_STRING.split(',')]

    LOCAL_UPLOADS_DIR: str = os.getenv("LOCAL_UPLOADS_DIR", "/app/uploads_temp")
    # --- CORRECCIÓN: Descomentar GCS_BUCKET_NAME ---
    GCS_BUCKET_NAME: Optional[str] = os.getenv("GCS_BUCKET_NAME")
    # --- FIN DE CORRECCIÓN ---

    GOOGLE_CLOUD_PROJECT_ID: Optional[str] = os.getenv("GOOGLE_CLOUD_PROJECT_ID")
    VERTEX_AI_LOCATION: str = os.getenv("VERTEX_AI_LOCATION", "us-central1")
    GEMINI_FLASH_MODEL_NAME: str = os.getenv("GEMINI_FLASH_MODEL_NAME", "gemini-1.5-flash-001")
    GEMINI_PRO_MODEL_NAME: str = os.getenv("GEMINI_PRO_MODEL_NAME", "gemini-1.5-pro-preview-0409")
    GEMINI_PRO_VISION_MODEL_NAME: str = os.getenv("GEMINI_PRO_VISION_MODEL_NAME", "gemini-1.0-pro-vision-001")
    TEXT_EMBEDDING_MODEL_NAME: str = os.getenv("TEXT_EMBEDDING_MODEL_NAME", "textembedding-gecko@003")
    TEXT_EMBEDDING_DIMENSION: int = int(os.getenv("TEXT_EMBEDDING_DIMENSION", "768"))

    ZEP_API_KEY: Optional[str] = os.getenv("ZEP_API_KEY")
    ZEP_API_URL: Optional[str] = os.getenv("ZEP_API_URL")

    QDRANT_URL: str = os.getenv("QDRANT_INTERNAL_URL", "http://qdrant:6333")
    QDRANT_API_KEY: Optional[str] = os.getenv("QDRANT_API_KEY")
    QDRANT_KB_COLLECTION: str = os.getenv("QDRANT_KB_COLLECTION", "plap_knowledge_base")
    QDRANT_PROMPTS_COLLECTION: str = os.getenv("QDRANT_PROMPTS_COLLECTION", "user_prompts")
    QDRANT_DOCS_COLLECTION: str = os.getenv("QDRANT_DOCS_COLLECTION", "user_documents")
    QDRANT_EL_COLLECTION: str = os.getenv("QDRANT_EL_COLLECTION", "experience_library")

    USER_SERVICE_URL: Optional[str] = os.getenv("USER_SERVICE_INTERNAL_URL")
    PROMPT_LIBRARY_SERVICE_URL: Optional[str] = os.getenv("PROMPT_LIBRARY_SERVICE_INTERNAL_URL")

    GENERATOR_AGENT_DEFAULT_MODEL: str = os.getenv("GENERATOR_AGENT_DEFAULT_MODEL", GEMINI_FLASH_MODEL_NAME)
    EXTRACTOR_AGENT_DEFAULT_MODEL: str = os.getenv("EXTRACTOR_AGENT_DEFAULT_MODEL", GEMINI_PRO_MODEL_NAME)
    COMPLIANCE_AGENT_DEFAULT_MODEL: Optional[str] = os.getenv("COMPLIANCE_AGENT_DEFAULT_MODEL")
    REVIEWER_AGENT_DEFAULT_MODEL: str = os.getenv("REVIEWER_AGENT_DEFAULT_MODEL", GEMINI_PRO_MODEL_NAME)
    RECOMMENDER_AGENT_DEFAULT_MODEL: str = os.getenv("RECOMMENDER_AGENT_DEFAULT_MODEL", GEMINI_FLASH_MODEL_NAME)
    SUGGESTER_AGENT_DEFAULT_MODEL: str = os.getenv("SUGGESTER_AGENT_DEFAULT_MODEL", GEMINI_FLASH_MODEL_NAME)

    GENERATOR_MAX_OUTPUT_TOKENS: int = int(os.getenv("GENERATOR_MAX_OUTPUT_TOKENS", "2048"))
    SUGGESTER_MIN_TEXT_LENGTH_THRESHOLD: int = int(os.getenv("SUGGESTER_MIN_TEXT_LENGTH_THRESHOLD", "3"))
    SUGGESTER_QDRANT_PREFIX: str = os.getenv("SUGGESTER_QDRANT_PREFIX", "Consider: ")
    SUGGESTER_MAX_QDRANT_SUGGESTIONS: int = int(os.getenv("SUGGESTER_MAX_QDRANT_SUGGESTIONS", "2"))
    SUGGESTER_MAX_TOTAL_SUGGESTIONS: int = int(os.getenv("SUGGESTER_MAX_TOTAL_SUGGESTIONS", "3"))


    def __init__(self):
        if not self.GOOGLE_CLOUD_PROJECT_ID:
            print("[CONFIG] WARNING: GOOGLE_CLOUD_PROJECT_ID no está configurado.")
        if not self.ZEP_API_KEY:
            print("[CONFIG] WARNING: ZEP_API_KEY no está configurado.")
        if not self.GCS_BUCKET_NAME: # Añadir advertencia si no está configurado
            print("[CONFIG] INFO: GCS_BUCKET_NAME no está configurado. La funcionalidad de Google Cloud Storage estará deshabilitada.")


settings = Settings()

print("-" * 50)
print(f"[CONFIG] {settings.SERVICE_TITLE} v{settings.SERVICE_VERSION} - Configuración Cargada (v2)")
print(f"  CORS Origins: {settings.CORS_ORIGINS}")
print(f"  Google Project ID: {settings.GOOGLE_CLOUD_PROJECT_ID if settings.GOOGLE_CLOUD_PROJECT_ID else 'NO CONFIGURADO'}")
print(f"  Vertex AI Location: {settings.VERTEX_AI_LOCATION}")
print(f"  Gemini Flash Model: {settings.GEMINI_FLASH_MODEL_NAME}")
print(f"  Embedding Model: {settings.TEXT_EMBEDDING_MODEL_NAME} (Dim: {settings.TEXT_EMBEDDING_DIMENSION})")
print(f"  Zep API Key: {'CONFIGURADA' if settings.ZEP_API_KEY else 'NO CONFIGURADA'}")
print(f"  Zep API URL: {settings.ZEP_API_URL if settings.ZEP_API_URL else 'Default SDK URL'}")
print(f"  Qdrant URL: {settings.QDRANT_URL}")
print(f"  Qdrant User Docs Collection: {settings.QDRANT_DOCS_COLLECTION}")
print(f"  Local Uploads Dir: {settings.LOCAL_UPLOADS_DIR}")
print(f"  GCS Bucket Name: {settings.GCS_BUCKET_NAME if settings.GCS_BUCKET_NAME else 'No Configurado'}") # Mostrar si está configurado
print(f"  User Service URL: {settings.USER_SERVICE_URL if settings.USER_SERVICE_URL else 'No Configurado'}")
print(f"  Prompt Library Service URL: {settings.PROMPT_LIBRARY_SERVICE_URL if settings.PROMPT_LIBRARY_SERVICE_URL else 'No Configurado'}")
print("-" * 50)