# PLAP/plap-agents/document_processing/file_parser.py
print("[FILE_PARSER] Loading file_parser.py")

from typing import Optional, Any
import traceback
import os
import aiofiles # Para lectura asíncrona de archivos locales
import mimetypes # Para inferir tipo de archivo
import re # Para limpieza de texto

# --- Intento de Importación de Librerías de Parsing ---
# Estas banderas indican si las librerías necesarias están disponibles.
PYPDF2_AVAILABLE = False
try:
    from PyPDF2 import PdfReader # Nombre de clase común, asegurarse que la librería es PyPDF2 o pypdf
    PYPDF2_AVAILABLE = True
    print("[FILE_PARSER] PyPDF2 (or compatible PDF library) imported.")
except ImportError:
    print("[FILE_PARSER] WARNING: PyPDF2 (or compatible PDF library) not found. PDF parsing disabled.")

PYTHON_DOCX_AVAILABLE = False
try:
    from docx import Document # Del paquete python-docx
    PYTHON_DOCX_AVAILABLE = True
    print("[FILE_PARSER] python-docx imported.")
except ImportError:
    print("[FILE_PARSER] WARNING: python-docx not found. DOCX parsing disabled.")

PILLOW_AVAILABLE = False
try:
    from PIL import Image # Del paquete Pillow
    PILLOW_AVAILABLE = True
    print("[FILE_PARSER] Pillow (PIL) imported.")
except ImportError:
    print("[FILE_PARSER] WARNING: Pillow (PIL) not found. Basic image processing disabled.")

# Google Cloud Vision API Client (para OCR de imágenes)
# La inicialización del cliente Vision se hará en main.py y se pasará aquí si es necesario.
# Aquí solo verificamos la disponibilidad del SDK y el tipo.
GOOGLE_VISION_SDK_AVAILABLE = False
VisionImageAnnotatorClient = None
VisionImage = None
try:
    from google.cloud import vision
    VisionImageAnnotatorClient = vision.ImageAnnotatorClient # Guardar el tipo
    VisionImage = vision.Image # Guardar el tipo
    GOOGLE_VISION_SDK_AVAILABLE = True
    print("[FILE_PARSER] Google Cloud Vision SDK types imported.")
except ImportError:
     print("[FILE_PARSER] WARNING: Google Cloud Vision SDK not found. Image OCR via Vision API disabled.")
except Exception as e:
     print(f"[FILE_PARSER] WARNING: Error importing Google Cloud Vision SDK types: {e}")
     GOOGLE_VISION_SDK_AVAILABLE = False


# --- Funciones de Parsing Específicas (Internas al Módulo) ---

async def _parse_pdf_content(file_path: str) -> str:
    """Parsea texto de un archivo PDF de forma asíncrona."""
    if not PYPDF2_AVAILABLE:
        return "[PDF parsing disabled: Library not available]"
    try:
        from fastapi.concurrency import run_in_threadpool # Para correr código síncrono

        def sync_parse_pdf():
            text_content = []
            with open(file_path, 'rb') as f:
                reader = PdfReader(f)
                if reader.is_encrypted:
                    try:
                        # Intentar desencriptar con contraseña vacía (algunos PDFs lo permiten)
                        if reader.decrypt("") == 0: # 0 for no success, 1 for success, 2 for success with owner pass
                             print(f"[FILE_PARSER] PDF {file_path} was encrypted but could not be decrypted with empty password.")
                             return "[Encrypted PDF: Decryption failed]"
                        else:
                             print(f"[FILE_PARSER] PDF {file_path} decrypted successfully with empty password.")
                    except Exception as e_decrypt:
                        print(f"[FILE_PARSER] Error decrypting PDF {file_path}: {e_decrypt}")
                        return f"[Encrypted PDF: Decryption error - {e_decrypt}]"

                for page_num in range(len(reader.pages)):
                    page = reader.pages[page_num]
                    page_text = page.extract_text()
                    if page_text:
                        text_content.append(page_text)
            return "\n".join(text_content)

        extracted_text = await run_in_threadpool(sync_parse_pdf)
        return extracted_text.strip() if extracted_text else "[No text extracted from PDF]"
    except Exception as e:
        print(f"[FILE_PARSER] ERROR parsing PDF {file_path}: {e}")
        traceback.print_exc()
        return f"[Error parsing PDF: {e}]"

async def _parse_docx_content(file_path: str) -> str:
    """Parsea texto de un archivo DOCX de forma asíncrona."""
    if not PYTHON_DOCX_AVAILABLE:
        return "[DOCX parsing disabled: Library not available]"
    try:
        from fastapi.concurrency import run_in_threadpool

        def sync_parse_docx():
            document = Document(file_path)
            return "\n".join([para.text for para in document.paragraphs if para.text])

        extracted_text = await run_in_threadpool(sync_parse_docx)
        return extracted_text.strip() if extracted_text else "[No text extracted from DOCX]"
    except Exception as e:
        print(f"[FILE_PARSER] ERROR parsing DOCX {file_path}: {e}")
        traceback.print_exc()
        return f"[Error parsing DOCX: {e}]"

async def _parse_image_content(file_path: str, vision_client: Optional[Any] = None) -> str:
    """
    Extrae texto de una imagen usando OCR (Google Vision API si está disponible).
    `vision_client` debe ser una instancia de `google.cloud.vision.ImageAnnotatorClient`.
    """
    if not PILLOW_AVAILABLE: # Pillow para manipulación básica o para pasar a Vision
        return "[Image processing disabled: Pillow library not available]"

    # Primero, un intento básico con Pillow para obtener metadatos
    try:
        with Image.open(file_path) as img:
            print(f"[FILE_PARSER] Image info: {img.format}, {img.mode}, {img.size} for file {file_path}")
    except Exception as e_pil:
        print(f"[FILE_PARSER] Pillow error opening image {file_path}: {e_pil}")
        return f"[Error opening image with Pillow: {e_pil}]"


    # Si Google Vision client está disponible y se proporciona, usarlo para OCR
    if GOOGLE_VISION_SDK_AVAILABLE and vision_client and VisionImage:
        print(f"[FILE_PARSER] Attempting OCR on image: {file_path} using Google Vision API.")
        try:
            from fastapi.concurrency import run_in_threadpool

            async def sync_vision_ocr():
                async with aiofiles.open(file_path, 'rb') as image_file:
                    content = await image_file.read()
                image = VisionImage(content=content) # type: ignore # VisionImage es el tipo importado
                response = vision_client.text_detection(image=image) # type: ignore # vision_client es Any

                if response.error.message:
                    raise Exception(f"Vision API Error: {response.error.message}")
                if response.text_annotations:
                    return response.text_annotations[0].description # El primer elemento es el texto completo
                return "[No text detected by Vision API]"

            # La llamada a la API de Vision puede ser síncrona o asíncrona dependiendo del cliente.
            # Asumimos que el cliente proporcionado podría ser síncrono.
            # No hay un método async `text_detection_async` estándar en el SDK base.
            extracted_text = await run_in_threadpool(sync_vision_ocr) # Ejecutar en threadpool si es síncrono

            print(f"[FILE_PARSER] OCR with Vision API finished. Extracted length: {len(extracted_text)}")
            return extracted_text

        except Exception as e_ocr:
            print(f"[FILE_PARSER] ERROR running OCR on image {file_path} with Vision API: {e_ocr}")
            traceback.print_exc()
            return f"[Error running OCR on image with Vision API: {e_ocr}]"
    else:
        return "[Image file: OCR not configured or Vision API client not available]"


async def _read_plain_text_file(file_path: str) -> str:
    """Lee contenido de un archivo de texto plano de forma asíncrona."""
    try:
        async with aiofiles.open(file_path, mode='r', encoding='utf-8', errors='ignore') as f:
            content = await f.read()
        return content.strip() if content else "[Empty text file]"
    except Exception as e:
        print(f"[FILE_PARSER] ERROR reading text file {file_path}: {e}")
        traceback.print_exc()
        return f"[Error reading text file: {e}]"


# --- Función Pública de Parsing ---

async def parse_file_content_to_text(file_path: str, vision_client_instance: Optional[Any] = None) -> str:
    """
    Parsea el contenido de un archivo (PDF, DOCX, Imagen, TXT) y devuelve el texto extraído.
    Requiere la ruta local del archivo.
    `vision_client_instance` es una instancia de `google.cloud.vision.ImageAnnotatorClient` si se usa OCR.
    """
    if not os.path.exists(file_path):
         print(f"[FILE_PARSER] ERROR: File not found at '{file_path}' for parsing.")
         return "[Error: File not found for parsing]"

    print(f"[FILE_PARSER] Attempting to parse file: {file_path}")
    extracted_text = "[Unsupported file type or parsing failed]" # Default message

    # Inferir tipo de archivo (MIME type es más fiable que la extensión)
    mime_type, _ = mimetypes.guess_type(file_path)
    file_extension = os.path.splitext(file_path)[1].lstrip('.').lower()
    print(f"[FILE_PARSER] File: '{os.path.basename(file_path)}', Extension: '{file_extension}', MIME: '{mime_type}'")


    try:
        if mime_type == "application/pdf" or file_extension == "pdf":
            extracted_text = await _parse_pdf_content(file_path)
        elif mime_type == "application/vnd.openxmlformats-officedocument.wordprocessingml.document" or file_extension == "docx":
            extracted_text = await _parse_docx_content(file_path)
        elif mime_type and mime_type.startswith("image/"):
            extracted_text = await _parse_image_content(file_path, vision_client=vision_client_instance)
        elif mime_type and mime_type.startswith("text/"): # Incluye .txt, .csv, .json, .md etc.
            extracted_text = await _read_plain_text_file(file_path)
        else:
            print(f"[FILE_PARSER] WARNING: Unsupported file type for direct text parsing: '{file_extension}' (MIME: {mime_type})")
            # Podrías intentar leerlo como binario y luego texto como último recurso, o simplemente marcar como no soportado.
            extracted_text = f"[Unsupported file type for text extraction: {file_extension or mime_type}]"

    except Exception as e:
        print(f"[FILE_PARSER] CRITICAL ERROR during file parsing dispatch for {file_path}: {e}")
        traceback.print_exc()
        extracted_text = f"[CRITICAL ERROR parsing file: {str(e)[:100]}]" # Limitar longitud del error

    # --- Post-parsing sanitation ---
    # Limpiar espacios múltiples y asegurar que no sea None
    cleaned_text = " ".join((extracted_text or "").split()).strip()


    # Considerar un límite en la longitud del texto extraído para evitar sobrecargar LLMs/Embeddings
    # Esto es un ejemplo, el límite real dependerá de los modelos y casos de uso.
    MAX_EXTRACTED_CHARS = 50000 # Ej. 50k caracteres
    if len(cleaned_text) > MAX_EXTRACTED_CHARS:
         print(f"[FILE_PARSER] Truncating extracted text from file '{os.path.basename(file_path)}' (original len: {len(cleaned_text)}, max: {MAX_EXTRACTED_CHARS}).")
         cleaned_text = cleaned_text[:MAX_EXTRACTED_CHARS] + "... (truncated)"

    print(f"[FILE_PARSER] Finished parsing '{os.path.basename(file_path)}'. Extracted/Cleaned length: {len(cleaned_text)}")
    return cleaned_text