# PLAP/plap-agents/document_processing/storage_handler.py
print("[STORAGE_HANDLER] Loading storage_handler.py")

import os
from typing import Optional, Any
import traceback
import aiofiles # Para manejo asíncrono de archivos locales
import uuid # Para generar IDs únicos para archivos
from fastapi import UploadFile # Para type hinting de file objects

# --- Intento de Importación del SDK de Google Cloud Storage ---
# La instancia del cliente GCS se pasará al constructor, aquí solo verificamos la disponibilidad del tipo.
GCS_SDK_AVAILABLE = False
GoogleStorageClient = None # Placeholder para el tipo google.cloud.storage.Client

try:
    print("[STORAGE_HANDLER] Attempting to import Google Cloud Storage SDK type...")
    from google.cloud import storage # Importar el módulo storage
    GoogleStorageClient = storage.Client # Guardar el tipo de cliente
    GCS_SDK_AVAILABLE = True
    print("[STORAGE_HANDLER] Google Cloud Storage SDK (Client type) imported.")
except ImportError:
    print("[STORAGE_HANDLER] WARNING: Google Cloud Storage SDK not found. GCS functionality will be disabled.")
    GCS_SDK_AVAILABLE = False
except Exception as e:
     print(f"[STORAGE_HANDLER] WARNING: Error importing Google Cloud Storage SDK types: {e}")
     traceback.print_exc()
     GCS_SDK_AVAILABLE = False


class StorageHandler:
    """Maneja el almacenamiento de archivos, localmente y/o en Google Cloud Storage."""

    def __init__(self,
                 local_uploads_dir: str,
                 gcs_bucket_name: Optional[str] = None,
                 gcs_client: Optional[Any] = None # Tipo: google.cloud.storage.Client
                 ):
        """
        Inicializa el manejador de almacenamiento.

        local_uploads_dir: Directorio local para guardar archivos temporalmente.
        gcs_bucket_name: Nombre del bucket de GCS si se usa Cloud Storage.
        gcs_client: Instancia inicializada del cliente GCS (google.cloud.storage.Client).
        """
        self._local_uploads_dir = local_uploads_dir
        self._gcs_bucket_name = gcs_bucket_name
        self._gcs_client: Optional[storage.Client] = None # type: ignore # Para el linter, se asigna abajo

        # Asignar el cliente GCS si el SDK está disponible y se proporcionó una instancia
        if GCS_SDK_AVAILABLE and isinstance(gcs_client, GoogleStorageClient): # type: ignore # Compara con el tipo importado
            self._gcs_client = cast(storage.Client, gcs_client)
            print(f"[StorageHandler] Google Cloud Storage client instance received.")
        elif gcs_bucket_name and (not GCS_SDK_AVAILABLE or not gcs_client):
            print(f"[StorageHandler] WARNING: GCS_BUCKET_NAME ('{gcs_bucket_name}') is set, but GCS SDK is not available or client instance was not provided. GCS functionality will be limited.")
            self._gcs_bucket_name = None # Deshabilitar GCS si el cliente no está listo

        # Asegurar que el directorio local de uploads exista
        try:
            os.makedirs(self._local_uploads_dir, exist_ok=True)
            print(f"[StorageHandler] Local uploads directory ensured at: {self._local_uploads_dir}")
        except Exception as e:
            print(f"[StorageHandler] CRITICAL ERROR: Could not create local uploads directory '{self._local_uploads_dir}': {e}")
            # Decide si esto debe impedir el funcionamiento (ej. lanzar error)
            # Por ahora, el servicio podría arrancar pero las subidas locales fallarán.

        if self._gcs_bucket_name and self._gcs_client:
            print(f"[StorageHandler] Google Cloud Storage configured. Bucket: '{self._gcs_bucket_name}'. Client: {'Available' if self._gcs_client else 'Not Available'}")
        else:
             print("[StorageHandler] Google Cloud Storage is not configured or client is not available.")


    async def save_uploaded_file_locally(
        self,
        file: UploadFile, # Objeto UploadFile de FastAPI
        user_id: str,
        # Opcional: session_id para organizar archivos por sesión dentro del usuario
        session_id: Optional[str] = None
    ) -> Optional[str]:
        """
        Guarda un archivo UploadFile de FastAPI localmente en un subdirectorio por usuario (y opcionalmente sesión).
        Retorna la ruta completa del archivo guardado localmente o None si falla.
        Genera un nombre de archivo único para evitar colisiones y problemas de seguridad.
        """
        if not file.filename:
            print("[StorageHandler] save_uploaded_file_locally: No filename provided in UploadFile.")
            return None

        # Crear subdirectorio para el usuario (y sesión si se proporciona)
        user_specific_dir = os.path.join(self._local_uploads_dir, user_id)
        if session_id:
            user_specific_dir = os.path.join(user_specific_dir, session_id)

        try:
            os.makedirs(user_specific_dir, exist_ok=True)
        except Exception as e:
            print(f"[StorageHandler] ERROR creating user directory '{user_specific_dir}': {e}")
            return None

        # Sanitizar el nombre de archivo original para obtener una base segura
        original_filename_base = os.path.basename(file.filename)
        sanitized_base = "".join([c for c in original_filename_base if c.isalnum() or c in ('.', '_', '-')]).rstrip('.')
        if not sanitized_base: # Si el nombre queda vacío después de sanitizar
             sanitized_base = "uploaded_file"
        file_extension = os.path.splitext(sanitized_base)[1] # Obtener extensión del nombre sanitizado

        # Crear un nombre de archivo único usando UUID y la extensión original
        unique_filename = f"{uuid.uuid4().hex}{file_extension}"
        file_location = os.path.join(user_specific_dir, unique_filename)

        print(f"[StorageHandler] Attempting to save '{file.filename}' as '{unique_filename}' to local path: '{file_location}' for user '{user_id}'.")

        try:
            # Guardar el archivo de forma asíncrona en chunks
            async with aiofiles.open(file_location, "wb") as file_object:
                await file.seek(0) # Asegurar que el puntero del archivo esté al inicio
                chunk_size = 8192 # Tamaño de chunk razonable (8KB)
                while chunk := await file.read(chunk_size):
                    await file_object.write(chunk)
            print(f"[StorageHandler] File '{unique_filename}' saved successfully locally.")
            return file_location # Retornar la ruta del archivo guardado

        except Exception as e:
            print(f"[StorageHandler] ERROR saving file '{unique_filename}' locally for user '{user_id}': {e}")
            traceback.print_exc()
            # Intentar limpiar el archivo si la escritura falló
            if os.path.exists(file_location):
                 try: os.remove(file_location); print(f"[StorageHandler] Cleaned up incomplete file: {file_location}")
                 except: pass
            return None


    async def upload_local_file_to_gcs(
        self,
        local_file_path: str,
        user_id: str, # Para organizar en GCS
        destination_blob_name: Optional[str] = None # Nombre específico en GCS o se genera uno
    ) -> Optional[str]:
        """
        Sube un archivo desde una ruta local a Google Cloud Storage.
        Retorna la URI de GCS ('gs://bucket-name/blob-name') o None si falla.
        """
        if not self._gcs_client or not self._gcs_bucket_name: # GCS_SDK_AVAILABLE ya chequeado en __init__
            print("[StorageHandler] upload_local_file_to_gcs: GCS client or bucket name not configured.")
            return None
        if not os.path.exists(local_file_path):
             print(f"[StorageHandler] upload_local_file_to_gcs: Local file not found at '{local_file_path}'.")
             return None

        try:
            from fastapi.concurrency import run_in_threadpool # Para llamadas síncronas del SDK

            bucket = await run_in_threadpool(self._gcs_client.get_bucket, self._gcs_bucket_name)

            # Definir el nombre del blob de destino en GCS
            if not destination_blob_name:
                original_filename = os.path.basename(local_file_path)
                # Crear una ruta estructurada en GCS, ej: user_uploads/user_id/original_filename_uuid
                destination_blob_name = f"user_uploads/{user_id}/{uuid.uuid4().hex}_{original_filename}"

            blob = bucket.blob(destination_blob_name)

            print(f"[StorageHandler] Uploading local file '{local_file_path}' to GCS: gs://{self._gcs_bucket_name}/{destination_blob_name}")
            await run_in_threadpool(blob.upload_from_filename, local_file_path)

            gcs_uri = f"gs://{self._gcs_bucket_name}/{destination_blob_name}"
            print(f"[StorageHandler] File uploaded successfully to GCS: {gcs_uri}")
            return gcs_uri

        except Exception as e:
            print(f"[StorageHandler] ERROR uploading local file '{local_file_path}' to GCS: {e}")
            traceback.print_exc()
            return None


    async def download_file_from_gcs(self, gcs_uri: str, local_destination_path: str) -> Optional[str]:
        """
        Descarga un archivo desde una URI de Google Cloud Storage a una ruta local.
        Retorna la ruta local del archivo descargado o None si falla.
        """
        if not self._gcs_client: # GCS_SDK_AVAILABLE ya chequeado en __init__
             print("[StorageHandler] download_file_from_gcs: GCS client not available.")
             return None

        try:
            if not gcs_uri.startswith("gs://"):
                print(f"[StorageHandler] download_file_from_gcs: Invalid GCS URI format: {gcs_uri}")
                return None

            parts = gcs_uri[5:].split("/", 1)
            if len(parts) < 2:
                print(f"[StorageHandler] download_file_from_gcs: Invalid GCS URI (missing blob name): {gcs_uri}")
                return None
            bucket_name_from_uri = parts[0]
            blob_name = parts[1]

            # Opcional: Validar que el bucket de la URI coincida con el configurado si es una política
            # if self._gcs_bucket_name and bucket_name_from_uri != self._gcs_bucket_name:
            #     print(f"[StorageHandler] download_file_from_gcs: URI bucket '{bucket_name_from_uri}' does not match configured bucket '{self._gcs_bucket_name}'. Download denied.")
            #     return None
            
            bucket_to_use_name = self._gcs_bucket_name or bucket_name_from_uri # Usar configurado si existe, sino el de la URI
            if not bucket_to_use_name:
                 print(f"[StorageHandler] download_file_from_gcs: No bucket name determined for GCS URI: {gcs_uri}")
                 return None


            from fastapi.concurrency import run_in_threadpool # Para llamadas síncronas del SDK
            bucket = await run_in_threadpool(self._gcs_client.get_bucket, bucket_to_use_name)
            blob = bucket.blob(blob_name)

            # Asegurar que el directorio de destino local exista
            destination_dir = os.path.dirname(local_destination_path)
            if destination_dir: # Solo crear si la ruta tiene un directorio
                 os.makedirs(destination_dir, exist_ok=True)

            print(f"[StorageHandler] Downloading GCS file '{gcs_uri}' to local path '{local_destination_path}'.")
            await run_in_threadpool(blob.download_to_filename, local_destination_path)

            print(f"[StorageHandler] File downloaded successfully to '{local_destination_path}'.")
            return local_destination_path

        except Exception as e:
            print(f"[StorageHandler] ERROR downloading file '{gcs_uri}' from GCS: {e}")
            traceback.print_exc()
            if os.path.exists(local_destination_path): # Limpiar archivo parcial
                 try: os.remove(local_destination_path)
                 except: pass
            return None


    async def close(self):
        """Cierra recursos si es necesario (ej. cliente GCS si se gestiona aquí)."""
        # El cliente GCS (`google.cloud.storage.Client`) no tiene un método `close()` explícito
        # ya que maneja las conexiones internamente o a través de la sesión HTTP subyacente.
        print("[StorageHandler] Shutdown logic (if any). GCS client usually doesn't require explicit close.")
        pass

# Nota:
# La instancia de StorageHandler se creará en `plap-agents/main.py` en el evento `startup`,
# pasándole la instancia del cliente GCS si está configurado.