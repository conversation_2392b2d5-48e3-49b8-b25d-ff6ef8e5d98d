# PLAP/plap-agents/main.py (MODULARIZADO Y CORREGIDO v8 - Upload Param Order Final Attempt)
print("--- [AGENTS_MAIN.PY - MODULAR INTEGRATION v8 - Upload Param Order Fix Attempt] STARTING MODULE EXECUTION ---")

from fastapi import (
    Fast<PERSON><PERSON>, APIRouter, HTTPException, status, Header,
    File, UploadFile, Depends, Request, Response
)
from fastapi.middleware.cors import CORSMiddleware
from fastapi.responses import JSONResponse
from typing import Dict, Any, Optional, List, Annotated, cast, Union
import traceback
import os
import asyncio
import importlib

# --- Importar Módulos Locales Estructurados ---
print("--- [AGENTS_MAIN.PY - MODULAR INTEGRATION v8] Attempting structured local imports... ---")
try:
    from config import settings
    from schemas.agent_schemas import (
        <PERSON>Type, AgentExecutionRequest, AgentExecutionResponse,
        SuggestionRequest, SuggestionResponse,
        EmbeddingRequest, EmbeddingResponse,
        FileUploadResponse, GeneratorAgentInput, GeneratorAgentOutput
    )
    from clients.vertex_ai_client import VertexAIClient
    from clients.zep_client_wrapper import ZepClientWrapper
    from clients.qdrant_client_wrapper import QdrantClientWrapper
    from clients.internal_services_client import InternalServicesClient
    from document_processing.storage_handler import StorageHandler
    from agents.agent_factory import AgentFactory
    from agents.suggester_agent import SuggesterAgent

    import vertexai
    try:
        storage_module = importlib.import_module('google.cloud.storage')
        storage = storage_module # type: ignore
        GCS_SDK_AVAILABLE_MAIN = True
        print("[AGENTS_MAIN] Google Cloud Storage SDK imported successfully.")
    except ImportError:
        storage = None # type: ignore
        GCS_SDK_AVAILABLE_MAIN = False
        print("[AGENTS_MAIN] WARNING: Google Cloud Storage SDK not found. GCS functionality will be limited.")
    print("--- [AGENTS_MAIN.PY - MODULAR INTEGRATION v8] Structured local imports successful. ---")
except ImportError as e:
    print(f"--- [AGENTS_MAIN.PY - MODULAR INTEGRATION v8] CRITICAL ERROR during structured local imports: {e} ---"); traceback.print_exc()
    raise SystemExit(f"Failed to import essential modules. Agents Service cannot start: {e}")
except Exception as e_general_import:
    print(f"--- [AGENTS_MAIN.PY - MODULAR INTEGRATION v8] UNEXPECTED CRITICAL ERROR during imports: {e_general_import} ---"); traceback.print_exc()
    raise SystemExit(f"Unexpected error during imports. Agents Service cannot start: {e_general_import}")

# --- Variables Globales para Clientes y Fábrica (Inicializadas en Startup) ---
_vertex_client: Optional[VertexAIClient] = None
_zep_client_wrapper: Optional[ZepClientWrapper] = None
_qdrant_client: Optional[QdrantClientWrapper] = None
_storage_handler: Optional[StorageHandler] = None
_internal_services_client: Optional[InternalServicesClient] = None
_agent_factory: Optional[AgentFactory] = None
_gcs_client_instance: Optional[Any] = None

# --- Configuración de la Aplicación FastAPI ---
app = FastAPI(
    title=settings.SERVICE_TITLE,
    version=settings.SERVICE_VERSION,
    description=settings.SERVICE_DESCRIPTION,
)
app.add_middleware(
    CORSMiddleware, allow_origins=settings.CORS_ORIGINS, allow_credentials=True,
    allow_methods=["*"], allow_headers=["*"])

# --- Evento Startup: Inicializar Clientes y Fábrica ---
_startup_event_executed_flag = False
@app.on_event("startup")
async def startup_event():
    global _vertex_client, _zep_client_wrapper, _qdrant_client, _storage_handler, \
           _internal_services_client, _agent_factory, _gcs_client_instance, _startup_event_executed_flag
    if _startup_event_executed_flag: print("[AGENTS_MAIN Startup] Startup event already executed."); return
    _startup_event_executed_flag = True
    print("[AGENTS_MAIN Startup] Starting service initialization...")
    try: os.makedirs(settings.LOCAL_UPLOADS_DIR, exist_ok=True); print(f"[AGENTS_MAIN Startup] Ensured local uploads directory: {settings.LOCAL_UPLOADS_DIR}")
    except Exception as e: print(f"[AGENTS_MAIN Startup] WARNING: Could not create local uploads dir '{settings.LOCAL_UPLOADS_DIR}': {e}")
    if settings.GCS_BUCKET_NAME and GCS_SDK_AVAILABLE_MAIN and storage:
        try: _gcs_client_instance = storage.Client(project=settings.GOOGLE_CLOUD_PROJECT_ID); print("[AGENTS_MAIN Startup] Google Cloud Storage client initialized.")
        except Exception as e_gcs: print(f"[AGENTS_MAIN Startup] ERROR GCS client init: {e_gcs}"); _gcs_client_instance = None
    elif settings.GCS_BUCKET_NAME: print("[AGENTS_MAIN Startup] GCS_BUCKET_NAME set, but SDK/Client type missing.")
    if VertexAIClient and vertexai and settings.GOOGLE_CLOUD_PROJECT_ID and settings.VERTEX_AI_LOCATION:
        try:
            if not hasattr(vertexai, '_did_init') or not vertexai._did_init: # type: ignore
                 vertexai.init(project=settings.GOOGLE_CLOUD_PROJECT_ID, location=settings.VERTEX_AI_LOCATION); vertexai._did_init = True # type: ignore
                 print("[AGENTS_MAIN Startup] Vertex AI SDK initialized globally.")
            else: print("[AGENTS_MAIN Startup] Vertex AI SDK already initialized globally.")
            _vertex_client = VertexAIClient(project_id=settings.GOOGLE_CLOUD_PROJECT_ID, location=settings.VERTEX_AI_LOCATION)
            print("[AGENTS_MAIN Startup] VertexAIClient wrapper initialized.")
        except Exception as e_vertex: print(f"[AGENTS_MAIN Startup] ERROR initializing Vertex AI: {e_vertex}"); traceback.print_exc(); _vertex_client = None
    else:
        print("[AGENTS_MAIN Startup] WARNING: Vertex AI not fully configured/SDK missing.");
        if VertexAIClient: _vertex_client = VertexAIClient(project_id=None, location=None)
    if ZepClientWrapper and settings.ZEP_API_KEY:
        _zep_client_wrapper = ZepClientWrapper(api_key=settings.ZEP_API_KEY, api_url=settings.ZEP_API_URL)
        if _zep_client_wrapper.is_available: print("[AGENTS_MAIN Startup] ZepClientWrapper initialized and available.")
        else: print("[AGENTS_MAIN Startup] WARNING: ZepClientWrapper initialized but may not be available.")
    else:
        print("[AGENTS_MAIN Startup] WARNING: ZepClientWrapper or ZEP_API_KEY missing.");
        if ZepClientWrapper: _zep_client_wrapper = ZepClientWrapper(api_key=None, api_url=None)
    if QdrantClientWrapper and settings.QDRANT_URL:
        _qdrant_client = QdrantClientWrapper(url=settings.QDRANT_URL, api_key=settings.QDRANT_API_KEY)
        if _qdrant_client.is_available:
            print("[AGENTS_MAIN Startup] QdrantClientWrapper initialized and available.")
            from fastapi.concurrency import run_in_threadpool
            async def _ensure_q_collections():
                print("[AGENTS_MAIN Startup] Ensuring Qdrant collections...")
                if settings.TEXT_EMBEDDING_DIMENSION > 0 and _qdrant_client:
                    for coll_name in [settings.QDRANT_DOCS_COLLECTION, settings.QDRANT_PROMPTS_COLLECTION, settings.QDRANT_KB_COLLECTION, settings.QDRANT_EL_COLLECTION]:
                        try: await run_in_threadpool(_qdrant_client.ensure_collection_exists, coll_name, settings.TEXT_EMBEDDING_DIMENSION)
                        except Exception as e_q_coll: print(f"[AGENTS_MAIN Startup] Error ensuring Qdrant collection '{coll_name}': {e_q_coll}")
                    print("[AGENTS_MAIN Startup] Qdrant collections ensuring process finished.")
            asyncio.create_task(_ensure_q_collections())
        else: print("[AGENTS_MAIN Startup] WARNING: QdrantClientWrapper initialized but may not be available.")
    else:
        print("[AGENTS_MAIN Startup] WARNING: QdrantClientWrapper or QDRANT_URL missing.");
        if QdrantClientWrapper: _qdrant_client = QdrantClientWrapper(url="http://dummy:6333")
    if StorageHandler: _storage_handler = StorageHandler(settings.LOCAL_UPLOADS_DIR, settings.GCS_BUCKET_NAME, _gcs_client_instance); print("[AGENTS_MAIN Startup] StorageHandler initialized.")
    else: print("[AGENTS_MAIN Startup] WARNING: StorageHandler class not imported.")
    if InternalServicesClient: _internal_services_client = InternalServicesClient(user_service_url=settings.USER_SERVICE_URL, prompt_library_service_url=settings.PROMPT_LIBRARY_SERVICE_URL); print("[AGENTS_MAIN Startup] InternalServicesClient initialized.")
    else: print("[AGENTS_MAIN Startup] WARNING: InternalServicesClient class not imported.")
    if AgentFactory:
        _agent_factory = AgentFactory(settings, _vertex_client, _zep_client_wrapper, _qdrant_client, _storage_handler, _internal_services_client)
        print("[AGENTS_MAIN Startup] AgentFactory initialized.")
    else: print("[AGENTS_MAIN Startup] CRITICAL WARNING: AgentFactory class not imported.")
    print("[AGENTS_MAIN Startup] Startup event finished.")

# --- Dependencias de FastAPI ---
def get_agent_factory_dependency() -> AgentFactory:
    if _agent_factory is None: raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Agent factory not initialized.")
    return _agent_factory
def get_storage_handler_dependency() -> StorageHandler:
    if _storage_handler is None: raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Storage handler not initialized.")
    return _storage_handler

AnnotatedAgentFactory = Annotated[AgentFactory, Depends(get_agent_factory_dependency)]
# AnnotatedStorageHandler = Annotated[StorageHandler, Depends(get_storage_handler_dependency)] # No se usa así en el endpoint corregido
AnnotatedVertexAIClient = Annotated[Optional[VertexAIClient], Depends(lambda: _vertex_client)]

# --- Routers y Endpoints ---
# (El código de los routers agents_router, generator_specific_router, suggester_router, embeddings_router se mantiene igual que antes)
# ... (COPIAR Y PEGAR EL CÓDIGO DE ESOS ROUTERS AQUÍ EXACTAMENTE COMO ESTABA EN LA RESPUESTA ANTERIOR) ...
# Endpoint Genérico para Ejecución de Agentes
agents_router = APIRouter(prefix="/api/v1/agents", tags=["Agent Execution (Generic)"])
@agents_router.post("/process", response_model=AgentExecutionResponse)
async def execute_agent_generic_endpoint(request_body: AgentExecutionRequest, agent_factory_instance: AnnotatedAgentFactory, x_user_id: Optional[str] = Header(None, alias="X-User-ID")):
    user_id_to_use = x_user_id or request_body.user_id
    if not user_id_to_use: raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="User ID required.")
    print(f"[AGENTS_MAIN /process] Agent: {request_body.agent_type}, User: {user_id_to_use}, Session: {request_body.session_id}")
    try:
        agent_instance = agent_factory_instance.get_agent(request_body.agent_type)
        return await agent_instance.execute(user_id_to_use, request_body.session_id, request_body.input_data)
    except ValueError as ve: raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except Exception as e: traceback.print_exc(); raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error executing agent: {str(e)}")
app.include_router(agents_router)

generator_specific_router = APIRouter(prefix="/agents/generator", tags=["Generator Agent (Specific Legacy)"])
@generator_specific_router.post("/process_idea", response_model=GeneratorAgentOutput)
async def generator_process_idea_specific_endpoint(data: GeneratorAgentInput, agent_factory_instance: AnnotatedAgentFactory, x_user_id: str = Header(..., alias="X-User-ID")):
    print(f"Endpoint /agents/generator/process_idea: User: {x_user_id}, Idea: '{data.prompt_idea}', Zep Session: {getattr(data, 'zep_session_id', 'N/A')}")
    try:
        input_data_for_agent = data.model_dump(exclude_none=True)
        current_zep_session_id = getattr(data, 'zep_session_id', None)
        generator_agent = cast(GeneratorAgent, agent_factory_instance.get_agent(AgentType.GENERATOR))
        response_from_agent: AgentExecutionResponse = await generator_agent.execute(x_user_id, current_zep_session_id, input_data_for_agent)
        if response_from_agent.status.startswith("success"):
            if "generated_prompt" in response_from_agent.output_data:
                return GeneratorAgentOutput(generated_prompt=response_from_agent.output_data["generated_prompt"], status=response_from_agent.status, agent_name=f"GeneratorAgent_{settings.SERVICE_VERSION}", model_used=response_from_agent.model_used)
            raise HTTPException(status_code=500, detail="Generator agent did not return expected 'generated_prompt'.")
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR if response_from_agent.status == "error" else status.HTTP_400_BAD_REQUEST, detail=response_from_agent.error_message or "Generator agent execution failed.")
    except ValueError as ve: raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail=str(ve))
    except Exception as e: traceback.print_exc(); raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error processing prompt idea: {str(e)}")
app.include_router(generator_specific_router)

suggester_router = APIRouter(prefix="/api/v1/agents/suggest", tags=["Agent Actions (Specific)"])
@suggester_router.post("/", response_model=List[SuggestionResponse])
async def get_suggestions_suggester_endpoint(request_data: SuggestionRequest, agent_factory_instance: AnnotatedAgentFactory, x_user_id: str = Header(..., alias="X-User-ID"), x_zep_session_id: Optional[str] = Header(None, alias="X-Zep-Session-ID")):
    print(f"Endpoint /api/v1/agents/suggest: User: {x_user_id}, Session: {x_zep_session_id}, Text: '{request_data.current_prompt_text[:50]}...'")
    try:
        suggester_agent_instance = cast(SuggesterAgent, agent_factory_instance.get_agent(AgentType.SUGGESTER))
        return await suggester_agent_instance.generate_suggestions(x_user_id, x_zep_session_id, request_data.current_prompt_text, request_data.context_text)
    except ValueError as ve: raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=str(ve))
    except Exception as e: traceback.print_exc(); raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Error getting suggestions: {str(e)}")
app.include_router(suggester_router)

embeddings_router = APIRouter(prefix="/api/v1/utils/embeddings", tags=["Utilities"])
@embeddings_router.post("/generate", response_model=EmbeddingResponse)
async def generate_embedding_utility_endpoint(request_data: EmbeddingRequest, vertex_client_instance: AnnotatedVertexAIClient):
    print(f"Endpoint /embeddings/generate: Text len: {len(request_data.text)}")
    if not vertex_client_instance or not vertex_client_instance.is_available: raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail="Embedding service (Vertex AI) unavailable.")
    try:
        embeddings = await vertex_client_instance.generate_embedding([request_data.text], settings.TEXT_EMBEDDING_MODEL_NAME)
        if not embeddings or not embeddings[0]: raise HTTPException(status_code=500, detail="Failed to generate embedding vector.")
        return EmbeddingResponse(embedding=embeddings[0], model_used=settings.TEXT_EMBEDDING_MODEL_NAME)
    except Exception as e: traceback.print_exc(); raise HTTPException(status_code=500, detail=f"Error generating embedding: {str(e)}")
app.include_router(embeddings_router)


# --- Endpoint para Carga de Archivos de Contexto (CON CORRECCIÓN DE ORDEN DE PARÁMETROS) ---
upload_router = APIRouter(prefix="/api/v1/context", tags=["Context Utilities"])
@upload_router.post("/upload", response_model=FileUploadResponse)
async def upload_context_file_internal_endpoint(
    # CORRECTED ORDER: Path params (none here), then Query/Header/Cookie WITHOUT defaults,
    # then Dependencies, then Body/File params, then Query/Header/Cookie WITH defaults.
    user_id: str = Header(..., alias="X-User-ID", description="ID del usuario propietario del archivo."),
    storage_handler_instance: StorageHandler = Depends(get_storage_handler_dependency), # Explicit Depends
    file: UploadFile = File(..., description="Archivo a subir para contexto."),
    session_id: Optional[str] = Header(None, alias="X-Zep-Session-ID", description="ID de sesión Zep opcional para asociar el archivo.")
):
    print(f"[AGENTS_MAIN /context/upload] File upload request for user: {user_id}, session: {session_id}, filename: {file.filename}")
    if not file.filename:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="No filename provided.")

    local_file_path = await storage_handler_instance.save_uploaded_file_locally(file, user_id, session_id)
    if not local_file_path:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail=f"Failed to save uploaded file '{file.filename}'.")

    gcs_uri_result: Optional[str] = None
    if settings.GCS_BUCKET_NAME and _gcs_client_instance and hasattr(storage_handler_instance, '_gcs_client') and storage_handler_instance._gcs_client: # type: ignore
        gcs_uri_result = await storage_handler_instance.upload_local_file_to_gcs(local_file_path, user_id)
        if not gcs_uri_result:
            print(f"WARNING: Failed to upload to GCS: {local_file_path}")

    return FileUploadResponse(
        info=f"File '{file.filename}' received and stored.",
        file_id=os.path.basename(local_file_path),
        filename=file.filename,
        content_type=file.content_type,
        size_bytes=file.size if file.size is not None else 0,
        local_path=local_file_path,
        gcs_uri=gcs_uri_result,
        status="uploaded_pending_processing"
    )
app.include_router(upload_router)


# --- Endpoint de Salud ---
@app.get("/health", tags=["Health"])
async def health_check():
    health_status: Dict[str, Any] = {"status": "healthy", "service_name": settings.SERVICE_TITLE, "service_version": settings.SERVICE_VERSION, "timestamp": datetime.now(timezone.utc).isoformat()}
    health_status["dependencies"] = {
        "vertex_ai_client": "Available" if _vertex_client and _vertex_client.is_available else "Unavailable/Not Initialized",
        "zep_client_wrapper": "Available" if _zep_client_wrapper and _zep_client_wrapper.is_available else "Unavailable/Not Initialized",
        "qdrant_client": "Available" if _qdrant_client and _qdrant_client.is_available else "Unavailable/Not Initialized",
        "storage_handler": "Available" if _storage_handler else "Unavailable/Not Initialized",
        "internal_services_client": "Available" if _internal_services_client else "Unavailable/Not Initialized",
        "agent_factory": "Available" if _agent_factory else "Unavailable/Not Initialized",
    }
    is_ready = all([_vertex_client and _vertex_client.is_available, _agent_factory is not None])
    health_status["service_ready"] = is_ready
    if not is_ready:
        print(f"[AGENTS_MAIN /health] Service is NOT fully ready. Status: {health_status}")
        return JSONResponse(content=health_status, status_code=status.HTTP_503_SERVICE_UNAVAILABLE)
    return health_status

# --- Evento Shutdown ---
@app.on_event("shutdown")
async def shutdown_event():
    print("[AGENTS_MAIN Shutdown] Starting shutdown event...")
    if _zep_client_wrapper: await _zep_client_wrapper.close()
    if _qdrant_client: await _qdrant_client.close()
    if _internal_services_client: await _internal_services_client.close()
    if _storage_handler: await _storage_handler.close()
    print("[AGENTS_MAIN Shutdown] Shutdown event finished.")

print(f"[AGENTS_MAIN.PY - MODULAR INTEGRATION v8] Module loaded. App: '{app.title}' v{app.version}. Awaiting Uvicorn start...")