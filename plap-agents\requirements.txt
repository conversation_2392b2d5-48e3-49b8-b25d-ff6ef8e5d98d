# Framework web (si los agentes exponen endpoints internos) y servidor
fastapi==0.111.0
uvicorn[standard]==0.30.1

# Validación de datos y tipos
pydantic==2.7.1

# Cliente HTTP para llamar a otros servicios internos o APIs externas (si no usas librerías cliente específicas)
httpx==0.27.0

# Base de datos relacional (si los agentes necesitan leer/escribir en Postgres directamente)
sqlalchemy==2.0.30
psycopg2-binary==2.9.9

# Cliente para Base de Datos Vectorial Qdrant
qdrant-client==1.9.0 # O la versión compatible más reciente

# Cliente para Zep Cloud
zep-cloud==2.12.3 # O la versión compatible más reciente

# Procesamiento de Archivos Adjuntos (PDF, DOCX, Imágenes)
PyPDF2==3.0.1 # O pypdf si prefieres esa librería
python-docx==1.1.0
Pillow==10.3.0 # Para imágenes

# Librerías Cliente de Google Cloud Platform
google-cloud-aiplatform==1.57.0 # Para Vertex AI (Gemini, Embeddings) - Usa una versión reciente y estable
google-cloud-storage==2.11.0 # Para interactuar con Cloud Storage (leer adjuntos)
# google-cloud-vision # Descomenta si la usas activamente para OCR

# Para leer variables de .env directamente en el script (útil para debug local)
python-dotenv==1.0.0

# Para el endpoint /upload-context si procesas UploadFile directamente en FastAPI
python-multipart==0.0.9

# --- AÑADIR ESTA LÍNEA ---
aiofiles==23.2.1 # Para operaciones asíncronas de archivos
# --- FIN DE LÍNEA AÑADIDA ---