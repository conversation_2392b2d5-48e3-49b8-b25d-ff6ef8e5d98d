# PLAP/plap-agents/schemas/agent_schemas.py
print("[AGENTS_SCHEMAS] Loading agent_schemas.py (v2 - ensuring all output data schemas)")

from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any, Type, cast, Union # Asegurar Union
from enum import Enum
import uuid
from datetime import datetime, timezone

# --- Intento de Importación de Schemas/Enums Compartidos ---
_SHARED_SCHEMAS_LOADED = False
LLMModelTypeShared: Type[Enum] # Type hint
WorkflowTypeShared: Type[Enum] # Type hint
PromptSchemaShared: Optional[Type[BaseModel]] = None # Type hint

try:
    print("[AGENTS_SCHEMAS] Attempting to import shared schemas from 'schemas.prompt_schemas'...")
    from schemas.prompt_schemas import (
        LLMModelType_v0_9_2 as ImportedLLMModelType,
        WorkflowType_v0_9_2 as ImportedWorkflowType,
        Prompt_v0_9_2 as ImportedPrompt
    )
    LLMModelTypeShared = ImportedLLMModelType
    WorkflowTypeShared = ImportedWorkflowType
    PromptSchemaShared = ImportedPrompt
    _SHARED_SCHEMAS_LOADED = True
    print("[AGENTS_SCHEMAS] Shared schemas imported successfully from schemas.prompt_schemas.")
except ImportError as e1:
    print(f"[AGENTS_SCHEMAS] WARNING: Failed import from 'schemas.prompt_schemas': {e1}. Trying 'plap_shared_models.schemas.prompt_schemas'...")
    try:
        from plap_shared_models.schemas.prompt_schemas import (
            LLMModelType_v0_9_2 as ImportedLLMModelType2,
            WorkflowType_v0_9_2 as ImportedWorkflowType2,
            Prompt_v0_9_2 as ImportedPrompt2
        )
        LLMModelTypeShared = ImportedLLMModelType2
        WorkflowTypeShared = ImportedWorkflowType2
        PromptSchemaShared = ImportedPrompt2
        _SHARED_SCHEMAS_LOADED = True
        print("[AGENTS_SCHEMAS] Shared schemas imported successfully from plap_shared_models.schemas.prompt_schemas.")
    except ImportError as e2:
        print(f"[AGENTS_SCHEMAS] CRITICAL WARNING: Failed to import shared schemas from any path: {e2}. Using local fallbacks.")

if not _SHARED_SCHEMAS_LOADED:
    print("[AGENTS_SCHEMAS] Defining local fallback Enums and Schemas.")
    class _LLMModelTypeFallback(str, Enum):
        GEMINI_FLASH_MODEL_NAME = "gemini-1.5-flash-001"
        GEMINI_PRO_MODEL_NAME = "gemini-1.5-pro-preview-0409"
        GEMINI_PRO_VISION_MODEL_NAME = "gemini-1.0-pro-vision-001"
        TEXT_EMBEDDING_MODEL_NAME = "textembedding-gecko@003"
    LLMModelTypeShared = cast(Type[Enum], _LLMModelTypeFallback)

    class _WorkflowTypeFallback(str, Enum):
        DEFAULT = "default"; GUIDED = "guided"; EDITOR = "editor"
    WorkflowTypeShared = cast(Type[Enum], _WorkflowTypeFallback)

    class _PromptSchemaFallback(BaseModel):
        id: uuid.UUID = Field(default_factory=uuid.uuid4)
        user_id: uuid.UUID = Field(default_factory=uuid.uuid4)
        name: str = "Placeholder Prompt"
        final_text: Optional[str] = None
        created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
        model_config = ConfigDict(from_attributes=True, protected_namespaces=())
    PromptSchemaShared = _PromptSchemaFallback
else:
    LLMModelTypeShared = cast(Type[Enum], LLMModelTypeShared)
    WorkflowTypeShared = cast(Type[Enum], WorkflowTypeShared)
    PromptSchemaShared = cast(Type[BaseModel], PromptSchemaShared)

class AgentType(str, Enum):
    EXTRACTOR = "extractor"; GENERATOR = "generator"; COMPLIANCE = "compliance"
    REVIEWER = "reviewer"; RECOMMENDER = "recommender"; SUGGESTER = "suggester"

class BaseProcessingRequest(BaseModel):
    user_id: str = Field(..., description="ID del usuario (UUID como string).")
    session_id: Optional[str] = Field(None, description="ID de la sesión de Zep (UUID como string).")
    model_config = ConfigDict(protected_namespaces=(), extra='allow')

class AgentExecutionRequest(BaseProcessingRequest):
    agent_type: AgentType = Field(..., description="Tipo del agente a ejecutar.")
    input_data: Dict[str, Any] = Field(..., description="Datos de entrada para el agente.")
    model_config = ConfigDict(protected_namespaces=(), use_enum_values=True, extra='allow')

class AgentExecutionResponse(BaseModel):
    agent_used: AgentType = Field(..., description="Tipo del agente que se ejecutó.")
    output_data: Dict[str, Any] = Field(..., description="Datos de salida del agente.")
    model_used: Optional[str] = Field(None, description="Modelo LLM/Embedding usado.")
    status: str = Field("success", description="Estado de la ejecución.")
    error_message: Optional[str] = Field(None, description="Mensaje de error si aplica.")
    model_config = ConfigDict(protected_namespaces=(), use_enum_values=True)

# --- Schemas Específicos para Input y Output Data de cada Agente ---

# Generator Agent
class GeneratorAgentInput(BaseModel):
    prompt_idea: str = Field(..., description="Idea inicial del prompt.")
    target_llm_model: Optional[str] = Field(None, description="Modelo LLM objetivo.")
    zep_session_id: Optional[str] = Field(None, description="ID de sesión Zep (pasado por Orchestrator/API GW).") # Añadido aquí para consistencia si el endpoint específico lo usa
    model_config = ConfigDict(protected_namespaces=())

class GeneratorAgentOutput(BaseModel): # Para el `output_data` de AgentExecutionResponse
    generated_prompt: str = Field(..., description="Prompt generado.")
    model_config = ConfigDict(protected_namespaces=())

# Extractor Agent
class ExtractorAgentInput(BaseModel):
    text_to_analyze: Optional[str] = Field(None, description="Texto a analizar.")
    file_id: Optional[str] = Field(None, description="ID/nombre del archivo subido.")
    extraction_spec: Dict[str, Any] = Field(..., description="Especificación de extracción.")
    model_config = ConfigDict(protected_namespaces=())

class ExtractorAgentOutput(BaseModel): # Para el `output_data`
    extracted_data: Dict[str, Any] = Field(..., description="Datos extraídos.")
    summary: Optional[str] = Field(None, description="Resumen del contenido.")
    sensitive_content_detected: bool = Field(False, description="PII detectado.")
    indexed_qdrant_point_id: Optional[str] = Field(None, description="ID en Qdrant si se indexó.")
    model_config = ConfigDict(protected_namespaces=())

# Compliance Agent
class ComplianceAgentInput(BaseModel):
    text_to_check: str = Field(..., description="Texto a verificar.")
    rules_to_apply: Optional[List[str]] = Field(None, description="Reglas a aplicar.")
    model_config = ConfigDict(protected_namespaces=())

class ComplianceViolationDetail(BaseModel):
    rule: str = Field(..., description="Regla violada.")
    description: Optional[str] = Field(None, description="Descripción de la violación.")
    match: Optional[str] = Field(None, description="Texto coincidente.")
    severity: Optional[str] = Field(None, description="Severidad.")
    model_config = ConfigDict(protected_namespaces=())

class ComplianceOutputData(BaseModel): # Para el `output_data`
    is_compliant: bool = Field(..., description="Cumple o no.")
    violations: Optional[List[ComplianceViolationDetail]] = Field(None, description="Violaciones.")
    model_config = ConfigDict(protected_namespaces=())

# Reviewer Agent
class ReviewerAgentInput(BaseModel):
    content_to_review: str = Field(..., description="Contenido a revisar.")
    review_criteria: Optional[List[str]] = Field(None, description="Criterios de revisión.")
    context: Optional[str] = Field(None, description="Contexto adicional.")
    model_config = ConfigDict(protected_namespaces=())

class ReviewIssueDetail(BaseModel):
    criterion: str = Field(..., description="Criterio afectado.")
    finding: str = Field(..., description="Hallazgo.")
    suggestion: Optional[str] = Field(None, description="Sugerencia.")
    severity: Optional[str] = Field(None, description="Severidad.")
    model_config = ConfigDict(protected_namespaces=())

class ReviewerAgentOutput(BaseModel): # Para el `output_data`
    overall_score: Optional[float] = Field(None, ge=0, le=5, description="Puntuación general.")
    feedback: Optional[str] = Field(None, description="Feedback general.")
    issues: Optional[List[ReviewIssueDetail]] = Field(None, description="Problemas específicos.")
    model_config = ConfigDict(protected_namespaces=())

# Recommender Agent
class RecommenderAgentInput(BaseModel):
    prompt_idea: str = Field(..., description="Idea para recomendaciones.")
    limit: int = Field(5, ge=1, le=20, description="Límite de recomendaciones.")
    model_config = ConfigDict(protected_namespaces=())

class RecommendationItem(BaseModel):
    id: str = Field(...)
    type: str = Field(...) # 'prompt', 'document_snippet', etc.
    title: Optional[str] = None
    description: Optional[str] = None
    content_snippet: Optional[str] = None
    score: Optional[float] = None
    source: Optional[str] = None
    model_config = ConfigDict(from_attributes=True, protected_namespaces=())

class RecommenderAgentOutput(BaseModel): # Para el `output_data`
    recommendations: List[RecommendationItem] = Field(..., description="Lista de items recomendados.")
    justification: Optional[str] = Field(None, description="Justificación de las recomendaciones.")
    model_config = ConfigDict(protected_namespaces=())

# Suggester Agent (para endpoint /process/suggest)
class SuggestionRequest(BaseModel):
    current_prompt_text: str = Field(..., description="Texto actual del prompt.")
    context_text: Optional[str] = Field(None, description="Contexto adicional.")
    model_config = ConfigDict(protected_namespaces=())

class SuggestionResponse(BaseModel): # Respuesta directa del endpoint /suggest
    suggestion: str = Field(..., description="Texto de la sugerencia.")
    agent_used: AgentType = Field(AgentType.SUGGESTER, description="Agente usado.")
    model_used: Optional[str] = Field(None, description="Modelo LLM/Embedding usado.")
    # Añadido status y error_message por si una sugerencia individual falla
    status: str = Field("success", description="Estado de esta sugerencia.")
    error_message: Optional[str] = Field(None, description="Error si falló esta sugerencia.")
    model_config = ConfigDict(protected_namespaces=(), use_enum_values=True)

# Embeddings Utility (para endpoint /embeddings/generate)
class EmbeddingRequest(BaseModel):
    text: str = Field(..., min_length=1, description="Texto para embedding.")
    model_config = ConfigDict(protected_namespaces=())

class EmbeddingResponse(BaseModel): # Respuesta directa del endpoint /embeddings/generate
    embedding: List[float] = Field(..., description="Vector de embedding.")
    model_used: str = Field(..., description="Modelo de embedding usado.")
    # status y error_message no son necesarios si el endpoint falla con HTTP error
    model_config = ConfigDict(protected_namespaces=())

# File Upload Utility (para endpoint /context/upload)
class FileUploadResponse(BaseModel): # Respuesta directa del endpoint /context/upload
    info: str = Field(..., description="Mensaje informativo.")
    file_id: Optional[str] = Field(None, description="ID asignado al archivo.")
    filename: str = Field(..., description="Nombre original del archivo.")
    content_type: Optional[str] = Field(None, description="MIME type.")
    size_bytes: Optional[int] = Field(None, description="Tamaño en bytes.")
    local_path: Optional[str] = Field(None, description="Ruta local temporal.")
    gcs_uri: Optional[str] = Field(None, description="URI de GCS si se subió.")
    status: str = Field("success_uploaded", description="Estado de la carga.")
    error_message: Optional[str] = Field(None, description="Mensaje de error si aplica.")
    model_config = ConfigDict(protected_namespaces=())

print(f"[AGENTS_SCHEMAS] agent_schemas.py loaded (v2). Shared schemas imported: {_SHARED_SCHEMAS_LOADED}")