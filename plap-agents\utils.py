# PLAP/plap-agents/utils.py
print("[UTILS] Loading utils.py")

from typing import Any, Dict, List, Optional, Union
from datetime import datetime, timezone, timedelta
import uuid
import re # Importar para saneo de texto y otras operaciones con regex
import json # Para trabajar con JSON si es necesario


def is_valid_uuid(uuid_to_test: Optional[str], version: int = 4) -> bool:
    """
    Verifica si una cadena es un UUID válido (opcionalmente una versión específica).
    """
    if not uuid_to_test or not isinstance(uuid_to_test, str):
        return False
    try:
        uuid_obj = uuid.UUID(uuid_to_test, version=version)
        # Adicionalmente, verificar que la representación string coincida (evita UUIDs con ceros que no son estándar)
        return str(uuid_obj) == uuid_to_test.lower()
    except ValueError:
        return False
    except Exception: # Captura otros errores inesperados
        return False


def clean_text_for_llm(text: Optional[str]) -> str:
    """
    Limpia el texto antes de enviarlo a un LLM:
    - Convierte None a cadena vacía.
    - Elimina espacios en blanco redundantes (múltiples espacios, saltos de línea por un solo espacio).
    - Elimina espacios al inicio y al final.
    - (Opcional) Considerar normalizar Unicode, eliminar caracteres de control no imprimibles (excepto saltos de línea comunes).
    """
    if text is None:
        return ""

    # Reemplazar múltiples espacios en blanco (incluyendo \n, \r, \t) con un solo espacio,
    # pero conservar saltos de línea individuales como importantes para la estructura del prompt.
    # Primero, consolidar saltos de línea múltiples a uno solo
    cleaned = re.sub(r'(\r\n|\r|\n){2,}', '\n', text)
    # Luego, reemplazar espacios y tabs múltiples por un solo espacio, EXCEPTO si son saltos de línea
    cleaned = re.sub(r'[ \t]{2,}', ' ', cleaned)
    # Eliminar espacios al inicio/final de cada línea
    cleaned = "\n".join([line.strip() for line in cleaned.split('\n')])
    # Eliminar espacios al inicio/final del texto completo
    cleaned = cleaned.strip()

    # (Opcional) Eliminar caracteres de control no imprimibles, excepto tab, newline, carriage return
    # Esta es una limpieza más agresiva, usar con cuidado.
    # cleaned = "".join(ch for ch in cleaned if unicodedata.category(ch)[0]!="C" or ch in ('\n', '\t', '\r'))

    return cleaned


def truncate_text(text: Optional[str], max_length: int, indicator: str = "...") -> str:
    """
    Trunca un texto si excede max_length, asegurando que el indicador se ajuste.
    """
    if text is None:
        return ""
    if len(text) <= max_length:
        return text

    # Asegurar que la longitud del indicador se tenga en cuenta
    effective_max_length = max_length - len(indicator)
    if effective_max_length <= 0: # Si el indicador es más largo que el max_length
        return indicator[:max_length] # Truncar el indicador mismo

    return text[:effective_max_length] + indicator


def sanitize_llm_output(output_text: Optional[str], strategy: str = "basic") -> str:
    """
    Realiza un saneo básico de la salida de un LLM para mitigar riesgos simples.
    Esta es una implementación placeholder y **NO es una solución de seguridad completa**.
    Se deben implementar guardrails más robustos y específicos según los riesgos.

    Estrategias:
    - "basic": Limpieza de texto simple.
    - "remove_code_blocks": Intenta remover bloques de código markdown (muy básico).
    - "escape_html": Escapa caracteres HTML (útil si la salida se va a renderizar en web directamente, aunque el frontend debería hacer esto).
    """
    if output_text is None:
        return ""

    cleaned = clean_text_for_llm(output_text) # Empezar con limpieza básica

    if strategy == "remove_code_blocks":
        # Intento MUY básico de quitar bloques de código. No es seguro contra ofuscación.
        cleaned = re.sub(r"```.*?```", "[Code Block Removed by Sanitizer]", cleaned, flags=re.DOTALL | re.IGNORECASE)
        print("[UTILS] sanitize_llm_output: Applied 'remove_code_blocks' (basic).")

    elif strategy == "escape_html":
        import html
        cleaned = html.escape(cleaned)
        print("[UTILS] sanitize_llm_output: Applied 'escape_html'.")

    # Siempre es buena idea loggear si el saneo modifica el output
    if cleaned != output_text:
         print(f"[UTILS] sanitize_llm_output (strategy: {strategy}): Output modified. Original len: {len(output_text)}, Cleaned len: {len(cleaned)}")

    # CRÍTICO: Esta función es un placeholder. La seguridad de las salidas de LLM es compleja.
    # Se deben implementar medidas robustas basadas en el Checklist de Seguridad y la Estrategia de Gobernanza.
    # Esto puede incluir:
    # - Listas de denegación/permisión más sofisticadas.
    # - Análisis de la estructura de la salida (ej. si se espera JSON).
    # - Uso de modelos de moderación de contenido.
    # - Escaneo de vulnerabilidades si la salida es código o configuración.
    print("[UTILS] WARNING: Using placeholder sanitize_llm_output. Implement robust output validation and sanitation.")
    return cleaned


def generate_internal_id(prefix: str = "plap_obj") -> str:
    """Genera un ID único con un prefijo."""
    return f"{prefix}_{uuid.uuid4().hex}"


# Ejemplo de cómo parsear una cadena JSON de forma segura
def safe_json_parse(json_string: Optional[str]) -> Optional[Dict[str, Any]]:
    """Intenta parsear una cadena JSON. Devuelve un diccionario o None si falla."""
    if not json_string or not isinstance(json_string, str):
        return None
    try:
        return json.loads(json_string)
    except json.JSONDecodeError as e:
        print(f"[UTILS] safe_json_parse: JSONDecodeError - {e}. Input string (first 100 chars): '{json_string[:100]}'")
        return None
    except Exception as e:
        print(f"[UTILS] safe_json_parse: Unexpected error parsing JSON - {e}. Input string (first 100 chars): '{json_string[:100]}'")
        return None

# Podrías añadir más funciones de utilidad aquí, como:
# - Formateadores de fecha/hora.
# - Validadores específicos.
# - Funciones para interactuar con la caché (si se usa Redis, etc.).

print("[UTILS] utils.py loaded.")