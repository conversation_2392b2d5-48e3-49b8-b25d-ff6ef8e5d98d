# Dockerfile Plantilla (v0.9.2 - VERSIÓN CORREGIDA WARNINGS)
# Usar para servicios Python que no manejan credenciales GCP directamente en la imagen

# Paso 1: Imagen base
FROM python:3.12-slim

# Paso 2: Variables de Entorno Esenciales (Formato Corregido)
ENV PYTHONDONTWRITEBYTECODE=1
ENV PYTHONUNBUFFERED=1
ENV PYTHONPATH="/app:/opt/plap-shared-models"

# Paso 3: Directorio de trabajo
WORKDIR /app

# Paso 4: Dependencias del sistema
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    # libpq-dev # Solo si es necesario para el servicio específico
    ca-certificates \
    && rm -rf /var/lib/apt/lists/*

# Paso 5: Crear usuario y grupo no privilegiados
RUN groupadd --system appgroup -r && useradd --system --no-log-init --gid appgroup -r --shell /bin/false --no-create-home appuser

# Paso 6: Copiar SOLO requirements.txt (Asignando propietario)
COPY --chown=appuser:appgroup requirements.txt requirements.txt

# Paso 7: Instalar dependencias Python
RUN pip install --no-cache-dir --upgrade pip && \
    pip install --no-cache-dir -r requirements.txt # Asume que uvicorn está aquí

# Paso 8: Copiar el RESTO del código fuente (Asignando propietario)
COPY --chown=appuser:appgroup . .

# Paso 9: Cambiar al usuario no privilegiado
USER appuser

# Paso 10: Comando de ejecución
CMD ["uvicorn", "main:app", "--host", "0.0.0.0", "--port", "80"]