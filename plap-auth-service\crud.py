# plap-auth-service/crud.py
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError

# --- CAMBIO AQUÍ ---
import models # Importación absoluta
# Anteriormente: from . import models
# --------------------

import security # Necesario para get_password_hash
import uuid
from typing import Optional

# --- Importar Schema de Input desde Shared ---
try:
    from schemas.user_schemas import UserCreate_v0_9_2 as UserCreateSchema
    print("CRUD: Successfully imported UserCreate_v0_9_2 from shared models.")
except ImportError as e:
    print(f"ERROR CRITICO en CRUD: No se pudo importar UserCreate_v0_9_2: {e}. Usando placeholder.")
    from pydantic import BaseModel, EmailStr
    class UserCreateSchema(BaseModel): # type: ignore
        email: EmailStr
        password: str
        name: Optional[str] = None
# --------------------------------------------


def get_user_by_email(db: Session, email: str) -> Optional[models.User]:
    """Busca un usuario por su email."""
    print(f"[CRUD_GET_BY_EMAIL] Buscando usuario con email: {email}")
    user = db.query(models.User).filter(models.User.email == email).first()
    if user:
        print(f"[CRUD_GET_BY_EMAIL] Usuario encontrado: {user.id}")
    else:
        print(f"[CRUD_GET_BY_EMAIL] Usuario NO encontrado con email: {email}")
    return user

def get_user_by_id(db: Session, user_id: uuid.UUID) -> Optional[models.User]:
    """Busca un usuario por su ID (UUID)."""
    return db.query(models.User).filter(models.User.id == user_id).first()

def create_user(db: Session, user: UserCreateSchema) -> models.User: 
    """Crea un nuevo usuario en la base de datos."""
    print(f"[CRUD_CREATE_USER] Intentando crear usuario con email: {user.email}, nombre: {user.name}")
    
    hashed_password = "" 
    try:
        hashed_password = security.get_password_hash(user.password)
        print(f"[CRUD_CREATE_USER] Password hasheada para: {user.email}")
    except Exception as e_hash:
        print(f"[CRUD_CREATE_USER] ERROR EXCEPCIONAL HASHEANDO PASSWORD para {user.email}: {e_hash}")
        raise 
            
    db_user = models.User(
        id=uuid.uuid4(), 
        email=user.email,
        name=user.name,
        hashed_password=hashed_password
    )
    print(f"[CRUD_CREATE_USER] Objeto models.User instanciado para: {user.email} con ID provisional: {db_user.id}")
    
    try:
        db.add(db_user)
        print(f"[CRUD_CREATE_USER] db.add(db_user) ejecutado para: {user.email}")
        db.commit()
        print(f"[CRUD_CREATE_USER] db.commit() EJECUTADO CON ÉXITO para: {user.email}. ID asignado: {db_user.id}")
        db.refresh(db_user)
        print(f"[CRUD_CREATE_USER] db.refresh(db_user) ejecutado. Usuario creado: {db_user.email}, ID: {db_user.id}")
        return db_user
    except IntegrityError as ie: 
        db.rollback()
        print(f"[CRUD_CREATE_USER] IntegrityError específico DENTRO de create_user al hacer commit para {user.email}: {ie}")
        raise 
    except Exception as e_db:
        db.rollback()
        print(f"[CRUD_CREATE_USER] OTRO ERROR DE DB DENTRO de create_user para {user.email}: {e_db}")
        import traceback
        traceback.print_exc() 
        raise 