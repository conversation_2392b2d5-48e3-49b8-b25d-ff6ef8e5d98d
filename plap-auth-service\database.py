import os
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.declarative import declarative_base
import sys # Importa sys para poder hacer flush

SQLALCHEMY_DATABASE_URL = os.getenv("DATABASE_URL")

# --- INICIO DEBUGGING ---
print(f"--- DEBUG database.py --- <PERSON>or leído de os.getenv('DATABASE_URL'): '{SQLALCHEMY_DATABASE_URL}'", flush=True) 
# flush=True asegura que se imprima inmediatamente
# --- FIN DEBUGGING ---

if not SQLALCHEMY_DATABASE_URL:
    print("ERROR CRÍTICO: La variable de entorno DATABASE_URL no está configurada en database.py.", flush=True)
    # Podrías lanzar un error aquí para detener el arranque si falta
    # raise ValueError("DATABASE_URL no configurada!")
    # O usar un valor por defecto inválido para que falle más adelante
    SQLALCHEMY_DATABASE_URL = "*********************************" 

try:
    # Asegúrate de que SOLO SQLALCHEMY_DATABASE_URL se pasa aquí
    engine = create_engine(SQLALCHEMY_DATABASE_URL) 
    print(f"--- DEBUG database.py --- Intentando crear engine con URL: '{SQLALCHEMY_DATABASE_URL}'", flush=True)
except Exception as e:
    print(f"--- DEBUG database.py --- ERROR al crear engine con URL '{SQLALCHEMY_DATABASE_URL}': {e}", flush=True)
    raise e

SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

Base = declarative_base()

def create_db_tables():
    try:
        print("--- DEBUG database.py --- Intentando crear tablas (Base.metadata.create_all)...", flush=True)
        Base.metadata.create_all(bind=engine)
        print("--- DEBUG database.py --- Base.metadata.create_all ejecutado.", flush=True)
    except Exception as e:
         # Imprime el error específico de creación de tablas
        print(f"--- DEBUG database.py --- Error durante create_all: {e}", flush=True)
         # Aquí también podrías ver el error "could not translate host name" si el engine falló antes
         # o errores de permisos, etc.

# ... (resto de tu código database.py) ... 