# plap-auth-service/main.py

from fastapi import FastAPI, Depends, HTTPException, status
from fastapi.security import OAuth2PasswordRequestForm # <--- IMPORTANTE PARA LOGIN
from fastapi.middleware.cors import CORSMiddleware
from sqlalchemy.orm import Session
from sqlalchemy.exc import IntegrityError 
from sqlalchemy.sql import text
from typing import Optional, Union, Type, List, Any, Annotated # <--- ASEGÚRATE QUE 'Type' Y 'Annotated' ESTÁN AQUÍ
import os
import uuid 
import sys 
from pydantic import BaseModel as PydanticBaseModel # Puedes quitar esto si no lo usas directamente
from datetime import datetime, timezone

# --- DEBUGGING IMPORTS ---
import pkgutil
# --- END DEBUGGING IMPORTS ---

print("--- [AUTH_SERVICE_MAIN.PY] STARTING MODULE EXECUTION ---")

# ... (<PERSON><PERSON> prints de DEBUG de PYTHONPATH, LISTDIR, etc. pueden mantenerse si los necesitas) ...
# Por brevedad, los omitiré aquí, pero puedes mantenerlos en tu archivo.
# Es importante que los imports locales ocurran DESPUÉS de que PYTHONPATH esté configurado
# y los montajes de volumen estén activos.

# --- Importaciones Locales ---
print("--- [AUTH_SERVICE_MAIN.PY] Attempting local imports... ---")
try:
    import crud
    import models
    import auth_schemas # Este módulo debería definir/importar tus schemas
    import security
    from database import SessionLocal, engine, create_db_tables 
    print("--- [AUTH_SERVICE_MAIN.PY] Local imports successful. ---")
except ImportError as e:
    print(f"--- [AUTH_SERVICE_MAIN.PY] ERROR en importaciones locales: {e} ---")
    # Considera salir o manejar este error si las importaciones son críticas para el arranque
    sys.exit(1)


# --- Configuración de la Aplicación FastAPI ---
app = FastAPI(
    title="PLAP Auth Service",
    version="0.9.2",
    description="Servicio de Autenticación para PLAP",
)

# --- Evento Startup ---
@app.on_event("startup")
def on_startup():
    print("Auth Service: Startup event triggered.")
    try:
        print("Auth Service: Intentando crear tablas...")
        create_db_tables() 
        print("Auth Service: create_db_tables() ejecutado.")
    except Exception as e:
        print(f"Auth Service: ERROR CRÍTICO creando tablas: {e}")
        # En un entorno de producción, podrías querer que el servicio falle si no puede conectar/crear tablas.

# --- Configuración de CORS ---
origins = os.getenv("CORS_ORIGINS", "*").split(",")
print(f"Auth Service: CORS Origins: {origins}") 
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins, 
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Dependencia DB ---
def get_db_session(): # Renombrado para claridad, aunque 'get_db' también es común
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# --- Endpoints de Autenticación ---

@app.post("/auth/signup", 
          response_model=auth_schemas.UserProfile_v0_9_2, # Usar el schema directamente
          status_code=status.HTTP_201_CREATED, 
          tags=["Authentication"])
async def signup_user(
    user_input: auth_schemas.UserCreate_v0_9_2, # Usar el schema directamente
    db: Session = Depends(get_db_session)
):
    db_user_found = crud.get_user_by_email(db, email=user_input.email)
    if db_user_found:
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered")
    
    try:
        # crud.create_user debería tomar el UserCreate_v0_9_2 y devolver el modelo ORM User
        created_user_orm = crud.create_user(db=db, user_input_schema=user_input) 
        print(f"Usuario creado en DB con ID: {created_user_orm.id}, Email: {created_user_orm.email}")
        
        # Construye la respuesta usando los campos del objeto ORM.
        # Asegúrate que UserProfile_v0_9_2 tiene los campos correspondientes.
        # Es más seguro y limpio construir el objeto Pydantic directamente para la respuesta.
        return auth_schemas.UserProfile_v0_9_2(
            id=created_user_orm.id,
            email=created_user_orm.email,
            full_name=getattr(created_user_orm, 'full_name', None), # Ajusta 'full_name' si el campo se llama diferente
            is_active=getattr(created_user_orm, 'is_active', True),
            credits_total=getattr(created_user_orm, 'credits_total', 0),
            created_at=getattr(created_user_orm, 'created_at', datetime.now(timezone.utc)),
            updated_at=getattr(created_user_orm, 'updated_at', datetime.now(timezone.utc))
            # Añade otros campos que tenga UserProfile_v0_9_2 y que estén en created_user_orm
        )

    except IntegrityError: 
        db.rollback() 
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Email already registered (IntegrityError)")
    except Exception as e: 
        db.rollback()
        print(f"Error inesperado en signup: {e}") 
        import traceback 
        traceback.print_exc()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="An internal error occurred during signup.")


@app.post("/auth/login", 
          response_model=auth_schemas.TokenResponse_v0_9_2, # Usar el schema directamente
          tags=["Authentication"])
async def login_for_access_token(
    form_data: Annotated[OAuth2PasswordRequestForm, Depends()], # CORREGIDO
    db: Session = Depends(get_db_session)
):
    # crud.get_user_by_email debería buscar por email (form_data.username)
    user_orm = crud.get_user_by_email(db, email=form_data.username) 

    if not user_orm or not security.verify_password(form_data.password, user_orm.hashed_password):
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Incorrect email or password",
            headers={"WWW-Authenticate": "Bearer"}, # "Bearer" es el esquema estándar
        )
    
    # Asumo que tu modelo ORM User tiene un campo 'is_active'
    if not getattr(user_orm, 'is_active', True): # Proporciona un default si el atributo no existe
        raise HTTPException(status_code=status.HTTP_400_BAD_REQUEST, detail="Inactive user")
    
    try:
        # security.create_access_token debería tomar un identificador de sujeto (ej. user_id)
        access_token = security.create_access_token(
            data={"sub": str(user_orm.id)} # El 'sub' (subject) del token JWT
        )
        # auth_schemas.TokenResponse_v0_9_2 debería esperar 'access_token' y 'token_type'
        return auth_schemas.TokenResponse_v0_9_2(access_token=access_token, token_type="bearer")
    except Exception as e:
        print(f"Error creando token JWT: {e}")
        import traceback
        traceback.print_exc()
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Could not create access token.")


@app.get("/health", tags=["Health"])
async def health_check():
    db_status = "unknown"
    try:
        # Usar la dependencia para obtener la sesión de DB es más consistente
        # db: Session = Depends(get_db_session) # No necesitas un 'yield' aquí
        # with SessionLocal() as db_session: # Mejor manera de manejar la sesión para una sola query
        db_session = SessionLocal()
        try:
            db_session.execute(text("SELECT 1")) 
            db_status = "connected"
        finally:
            db_session.close()
    except Exception as e:
        print(f"Health check DB connection error: {e}")
        db_status = "error" 
    return {"status": "healthy", "service": "Auth Service", "db_status": db_status}

print("--- [AUTH_SERVICE_MAIN.PY] FINISHING MODULE EXECUTION ---")