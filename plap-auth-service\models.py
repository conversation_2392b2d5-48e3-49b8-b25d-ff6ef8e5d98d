# plap-user-service/models.py (CORREGIDO para prompt_workflow_id)
from sqlalchemy import Column, String, Integer, DateTime, Boolean, ForeignKey, func, Float, Text, JSON # Asegúrate de importar JSON si no estaba
from sqlalchemy.orm import declarative_base, relationship
from sqlalchemy.dialects.postgresql import UUID, JSONB # JSONB es específico de Postgres, JSON es más genérico
import uuid
# from datetime import datetime, timezone # datetime ya se importa con func

Base = declarative_base()

class User(Base):
    __tablename__ = "users"
    # ... (definición de User como la teníamos, asegurando que es idéntica a la de auth-service/models.py)
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    email = Column(String, unique=True, index=True, nullable=False)
    hashed_password = Column(String, nullable=False) # Aunque user-service no lo use para auth, es parte de la tabla
    name = Column(String, nullable=True)
    avatar_url = Column(String, nullable=True)
    credits_total = Column(Integer, default=0, nullable=False)
    credits_used = Column(Integer, default=0, nullable=False)
    current_plan_id = Column(UUID(as_uuid=True), ForeignKey('plans.id'), nullable=True) # FK
    notification_preferences = Column(JSONB, nullable=True) # Para Postgres, sino JSON
    is_active = Column(Boolean, default=True, nullable=False)
    is_superuser = Column(Boolean, default=False, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False)

    plan = relationship("Plan", back_populates="users")
    credit_transactions = relationship("CreditTransaction", back_populates="user", order_by="desc(CreditTransaction.timestamp)")
    invoices = relationship("Invoice", back_populates="user", order_by="desc(Invoice.issue_date)")


class Plan(Base):
    __tablename__ = "plans"
    # ... (definición de Plan como la tenías)
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    name = Column(String, unique=True, nullable=False)
    description = Column(Text, nullable=True)
    monthly_cost = Column(Float, nullable=False)
    monthly_credits = Column(Integer, nullable=False)
    features = Column(JSONB, nullable=True) # Para Postgres, sino JSON
    is_active = Column(Boolean, default=True, nullable=False)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False) # Añadido
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False) # Añadido

    users = relationship("User", back_populates="plan")


class CreditTransaction(Base):
    __tablename__ = "credit_transactions"
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    type = Column(String(50), nullable=False)
    credits_amount = Column(Integer, nullable=False)
    timestamp = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    
    # --- CORRECCIÓN AQUÍ ---
    prompt_workflow_id = Column(String(100), nullable=True) # Cambiado de UUID a String
    # -------------------------
    
    description = Column(Text, nullable=True)
    related_invoice_id = Column(UUID(as_uuid=True), ForeignKey('invoices.id'), nullable=True)

    user = relationship("User", back_populates="credit_transactions")
    invoice = relationship("Invoice", back_populates="credit_transactions", foreign_keys=[related_invoice_id]) # Especificar FK si hay ambigüedad


class Invoice(Base):
    __tablename__ = "invoices"
    # ... (definición de Invoice como la tenías)
    id = Column(UUID(as_uuid=True), primary_key=True, default=uuid.uuid4)
    user_id = Column(UUID(as_uuid=True), ForeignKey('users.id'), nullable=False, index=True)
    stripe_invoice_id = Column(String(255), unique=True, nullable=True)
    issue_date = Column(DateTime(timezone=True), server_default=func.now(), nullable=False)
    due_date = Column(DateTime(timezone=True), nullable=True)
    paid_date = Column(DateTime(timezone=True), nullable=True)
    amount = Column(Float, nullable=False)
    currency = Column(String(3), default='USD', nullable=False)
    status = Column(String(50), nullable=False)
    line_items = Column(JSONB, nullable=True) # Para Postgres, sino JSON
    payment_method_details = Column(JSONB, nullable=True) # Para Postgres, sino JSON
    link_to_pdf = Column(String(512), nullable=True)
    created_at = Column(DateTime(timezone=True), server_default=func.now(), nullable=False) # Añadido
    updated_at = Column(DateTime(timezone=True), server_default=func.now(), onupdate=func.now(), nullable=False) # Añadido

    user = relationship("User", back_populates="invoices")
    credit_transactions = relationship("CreditTransaction", back_populates="invoice")

# Asegúrate de que este archivo database.py define Base = declarative_base()
# from .database import Base # No es necesario si Base se define aquí o en models.py de User