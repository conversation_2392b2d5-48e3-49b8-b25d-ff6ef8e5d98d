# Framework web
fastapi==0.111.0
uvicorn[standard]==0.30.1

# Validación de datos
pydantic==2.7.1

# Base de datos
sqlalchemy==2.0.30
psycopg2-binary==2.9.9

# Autenticación y Seguridad
passlib[bcrypt]==1.7.4      # <--- CORRECCIÓN AQUÍ: Usar la última versión estable conocida
bcrypt==4.1.3              # Mantener una versión reciente de bcrypt
PyJWT[crypto]==2.8.0      # <--- ESTA LÍNEA ES LA IMPORTANTEto]==2.8.0

# Opcional: para leer .env localmente
# python-dotenv==1.0.0