# plap-auth-service/security.py
from passlib.context import Crypt<PERSON>ontext
import jwt # PyJWT
from datetime import datetime, timedelta, timezone
from typing import Optional
import os

# Configuración de Passlib para hashear contraseñas
pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

# Configuración de JWT (¡DEBEN COINCIDIR con backend-api/dependencies.py!)
# <PERSON> desde variables de entorno (inyectadas por Docker Compose desde .env)
JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "a_very_secret_key_please_change_in_env") # ¡USA UNA CLAVE FUERTE EN .ENV!
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = int(os.getenv("ACCESS_TOKEN_EXPIRE_MINUTES", "30")) # Por defecto 30 minutos

print(f"Auth Service Security: JWT_SECRET_KEY loaded: {'********' if JWT_SECRET_KEY else 'NOT FOUND'}")
print(f"Auth Service Security: ACCESS_TOKEN_EXPIRE_MINUTES: {ACCESS_TOKEN_EXPIRE_MINUTES}")


def verify_password(plain_password: str, hashed_password: str) -> bool:
    return pwd_context.verify(plain_password, hashed_password)

def get_password_hash(password: str) -> str:
    return pwd_context.hash(password)

def create_access_token(data: dict, expires_delta: Optional[timedelta] = None) -> str:
    to_encode = data.copy()
    if expires_delta:
        expire = datetime.now(timezone.utc) + expires_delta
    else:
        expire = datetime.now(timezone.utc) + timedelta(minutes=ACCESS_TOKEN_EXPIRE_MINUTES)
    to_encode.update({"exp": expire})
    encoded_jwt = jwt.encode(to_encode, JWT_SECRET_KEY, algorithm=ALGORITHM)
    return encoded_jwt