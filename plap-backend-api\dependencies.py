# plap-backend-api/dependencies.py
from fastapi import Depends, HTTPException, status, Header
from fastapi.security import <PERSON>A<PERSON>2Password<PERSON>earer 
import jwt 
import os
from typing import Optional, Annotated # <--- CAMBIO AQUÍ: <PERSON><PERSON><PERSON><PERSON> Annotated

JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "super-secret-key-deberia-estar-en-env")
ALGORITHM = "HS256"

async def get_current_user_id_from_token(authorization: Optional[str] = Header(None)) -> str:
    print(f"--- DEBUG dependencies.py: Received Authorization Header: {authorization}") # DEBUG
    if authorization is None:
        print("--- DEBUG dependencies.py: No Authorization header found.") # DEBUG
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Not authenticated (no Authorization header)",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    parts = authorization.split()
    if len(parts) != 2 or parts[0].lower() != "bearer":
        print(f"--- DEBUG dependencies.py: Invalid authentication scheme: {authorization}") # DEBUG
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication scheme",
            headers={"WWW-Authenticate": "Bearer"},
        )
    
    token = parts[1]
    credentials_exception = HTTPException(
        status_code=status.HTTP_401_UNAUTHORIZED,
        detail="Could not validate credentials",
        headers={"WWW-Authenticate": "Bearer"},
    )
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        user_id: Optional[str] = payload.get("sub")
        print(f"--- DEBUG dependencies.py: Token Decoded. Payload 'sub' (user_id): {user_id}") # DEBUG
        if user_id is None:
            print("--- DEBUG dependencies.py: user_id (sub) is None in token payload.") # DEBUG
            raise credentials_exception
        return user_id
    except jwt.ExpiredSignatureError:
        print("--- DEBUG dependencies.py: Token has expired.") # DEBUG
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Token has expired",
            headers={"WWW-Authenticate": "Bearer"},
        )
    except jwt.PyJWTError as e:
        print(f"--- DEBUG dependencies.py: JWT Error during decode: {e}") # DEBUG
        raise credentials_exception
    except Exception as e:
        print(f"--- DEBUG dependencies.py: Unexpected error decoding token: {e}") # DEBUG
        raise credentials_exception

AuthenticatedUserId = Annotated[str, Depends(get_current_user_id_from_token)]