# plap-backend-api/main.py
from fastapi import <PERSON><PERSON><PERSON>, Depends, HTTPException, status, Header, Request, Response
from fastapi.middleware.cors import CORSMiddleware
from typing import Annotated, Dict, Any, List, Optional
import httpx
import os
import jwt # Lo necesitas para get_current_user_id_from_token
from datetime import datetime
import traceback

# --- INICIO DE CORRECCIÓN: Añadir importación de ConfigDict ---
from pydantic import BaseModel, Field, ConfigDict # Asegúrate que ConfigDict está aquí
# --- FIN DE CORRECCIÓN ---
from enum import Enum

app = FastAPI(
    title="PLAP Backend API Gateway",
    version="0.9.5_ConfigDictFix", # Actualización de versión
    description="API Gateway para el sistema PLAP",
)

origins_str = os.getenv("CORS_ORIGINS_BACKEND_API", "*")
origins = origins_str.split(',') if origins_str and origins_str != "*" else ["http://localhost:3000", "http://127.0.0.1:3000"]
app.add_middleware(
    CORSMiddleware, allow_origins=origins, allow_credentials=True,
    allow_methods=["*"], allow_headers=["*"])

class LLMModelTypePublic(str, Enum):
    GEMINI_PRO = "gemini-pro"
    GEMINI_1_0_FLASH_001 = "gemini-1.0-flash-001"
    GEMINI_1_5_PRO = "gemini-1.5-pro"
    GEMINI_1_5_FLASH = "gemini-1.5-flash"
    GEMINI_2_5_FLASH_PREVIEW = "gemini-2.5-flash-preview"
    GEMINI_2_5_FLASH_PREVIEW_04_17 = "gemini-2.5-flash-preview-04-17"

class WorkflowTypePublic(str, Enum):
    GUIDED = "guided"; EDITOR = "editor"; DEFAULT = "default"

class PublicPromptInitiationPayload(BaseModel):
    prompt_text_idea: str = Field(...)
    target_llm_model: LLMModelTypePublic
    workflow_type: WorkflowTypePublic = WorkflowTypePublic.DEFAULT
    zep_session_id: Optional[str] = Field(None, description="ID de sesión de Zep existente para continuar una conversación.")
    # Ahora ConfigDict debería estar definido
    model_config = ConfigDict(use_enum_values=True, protected_namespaces=())

AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_INTERNAL_URL", "http://auth-service:80")
USER_SERVICE_URL = os.getenv("USER_SERVICE_INTERNAL_URL", "http://user-service:80")
ORCHESTRATOR_SERVICE_URL = os.getenv("ORCHESTRATOR_SERVICE_INTERNAL_URL", "http://orchestrator:80")

JWT_SECRET_KEY = os.getenv("JWT_SECRET_KEY", "super_secret_key_that_should_be_in_env")
ALGORITHM = "HS256"

async def get_current_user_id_from_token(request: Request) -> str:
    auth_header = request.headers.get("Authorization")
    if not auth_header or not auth_header.startswith("Bearer "): raise HTTPException(status.HTTP_401_UNAUTHORIZED, "Not authenticated")
    token = auth_header.split(" ")[1]
    try:
        payload = jwt.decode(token, JWT_SECRET_KEY, algorithms=[ALGORITHM])
        user_id: Optional[str] = payload.get("sub")
        if user_id is None: raise HTTPException(status.HTTP_401_UNAUTHORIZED, "Invalid token: Missing user_id")
        return user_id
    except jwt.ExpiredSignatureError: raise HTTPException(status.HTTP_401_UNAUTHORIZED, "Token has expired")
    except jwt.PyJWTError as e: raise HTTPException(status.HTTP_401_UNAUTHORIZED, f"Invalid token: {e}")

async def _forward_request(request: Request, target_service_base_url: str, target_path: str, pass_user_id: bool = False):
    method = request.method; _target_base = target_service_base_url.rstrip("/"); _target_path_segment = target_path.lstrip("/")
    full_target_url = f"{_target_base}/{_target_path_segment}"
    headers_to_forward = {k: v for k, v in request.headers.items() if k.lower() not in ['host', 'connection', 'content-length', 'transfer-encoding']}
    if "content-type" in request.headers: headers_to_forward["content-type"] = request.headers["content-type"]
    if pass_user_id:
        try: user_id = await get_current_user_id_from_token(request); headers_to_forward["X-User-ID"] = user_id
        except HTTPException as e:
            if not (e.status_code == status.HTTP_401_UNAUTHORIZED and not pass_user_id): raise e
    body_content = await request.body()
    print(f"API GW: Fwd {method} to {full_target_url} (X-User-ID: {headers_to_forward.get('X-User-ID', 'N/A')})")
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            rp_req = client.build_request(method, full_target_url, headers=headers_to_forward, content=body_content, params=request.query_params)
            rp_resp = await client.send(rp_req)
            response_headers = {k: v for k, v in rp_resp.headers.items() if k.lower() not in ['content-encoding', 'transfer-encoding', 'connection']}
            return Response(content=rp_resp.content, status_code=rp_resp.status_code, headers=response_headers)
        except httpx.HTTPStatusError as exc: print(f"API GW Err from {target_service_base_url}: {exc.response.status_code}"); return Response(content=exc.response.content, status_code=exc.response.status_code, headers=dict(exc.response.headers))
        except httpx.RequestError as exc: print(f"API GW ConnErr to {target_service_base_url}: {exc}"); raise HTTPException(status.HTTP_503_SERVICE_UNAVAILABLE, f"Service {target_service_base_url} unavailable.")

@app.api_route("/api/v1/auth/{auth_path:path}", methods=["GET", "POST", "PUT", "DELETE", "PATCH"])
async def forward_to_auth(request: Request, auth_path: str):
    return await _forward_request(request, AUTH_SERVICE_URL, f"/auth/{auth_path}")

@app.post("/api/v1/prompts/", status_code=status.HTTP_202_ACCEPTED, tags=["Prompts & Workflows"])
async def create_prompt_workflow_public(payload: PublicPromptInitiationPayload, user_id: Annotated[str, Depends(get_current_user_id_from_token)]):
    print(f"[API GATEWAY DEBUG /api/v1/prompts/] Payload Pydantic recibido del cliente: {payload}")
    print(f"[API GATEWAY DEBUG /api/v1/prompts/] Valor de payload.zep_session_id: {payload.zep_session_id}")
    orchestrator_payload_data = payload.model_dump(exclude_none=True)
    print(f"[API GATEWAY DEBUG /api/v1/prompts/] Payload a enviar al Orchestrator (orchestrator_payload_data): {orchestrator_payload_data}")
    headers_to_orchestrator = {"X-User-ID": user_id, "Content-Type": "application/json"}
    target_url = f"{ORCHESTRATOR_SERVICE_URL}/internal/workflows"
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            print(f"API Gateway: Llamando a Orchestrator en {target_url} con payload: {orchestrator_payload_data} y headers: {headers_to_orchestrator}")
            response = await client.post(target_url, json=orchestrator_payload_data, headers=headers_to_orchestrator)
            if 200 <= response.status_code < 300: return response.json()
            elif response.status_code == status.HTTP_202_ACCEPTED:
                 print("API Gateway: Orchestrator aceptó la solicitud (202). Devolviendo cuerpo si existe.")
                 try: return response.json()
                 except: return Response(status_code=status.HTTP_202_ACCEPTED)
            else:
                error_text = response.text
                print(f"API Gateway: Error del Orchestrator: {response.status_code} - {error_text[:500]}")
                detail_msg = f"Error del servicio de orquestación: {response.status_code}"
                try: error_json = response.json(); detail_msg = error_json.get("detail", detail_msg)
                except:
                    if error_text: detail_msg = f"Error del servicio de orquestación: {response.status_code} - {error_text[:200]}"
                raise HTTPException(status_code=response.status_code, detail=detail_msg)
        except httpx.RequestError as e: print(f"API Gateway: Error conectando con Orchestrator: {e}"); raise HTTPException(status.HTTP_503_SERVICE_UNAVAILABLE, "Servicio de orquestación no disponible.")
        except Exception as e_general:
            print(f"API Gateway: Error inesperado procesando /api/v1/prompts/: {e_general}")
            traceback.print_exc()
            raise HTTPException(status.HTTP_500_INTERNAL_SERVER_ERROR, "Error interno del servidor en API Gateway.")

@app.get("/api/v1/users/health_debug_user_service", tags=["User Service (Debug)"])
async def forward_to_user_service_health_debug(request: Request, user_id: Annotated[str, Depends(get_current_user_id_from_token)]):
    return await _forward_request(request, USER_SERVICE_URL, "/health_debug_user_service", pass_user_id=False)

@app.api_route("/api/v1/users/{user_path:path}", methods=["GET", "PUT", "PATCH"])
async def forward_to_user_service(request: Request, user_path: str):
    return await _forward_request(request, USER_SERVICE_URL, f"/users/{user_path}", pass_user_id=True)

@app.get("/api/v1/gateway/health", tags=["Health"])
async def gateway_health_check(): return {"status": "healthy", "service": "API Gateway", "version": app.version}

@app.on_event("startup")
async def api_gateway_startup(): print(f"API GW Startup. JWT Secret Suffix: {JWT_SECRET_KEY[-5:]}")

@app.on_event("shutdown")
async def api_gateway_shutdown(): print("API GW Shutdown.")

print(f"[BACKEND_API_MAIN.PY] Loaded. App: '{app.title}'.")