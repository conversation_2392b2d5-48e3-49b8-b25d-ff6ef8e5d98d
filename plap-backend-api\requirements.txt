# Framework web y servidor ASGI
fastapi==0.111.0
uvicorn[standard]==0.30.1 # Incluye [standard] para dependencias opcionales como websockets si las usaras, o quítalo

# Validación de datos (aunque muchos schemas vienen de shared-models, puede usarlos)
pydantic==2.7.1 

# Cliente HTTP ASÍNCRONO para llamar a servicios internos (MUY IMPORTANTE)
httpx==0.27.0

# Manejo de JWT (para verificar tokens recibidos del frontend)
PyJWT==2.8.0

# Para leer variables de .env directamente
python-dotenv==1.0.0 

# --- Opcional: Dependencias si el endpoint /prompts/suggest se implementa AQUÍ ---
# Si la lógica de llamar a Zep/Qdrant para sugerencias está en este servicio
# en lugar de en plap-agents, necesitarías estos clientes aquí también:

# qdrant-client==1.9.0
# zep-cloud 
# google-cloud-aiplatform # Si necesita llamar a Google Embedding API directamente

# --- Opcional: Para manejar File Uploads si se implementa aquí ---
# python-multipart==0.0.9 
# aiofiles==23.2.1
