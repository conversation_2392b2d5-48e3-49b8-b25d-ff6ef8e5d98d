# plap-backend-api/router.py
from fastapi import APIRouter, Request, HTTPException, status, Depends, Header
from fastapi.responses import JSONResponse, StreamingResponse
import httpx # Cliente HTTP asíncrono
import os
from typing import Annotated, Any, Optional, Dict # Asegúrate que Dict está importado
import json
import traceback # Para imprimir tracebacks completos en errores inesperados

# Importa la dependencia de autenticación
from dependencies import get_current_user_id_from_token 

# Tipo anotado para la dependencia
AuthenticatedUserId = Annotated[str, Depends(get_current_user_id_from_token)]

# URLs INTERNAS de los servicios (leídas de .env o definidas aquí)
AUTH_SERVICE_URL = os.getenv("AUTH_SERVICE_INTERNAL_URL", "http://auth-service:80")
USER_SERVICE_URL = os.getenv("USER_SERVICE_INTERNAL_URL", "http://user-service:80")
PROMPT_LIBRARY_SERVICE_URL = os.getenv("PROMPT_LIBRARY_SERVICE_INTERNAL_URL", "http://prompt-library-service:80")
ORCHESTRATOR_SERVICE_URL = os.getenv("ORCHESTRATOR_SERVICE_INTERNAL_URL", "http://orchestrator:80")
AGENTS_SERVICE_URL = os.getenv("AGENTS_SERVICE_INTERNAL_URL", "http://agents:80")

# Función helper para reenviar peticiones (maneja errores comunes)
async def forward_request(
    request: Request,
    target_url: str,
    target_path: str,
    user_id: Optional[str] = None, 
    json_body_override: Optional[Dict[Any, Any]] = None
):
    """Reenvía una petición a un servicio interno."""
    async with httpx.AsyncClient(base_url=target_url, timeout=30.0) as client:
        excluded_headers = ['host', 'connection', 'accept-encoding', 'content-length', 'authorization']
        internal_headers = {
            key: value for key, value in request.headers.items() if key.lower() not in excluded_headers
        }
        
        if user_id:
            internal_headers["X-User-ID"] = user_id
            print(f"--- DEBUG forward_request: Añadiendo X-User-ID: {user_id} para {target_url}{target_path}")

        internal_url = target_path
        body_content: Optional[bytes] = None
        if json_body_override is not None:
            internal_headers["content-type"] = "application/json"
            body_content = json.dumps(json_body_override).encode('utf-8')
        elif request.method not in ["GET", "HEAD", "DELETE"]:
            body_content = await request.body()
        
        try:
            print(f"--- DEBUG forward_request: Enviando {request.method} a {target_url}{internal_url} con headers: {internal_headers.keys()}") # Log headers
            response = await client.request(
                method=request.method,
                url=internal_url,
                headers=internal_headers,
                content=body_content,
                params=request.query_params
            )
            
            response_body = await response.aread()
            response_headers = dict(response.headers)
            excluded_response_headers = ['transfer-encoding', 'connection', 'keep-alive', 'proxy-authenticate', 'proxy-authorization', 'te', 'trailers', 'upgrade']
            for h in excluded_response_headers:
                response_headers.pop(h, None)

            return StreamingResponse(
                iter([response_body]),
                status_code=response.status_code,
                headers=response_headers
            )

        except httpx.ConnectError as e:
            print(f"Gateway Error: Connection Error forwarding to {target_url}{internal_url}: {e}")
            raise HTTPException(status_code=status.HTTP_503_SERVICE_UNAVAILABLE, detail=f"Service '{target_url.split('//')[1].split(':')[0]}' unavailable.")
        except httpx.TimeoutException as e:
            print(f"Gateway Error: Timeout Error forwarding to {target_url}{internal_url}: {e}")
            raise HTTPException(status_code=status.HTTP_504_GATEWAY_TIMEOUT, detail=f"Request to service '{target_url.split('//')[1].split(':')[0]}' timed out.")
        except httpx.HTTPStatusError as e: 
            print(f"Gateway Error: HTTP Error {e.response.status_code} from {target_url}{internal_url}")
            error_content = await e.response.aread()
            return StreamingResponse(iter([error_content]), status_code=e.response.status_code, headers=e.response.headers)
        except Exception as e:
            print(f"Gateway Error: Unexpected Error forwarding request to {target_url}{internal_url}:")
            traceback.print_exc() 
            raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR, detail="Internal gateway error.")


# --- Routers ---

auth_router = APIRouter(prefix="/auth", tags=["Authentication"])

@auth_router.post("/signup")
async def signup_proxy(request: Request):
    return await forward_request(request, AUTH_SERVICE_URL, "/auth/signup")

@auth_router.post("/login")
async def login_proxy(request: Request):
    return await forward_request(request, AUTH_SERVICE_URL, "/auth/login")

user_router = APIRouter(
    prefix="/users",
    tags=["User Profile"],
    dependencies=[Depends(get_current_user_id_from_token)]
)

@user_router.get("/me", response_model=Any) # response_model=Any para flexibilidad
async def get_my_profile(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /users/me: current_user_id inyectado: {current_user_id}")
    return await forward_request(
        request, 
        USER_SERVICE_URL, 
        f"/internal/users/{current_user_id}",
        user_id=current_user_id
    )

@user_router.patch("/me", response_model=Any)
async def update_my_profile(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py PATCH /users/me: current_user_id inyectado: {current_user_id}")
    # El cuerpo de la petición se tomará de 'request' dentro de forward_request
    return await forward_request(
        request, 
        USER_SERVICE_URL, 
        f"/internal/users/{current_user_id}",
        user_id=current_user_id
    )

@user_router.get("/me/credits")
async def get_credits_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /users/me/credits: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, USER_SERVICE_URL, f"/internal/users/{current_user_id}/credits", user_id=current_user_id)

@user_router.get("/me/usage")
async def get_usage_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /users/me/usage: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, USER_SERVICE_URL, f"/internal/users/{current_user_id}/usage", user_id=current_user_id)

@user_router.get("/me/billing")
async def get_billing_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /users/me/billing: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, USER_SERVICE_URL, f"/internal/users/{current_user_id}/billing", user_id=current_user_id)

@user_router.get("/me/invoices")
async def get_invoices_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /users/me/invoices: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, USER_SERVICE_URL, f"/internal/users/{current_user_id}/invoices", user_id=current_user_id)

@user_router.patch("/me/notifications")
async def update_notifications_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py PATCH /users/me/notifications: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, USER_SERVICE_URL, f"/internal/users/{current_user_id}/notifications", user_id=current_user_id)

prompt_router = APIRouter(
    prefix="/prompts",
    tags=["Prompts & Suggestions"],
    dependencies=[Depends(get_current_user_id_from_token)]
)

@prompt_router.get("/")
async def list_prompts_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /prompts/: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, PROMPT_LIBRARY_SERVICE_URL, "/internal/prompts", user_id=current_user_id)

@prompt_router.post("/save") 
async def save_prompt_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py POST /prompts/save: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, PROMPT_LIBRARY_SERVICE_URL, "/internal/prompts/save", user_id=current_user_id)

@prompt_router.get("/{prompt_id}") 
async def get_prompt_or_workflow_proxy(prompt_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py GET /prompts/{{prompt_id}}: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, PROMPT_LIBRARY_SERVICE_URL, f"/internal/prompts/{prompt_id}", user_id=current_user_id)

@prompt_router.delete("/{prompt_id}")
async def delete_prompt_proxy(prompt_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py DELETE /prompts/{{prompt_id}}: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, PROMPT_LIBRARY_SERVICE_URL, f"/internal/prompts/{prompt_id}", user_id=current_user_id)

@prompt_router.post("/{prompt_id}/duplicate")
async def duplicate_prompt_proxy(prompt_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py POST /prompts/{{prompt_id}}/duplicate: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, PROMPT_LIBRARY_SERVICE_URL, f"/internal/prompts/{prompt_id}/duplicate", user_id=current_user_id)

@prompt_router.post("/suggest")
async def suggest_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py POST /prompts/suggest: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, AGENTS_SERVICE_URL, "/process/suggest", user_id=current_user_id)

workflow_router = APIRouter(
    prefix="/prompts",
    tags=["Workflow Actions"],
    dependencies=[Depends(get_current_user_id_from_token)]
)

@workflow_router.post("/") 
async def initiate_workflow_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py POST /prompts/ (initiate workflow): current_user_id inyectado: {current_user_id}")
    return await forward_request(request, ORCHESTRATOR_SERVICE_URL, "/internal/workflows", user_id=current_user_id)

@workflow_router.put("/{workflow_id}/guided/recommendation")
async def confirm_recommendation_proxy(workflow_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py PUT /prompts/{{workflow_id}}/guided/recommendation: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, ORCHESTRATOR_SERVICE_URL, f"/internal/workflows/{workflow_id}/guided/recommendation", user_id=current_user_id)

@workflow_router.put("/{workflow_id}/guided/refinement")
async def submit_refinement_proxy(workflow_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py PUT /prompts/{{workflow_id}}/guided/refinement: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, ORCHESTRATOR_SERVICE_URL, f"/internal/workflows/{workflow_id}/guided/refinement", user_id=current_user_id)

@workflow_router.put("/{workflow_id}/execute")
async def execute_workflow_proxy(workflow_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py PUT /prompts/{{workflow_id}}/execute: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, ORCHESTRATOR_SERVICE_URL, f"/internal/workflows/{workflow_id}/execute", user_id=current_user_id)

@workflow_router.post("/{workflow_id}/review")
async def review_workflow_proxy(workflow_id: str, request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py POST /prompts/{{workflow_id}}/review: current_user_id inyectado: {current_user_id}")
    return await forward_request(request, ORCHESTRATOR_SERVICE_URL, f"/internal/workflows/{workflow_id}/review_action", user_id=current_user_id)

file_upload_router = APIRouter(
     prefix="/files",
     tags=["File Upload"],
     dependencies=[Depends(get_current_user_id_from_token)]
)

@file_upload_router.post("/upload-context")
async def upload_proxy(request: Request, current_user_id: AuthenticatedUserId):
    print(f"--- DEBUG router.py POST /files/upload-context: current_user_id inyectado: {current_user_id}")
    print("Advertencia: El reenvío de archivos multipart no está completamente implementado en forward_request.")
    return await forward_request(request, AGENTS_SERVICE_URL, "/context/upload", user_id=current_user_id)