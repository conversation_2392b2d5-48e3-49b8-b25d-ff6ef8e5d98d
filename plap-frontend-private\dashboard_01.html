<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Area - Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Manrope Font -->
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            /* TYPOGRAPHY */
            --font-header: 'Orbitron', sans-serif;
            --font-body: '<PERSON>rop<PERSON>', sans-serif;

            /* NEW COLOR PALETTE */
            --color-vibrant-magenta: #C645F9;
            --color-indigo-blue: #5E6CE7;
            --color-pure-white: #FFFFFF;
            --color-deep-purple-dark: #0D0425;

            /* MAPPING TO UI ROLES */
            --color-bg: #F4F6FA;
            --color-sidebar-bg: var(--color-pure-white);
            --color-card-bg: var(--color-pure-white);
            
            --color-text: var(--color-deep-purple-dark);
            --color-text-muted: #5A5D7E;
            --color-text-on-dark: var(--color-pure-white);

            --color-cta: var(--color-indigo-blue);
            --color-cta-text: var(--color-pure-white);

            --color-accent-start: var(--color-vibrant-magenta);
            --color-accent-middle: color-mix(in srgb, var(--color-vibrant-magenta) 50%, var(--color-indigo-blue) 50%);
            --color-accent-end: var(--color-indigo-blue);
            
            --color-secondary-accent-start: color-mix(in srgb, var(--color-vibrant-magenta) 70%, var(--color-pure-white) 30%);
            --color-secondary-accent-end: color-mix(in srgb, var(--color-indigo-blue) 70%, var(--color-pure-white) 30%);

            --color-icon-gray: #888CAC;
            --color-placeholder: #A0A3C0;
            --color-hover-bg: rgba(94, 108, 231, 0.07);
            --color-border-light: #E0E2F0;
            --color-border-input-focus: var(--color-cta);

            --color-star-active: var(--color-vibrant-magenta);

            --sidebar-width: 280px;
            --header-height: 70px;
            --border-radius-card: 16px;
            --border-radius-pill: 9999px;
            --border-radius-element: 8px;

            --color-shadow-outset-primary: #DDE2F0; /* Adjusted shadow color */
            --color-shadow-outset-secondary: #E8ECF8; /* Adjusted shadow color */
        }

        /* Global Animations (from original code) */
        @keyframes synchronizedNeonShine { /* ... */ }
        @keyframes signUpGradientAnimation { /* ... */ }
        @keyframes spin { /* ... */ }
        @keyframes fadeInDown { /* ... */ }
        @keyframes fadeInUp { /* ... */ }
        /* Ensure these are defined if used, or remove if not */
        @keyframes synchronizedNeonShine {0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}
        @keyframes signUpGradientAnimation {0%{background-position:0% 50%}50%{background-position:100% 50%}100%{background-position:0% 50%}}
        @keyframes spin {0%{transform:rotate(0deg)}100%{transform:rotate(360deg)}}
        @keyframes fadeInDown {from{opacity:0;transform:translateY(-15px)}to{opacity:1;transform:translateY(0)}}
        @keyframes fadeInUp {from{opacity:0;transform:translateY(15px)}to{opacity:1;transform:translateY(0)}}


        body {
            margin: 0; font-family: var(--font-body); background-color: var(--color-bg);
            color: var(--color-text); display: flex; min-height: 100vh; overflow-x: hidden;
            -webkit-font-smoothing: antialiased; -moz-osx-font-smoothing: grayscale;
        }

        /* --- SIDEBAR --- */
        .sidebar {
            width: var(--sidebar-width); background-color: var(--color-sidebar-bg);
            border-right: 1px solid var(--color-border-light); padding: 25px 0;
            display: flex; flex-direction: column; position: fixed;
            left: 0; top: 0; bottom: 0; z-index: 1000;
            box-shadow: 3px 0 20px rgba(13, 4, 37, 0.03); /* Subtle dark purple shadow */
            transition: transform 0.3s ease-in-out;
        }
        .sidebar-logo {
            font-family: var(--font-header); font-size: 1.8rem; font-weight: 700;
            color: var(--color-indigo-blue); padding: 0 30px; margin-bottom: 35px; text-align: left;
        }
        .sidebar-nav ul { list-style: none; padding: 0; margin: 0; }
        .sidebar-nav .nav-item { margin-bottom: 6px; }
        .sidebar-nav .nav-link {
            display: flex; align-items: center; padding: 13px 30px;
            color: var(--color-text-muted); text-decoration: none;
            font-size: 0.95rem; font-weight: 500; /* Manrope regular/medium */
            transition: background-color 0.2s ease, color 0.2s ease, border-left-color 0.2s ease;
            border-left: 4px solid transparent;
        }
        .sidebar-nav .nav-link i { margin-right: 18px; width: 20px; text-align: center; font-size: 1.1em; }
        .sidebar-nav .nav-link:hover {
            background-color: var(--color-hover-bg);
            color: var(--color-cta);
        }
        .sidebar-nav .nav-link.active {
            color: var(--color-cta);
            background-color: var(--color-hover-bg);
            border-left-color: var(--color-cta);
            font-weight: 600; /* Manrope semibold */
        }
        .sidebar-nav .nav-link.active i { color: var(--color-cta); }
        .sidebar-user-profile {
            margin-top: auto; padding: 20px 30px; border-top: 1px solid var(--color-border-light);
            display: flex; align-items: center; gap: 15px;
        }
        .sidebar-user-profile img { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; border: 2px solid var(--color-border-light); }
        .sidebar-user-profile .user-info p { margin: 0; }
        .sidebar-user-profile .user-info .user-name { font-weight: 600; font-size: 0.9rem; color: var(--color-text); }
        .sidebar-user-profile .user-info .user-email { font-size: 0.75rem; color: var(--color-text-muted); }

        /* --- MAIN CONTENT WRAPPER & APP HEADER --- */
        .main-content-wrapper { margin-left: var(--sidebar-width); width: calc(100% - var(--sidebar-width)); display: flex; flex-direction: column; transition: margin-left 0.3s ease-in-out; }
        .app-header {
            height: var(--header-height); background-color: var(--color-sidebar-bg); /* Consistent with sidebar */
            border-bottom: 1px solid var(--color-border-light); display: flex;
            align-items: center; justify-content: space-between; padding: 0 30px;
            position: sticky; top: 0; z-index: 900;
            box-shadow: 0 2px 10px rgba(13, 4, 37, 0.02);
        }
        .header-title-container { display: flex; align-items: center; }
        .menu-toggle-btn { display: none; background: none; border: none; font-size: 1.5rem; cursor: pointer; color: var(--color-text); margin-right: 15px; padding: 5px;}
        .header-title { font-family: var(--font-header); font-size: 1.5rem; font-weight: 600; color: var(--color-text); }
        .header-actions { display: flex; align-items: center; gap: 20px; }

        .btn-gradient-action {
            position: relative; display: inline-flex; align-items: center; gap: 8px;
            padding: 10px 22px; border-radius: var(--border-radius-pill);
            font-size: 0.9rem; font-weight: 600; /* Manrope semibold */
            cursor: pointer; transition: all 0.3s ease; text-decoration: none; line-height: 1.5;
            background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-end)); /* Simpler gradient */
            background-size: 200% 100%; /* Adjusted for 2 colors */
            color: var(--color-text-on-dark); border: none;
            box-shadow: 0 6px 15px -4px color-mix(in srgb, var(--color-indigo-blue) 40%, transparent);
            animation: signUpGradientAnimation 15s linear infinite;
            overflow: hidden;
        }
        .btn-gradient-action:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 0 20px 0px color-mix(in srgb, var(--color-vibrant-magenta) 30%, transparent),
                        0 8px 20px -6px color-mix(in srgb, var(--color-indigo-blue) 30%, transparent);
        }
        .btn-gradient-action i { font-size: 1.05em; }

        .stylish-action-button {
            position: relative; display: inline-flex; align-items: center; gap: 8px;
            background-color: var(--color-pure-white); color: var(--color-text);
            padding: 8px 15px; border-radius: var(--border-radius-element); border: 1px solid var(--color-border-light);
            font-size: 0.85rem; font-weight: 500; /* Manrope medium */
            cursor: pointer;
            transition: box-shadow 0.2s ease, background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
            text-decoration: none; line-height: 1.5; z-index: 1; overflow: hidden;
            box-shadow: 0 2px 4px rgba(13,4,37,0.03);
        }
        .stylish-action-button::before { /* Subtle gradient border accent on hover/active */
            content: ""; position: absolute; bottom: -1px; left: 0; right: 0; height: 2.5px;
            background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-end));
            opacity: 0; transition: opacity 0.3s ease; z-index: -1;
            border-radius: 0 0 var(--border-radius-element) var(--border-radius-element);
        }
        .stylish-action-button:hover {
            border-color: var(--color-cta);
            color: var(--color-cta);
            box-shadow: 0 4px 10px rgba(94,108,231,0.1);
        }
        .stylish-action-button:hover::before { opacity: 1; }
        .stylish-action-button:hover i { color: var(--color-cta); }
        .stylish-action-button.delete { color: var(--color-vibrant-magenta); border-color: color-mix(in srgb, var(--color-vibrant-magenta) 30%, transparent); }
        .stylish-action-button.delete:hover { background-color: color-mix(in srgb, var(--color-vibrant-magenta) 10%, var(--color-pure-white) 90%); color: var(--color-vibrant-magenta); border-color: var(--color-vibrant-magenta); }
        .stylish-action-button.delete:hover::before { background: var(--color-vibrant-magenta); }


        .user-menu { position: relative; }
        .user-menu-btn { background: none; border: none; cursor: pointer; display: flex; align-items: center; gap: 10px; padding: 8px; border-radius: var(--border-radius-element); }
        .user-menu-btn:hover { background-color: var(--color-hover-bg); }
        .user-menu-btn img { width: 36px; height: 36px; border-radius: 50%; object-fit: cover; }
        .user-menu-btn .user-name-header { font-weight: 500; font-size:0.9rem; color: var(--color-text); }
        .user-menu-btn .fa-chevron-down { color: var(--color-text-muted); font-size: 0.8em; }
        .user-dropdown {
            position: absolute; top: calc(100% + 10px); right: 0;
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-element); box-shadow: 0 8px 25px rgba(13,4,37,0.07);
            width: 200px; padding: 8px 0; z-index: 1010;
            opacity: 0; visibility: hidden; transform: translateY(10px);
            transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s ease;
        }
        .user-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0); }
        .user-dropdown a { display: block; padding: 10px 15px; color: var(--color-text); text-decoration: none; font-size: 0.9rem; }
        .user-dropdown a:hover { background-color: var(--color-hover-bg); }
        .user-dropdown a i { margin-right: 10px; color: var(--color-text-muted); }
        .user-dropdown .divider { height: 1px; background-color: var(--color-border-light); margin: 8px 0; }

        /* --- CONTENT AREA & GENERAL STYLES --- */
        .content-area { flex-grow: 1; padding: 30px; overflow-y: auto; }
        .section-title {
            font-family: var(--font-header); /* Use header font for section titles */
            font-size: 1.6rem; font-weight: 700; margin-bottom: 25px;
            color: var(--color-text); padding-bottom: 12px;
            border-bottom: 1px solid var(--color-border-light);
            display: flex; align-items: center; gap: 12px;
        }
        .section-title i { color: var(--color-cta); font-size: 1.1em; }
        .grid-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 25px; }
        .text-muted { color: var(--color-text-muted); }
        .hidden { display: none !important; }

        /* --- DASHBOARD Specific Styles --- */
        #dashboardView .welcome-section {
            background: linear-gradient(135deg, var(--color-accent-start), var(--color-accent-end));
            padding: 30px 40px; border-radius: var(--border-radius-card); margin-bottom: 30px;
            color: var(--color-text-on-dark);
            box-shadow: 0 10px 30px -5px color-mix(in srgb, var(--color-indigo-blue) 30%, transparent);
        }
        #dashboardView .welcome-section h2 { font-family: var(--font-header); font-size: 2rem; margin-top: 0; margin-bottom: 5px; font-weight: 700; }
        #dashboardView .welcome-section p { font-size: 1rem; opacity: 0.85; margin-bottom: 0; font-weight: 400; }
        #dashboardView .dashboard-card {
            background-color: var(--color-card-bg); border-radius: var(--border-radius-card); padding: 25px;
            box-shadow: 0 8px 25px rgba(13,4,37,0.04); transition: transform 0.2s ease, box-shadow 0.2s ease;
            border: 1px solid var(--color-border-light);
        }
        #dashboardView .dashboard-card:hover { transform: translateY(-4px); box-shadow: 0 12px 30px rgba(13,4,37,0.06); }
        #dashboardView .card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        #dashboardView .card-header h3 { margin: 0; font-size: 1.1rem; font-weight: 600; /* Manrope semibold */ color: var(--color-text); }
        #dashboardView .card-header .icon { font-size: 1.5rem; color: var(--color-cta); opacity: 0.8; }
        #dashboardView .enhanced-prompt-item {
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-card); padding: 20px;
            margin-bottom: 20px; box-shadow: 0 4px 15px rgba(13,4,37,0.03);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        #dashboardView .enhanced-prompt-item:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(13,4,37,0.05); }
        #dashboardView .enhanced-prompt-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        #dashboardView .enhanced-prompt-item-header h5 { margin: 0; font-size: 1.05rem; font-weight: 600; color: var(--color-text); }
        #dashboardView .enhanced-prompt-item-header .star-icon.favorited { color: var(--color-star-active); }
        #dashboardView .enhanced-prompt-item-meta-minimal { display: flex; flex-wrap: wrap; gap: 12px; font-size: 0.75rem; color: var(--color-text-muted); margin-bottom: 12px; padding: 8px; background-color: var(--color-bg); border-radius: 6px; }
        #dashboardView .meta-item { display: flex; align-items: center; gap: 5px; }
        #dashboardView .meta-item i { color: var(--color-cta); font-size: 0.9em; }
        #dashboardView .full-prompt-display-summary { font-size: 0.85rem; line-height: 1.6; color: var(--color-text-muted); max-height: 60px; overflow: hidden; text-overflow: ellipsis; display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; margin-bottom: 15px; }
        #dashboardView .prompt-actions { display: flex; gap: 10px; margin-top: 15px; }

        /* --- MY PROMPTS Specific Styles --- */
        .prompts-controls-bar {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 25px; padding: 15px; background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card); box-shadow: 0 4px 15px rgba(13,4,37,0.03);
            flex-wrap: wrap; gap: 15px; border: 1px solid var(--color-border-light);
        }
        .prompts-view-toggle { display: flex; gap: 0; }
        .prompts-view-toggle button {
            background-color: transparent; border: 1px solid var(--color-border-light);
            padding: 8px 15px; cursor: pointer; color: var(--color-text-muted);
            font-weight: 500;
            transition: background-color 0.2s, color 0.2s, border-color 0.2s;
        }
        .prompts-view-toggle button:first-child { border-radius: var(--border-radius-element) 0 0 var(--border-radius-element); border-right:none;}
        .prompts-view-toggle button:last-child { border-radius: 0 var(--border-radius-element) var(--border-radius-element) 0; }
        .prompts-view-toggle button.active { background-color: var(--color-cta); color: var(--color-text-on-dark); border-color: var(--color-cta); }
        .prompts-view-toggle button:hover:not(.active) { background-color: var(--color-hover-bg); border-color: color-mix(in srgb, var(--color-cta) 50%, var(--color-border-light) 50%);}

        .prompts-filter-panel {
            display: flex; flex-wrap: wrap; gap: 15px; align-items: center;
            padding: 15px; background-color: var(--color-bg); /* Match page bg */
            border-radius: var(--border-radius-element); margin-bottom: 25px;
            border: 1px solid var(--color-border-light);
        }
        .prompts-filter-panel .filter-group { display: flex; align-items: center; gap: 8px; }
        .prompts-filter-panel .filter-group label { font-size: 0.85rem; font-weight: 500; color: var(--color-text-muted); }
        .prompts-filter-panel .form-control, .prompts-filter-panel .btn-filter-toggle {
            padding: 8px 12px; border-radius: var(--border-radius-element);
            border: 1px solid var(--color-border-light); font-size: 0.9rem;
            background-color: var(--color-card-bg); color: var(--color-text);
            font-weight: 500;
        }
        .prompts-filter-panel .form-control:focus { border-color: var(--color-cta); box-shadow: 0 0 0 2px color-mix(in srgb, var(--color-cta) 20%, transparent); outline: none; }
        .prompts-filter-panel .btn-filter-toggle { cursor: pointer; transition: background-color 0.2s, color 0.2s, border-color 0.2s, box-shadow 0.2s; }
        .prompts-filter-panel .btn-filter-toggle.active {
            background-color: var(--color-cta); color: var(--color-text-on-dark); border-color: var(--color-cta);
            box-shadow: 0 2px 8px color-mix(in srgb, var(--color-cta) 20%, transparent);
        }
        .prompts-filter-panel .btn-filter-toggle:hover:not(.active) { background-color: var(--color-hover-bg); border-color: color-mix(in srgb, var(--color-cta) 50%, var(--color-border-light) 50%);}

        .prompt-card-item-v2 {
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-card);
            box-shadow: 0 4px 15px rgba(13,4,37,0.03);
            transition: transform 0.25s ease, box-shadow 0.25s ease;
            display: flex; flex-direction: column; padding: 20px;
        }
        .prompt-card-item-v2:hover { transform: translateY(-5px); box-shadow: 0 10px 30px rgba(13,4,37,0.06); }
        .prompt-card-header-v2 { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px; }
        .prompt-card-title-v2 { font-size: 1.1rem; font-weight: 600; /* Manrope Semibold */ color: var(--color-text); margin:0; line-height: 1.3; }
        .prompt-card-fav-icon-v2 { font-size: 1.1rem; color: var(--color-icon-gray); cursor:pointer; transition: color 0.2s ease, transform 0.2s ease; padding:5px;}
        .prompt-card-fav-icon-v2:hover { transform: scale(1.2); color: var(--color-vibrant-magenta);}
        .prompt-card-fav-icon-v2.favorited { color: var(--color-star-active); }

        .prompt-card-tags-v2 { display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 12px; }
        .prompt-card-tags-v2 .tag {
            background-color: var(--color-hover-bg);
            color: var(--color-indigo-blue);
            border: 1px solid color-mix(in srgb, var(--color-indigo-blue) 20%, transparent);
            padding: 4px 10px;
            border-radius: var(--border-radius-pill); font-size: 0.7rem; font-weight: 600; /* Manrope Semibold */
        }
        .prompt-card-summary-v2 {
            font-size: 0.9rem; line-height: 1.6; color: var(--color-text-muted); margin-bottom: 15px;
            flex-grow: 1; overflow: hidden; text-overflow: ellipsis;
            display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;
        }
        .prompt-card-footer-v2 { display: flex; justify-content: space-between; align-items: center; margin-top: auto; border-top: 1px solid var(--color-border-light); padding-top: 15px; }
        .prompt-card-stats-v2 { font-size: 0.8rem; color: var(--color-text-muted); }
        .prompt-card-stats-v2 i { margin-right: 4px; }
        .prompt-card-actions-v2 { display: flex; gap: 10px; }

        /* --- HISTORY Specific Styles --- */
        .history-list-item {
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-element); padding: 15px 20px; margin-bottom: 15px;
            display: flex; justify-content: space-between; align-items: center;
            transition: box-shadow 0.2s ease;
        }
        .history-list-item:hover { box-shadow: 0 4px 12px rgba(13,4,37,0.04); }
        .history-item-details { flex-grow: 1; }
        .history-item-prompt { font-size: 0.9rem; margin-bottom: 5px; white-space: nowrap; overflow: hidden; text-overflow: ellipsis; max-width: 400px; color:var(--color-text); }
        .history-item-meta { font-size: 0.8rem; color: var(--color-text-muted); }
        .history-item-meta .model-chip {
            background-color: var(--color-hover-bg);
            color: var(--color-indigo-blue);
            border: 1px solid color-mix(in srgb, var(--color-indigo-blue) 20%, transparent);
            padding: 2px 6px; border-radius: var(--border-radius-pill); font-size:0.7rem; margin-left:8px; font-weight: 500;
        }
        .history-item-actions { display: flex; gap: 10px; }


        /* --- PLAYGROUND - STYLES FROM PUBLIC HTML (SCOPED) --- */
        #playgroundView .search-area-container {
            display: flex; flex-direction: column; align-items: center;
            width: 100%; max-width: 700px; margin: 20px auto;
            position: relative; animation: fadeInUp 1s ease-out 0.2s;
            animation-fill-mode: backwards; z-index: 2;
        }
        #playgroundView .search-bar-card {
            width: 100%; background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card);
            box-shadow: 0 10px 30px rgba(13,4,37, 0.05), 0 6px 15px rgba(13,4,37,0.03);
            border: 1px solid var(--color-border-light); /* Added subtle border */
            position: relative; display: flex; flex-direction: column;
            box-sizing: border-box; transition: box-shadow 0.4s ease; overflow: hidden; z-index: 1;
        }
        #playgroundView .search-bar-card::before { /* Gradient border for focus */
            content: ''; position: absolute; top: -1px; left: -1px; right: -1px; bottom: -1px; /* Cover border */
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-end));
            background-size: 200% 100%; z-index: -1; border-radius: inherit; padding: 2px; /* Adjust padding for border thickness */
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; mask-composite: exclude;
            opacity: 0; transition: opacity 0.4s ease; animation: synchronizedNeonShine 10s linear infinite paused;
        }
        #playgroundView .search-bar-card.focused-card::before,
        #playgroundView .search-bar-card.always-focused::before { opacity: 1; animation-play-state: running; }
        #playgroundView .search-bar-card.focused-card,
        #playgroundView .search-bar-card.always-focused {
            box-shadow: 0 0 15px color-mix(in srgb, var(--color-vibrant-magenta) 25%, transparent),
                        0 0 25px color-mix(in srgb, var(--color-indigo-blue) 20%, transparent),
                        0 10px 30px rgba(13,4,37, 0.07);
        }
        #playgroundView .search-input {
            width: 100%; padding: 20px 20px 10px 20px; background: transparent; border: none; outline: none;
            color: var(--color-text); font-size: 1rem; font-family: var(--font-body); font-weight: 400;
            resize: none; overflow-y: hidden; line-height: 1.6; min-height: 70px; box-sizing: border-box;
        }
        #playgroundView .search-input::placeholder { color: var(--color-placeholder); opacity: 1; font-weight: 400; }
        #playgroundView .toolbar-bottom {
            display: flex; justify-content: space-between; align-items: center;
            padding: 8px 16px; background-color: transparent; /* Toolbar is part of the card */
            position: relative; z-index: 1; border-top: 1px solid var(--color-border-light);
        }
        #playgroundView .model-selector-btn {
            position: relative; background-color: var(--color-hover-bg); border: 1px solid var(--color-border-light);
            color: var(--color-text); padding: 8px 15px; border-radius: var(--border-radius-element);
            cursor: pointer; font-size: 0.85rem; font-weight: 500;
            display: flex; align-items: center; gap: 8px;
            transition: box-shadow 0.2s ease, border-color 0.2s ease; z-index: 3; overflow: hidden;
        }
        #playgroundView .model-selector-btn::before { /* Gradient border on hover */
            content: ""; position: absolute; bottom: -1px; left:0; right:0; height: 2px;
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-end));
            opacity: 0; transition: opacity 0.3s ease;
        }
        #playgroundView .model-selector-btn:hover {
             border-color: var(--color-cta);
             box-shadow: 0 0 8px color-mix(in srgb, var(--color-cta) 20%, transparent);
        }
        #playgroundView .model-selector-btn:hover::before { opacity: 1; }

        #playgroundView .model-dropdown {
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-border-light); /* Changed from shadow outset */
            box-shadow: 0 4px 15px rgba(13,4,37,0.05);
            /* ... rest of dropdown styles ... */
        }
        #playgroundView #playgroundModelSearchInput { border: 1px solid var(--color-border-light); }
        #playgroundView #playgroundModelSearchInput:focus { border-color: var(--color-cta); box-shadow: 0 0 0 2px color-mix(in srgb, var(--color-cta) 20%, transparent); }
        #playgroundView .model-dropdown li:hover { background-color: var(--color-hover-bg); }
        #playgroundView .model-dropdown li.selected-model-item { background-color: var(--color-cta); color: var(--color-text-on-dark); }
        #playgroundView .action-icon { color: var(--color-icon-gray); /* ... */ }
        #playgroundView .action-icon:hover { color: var(--color-cta); background-color: var(--color-hover-bg); }
        #playgroundView #dynamicContentArea { background-color: var(--color-card-bg); /* ... */ }
        #playgroundView .simple-suggestions-container { background-color: var(--color-card-bg); border: 1px solid var(--color-border-light); }
        #playgroundView .simple-suggestion-item { color: var(--color-text); border-bottom: 1px solid var(--color-border-light); }
        #playgroundView .simple-suggestion-item:hover { background-color: var(--color-hover-bg); }
        #playgroundView .favorite-prompts-section { border-top: 1px solid var(--color-border-light); }
        #playgroundView .favorite-prompts-section h4 .fa-star { color: var(--color-star-active); }
        #playgroundView .select-prompt-button { /* This button style comes from original public page */
            /* Re-style to match .stylish-action-button or keep as is if desired */
            /* For now, let's make it similar to .stylish-action-button */
            position: relative; display: inline-flex; align-items: center; gap: 8px;
            background-color: var(--color-pure-white); color: var(--color-text);
            padding: 8px 15px; border-radius: var(--border-radius-element); border: 1px solid var(--color-border-light);
            font-size: 0.85rem; font-weight: 500; cursor: pointer;
            transition: box-shadow 0.2s ease, background-color 0.2s ease, color 0.2s ease, border-color 0.2s ease;
            text-decoration: none; line-height: 1.5; z-index: 1; overflow: hidden; margin-top: 10px;
            box-shadow: 0 2px 4px rgba(13,4,37,0.03);
        }
        #playgroundView .select-prompt-button::before {
            content: ""; position: absolute; bottom: -1px; left: 0; right: 0; height: 2.5px;
            background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-end));
            opacity: 0; transition: opacity 0.3s ease; z-index: -1;
            border-radius: 0 0 var(--border-radius-element) var(--border-radius-element);
        }
        #playgroundView .select-prompt-button:hover {
            border-color: var(--color-cta); color: var(--color-cta);
            box-shadow: 0 4px 10px rgba(94,108,231,0.1);
        }
        #playgroundView .select-prompt-button:hover::before { opacity: 1; }
        #playgroundView .select-prompt-button:hover i { color: var(--color-cta); }
        #playgroundView .toolbar-bottom .playground-save-action { margin-left: auto; padding-right: 15px; }


        /* --- SETTINGS Specific Styles --- */
        #settingsView .settings-tabs { border-bottom: 1px solid var(--color-border-light); }
        #settingsView .settings-tabs .tab-link { color: var(--color-text-muted); font-weight: 500; }
        #settingsView .settings-tabs .tab-link:hover { color: var(--color-text); }
        #settingsView .settings-tabs .tab-link.active { color: var(--color-cta); border-bottom-color: var(--color-cta); font-weight: 600; }
        #settingsView .form-group label { font-weight: 600; /* Manrope Semibold */ }
        #settingsView .form-group .form-control, #settingsView .form-group textarea {
            border: 1px solid var(--color-border-light); background-color: var(--color-card-bg);
            color: var(--color-text); font-weight: 500; /* Manrope Medium */
        }
        #settingsView .form-group .form-control::placeholder, #settingsView .form-group textarea::placeholder { color: var(--color-placeholder); }
        #settingsView .form-group .form-control:focus, #settingsView .form-group textarea:focus {
            border-color: var(--color-border-input-focus);
            box-shadow: 0 0 0 3px color-mix(in srgb, var(--color-cta) 15%, transparent);
        }

        /* Responsive */
        @media (max-width: 992px) { /* ... (structure unchanged) ... */ }
        @media (max-width: 768px) { /* ... (structure unchanged) ... */ }

    </style>
</head>
<body>
    <!-- HTML Structure from previous response remains the same -->
    <aside class="sidebar" id="sidebar">
        <div class="sidebar-logo">allhub</div>
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item"><a href="#" class="nav-link active" data-view="dashboard"><i class="fas fa-th-large"></i> Dashboard</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="my-prompts"><i class="fas fa-lightbulb"></i> My Prompts</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="history"><i class="fas fa-history"></i> History</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="playground"><i class="fas fa-rocket"></i> Playground</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="settings"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>
        </nav>
        <div class="sidebar-user-profile">
            <img src="https://i.pravatar.cc/80?u=UserElenaV" alt="User Avatar">
            <div class="user-info">
                <p class="user-name">Elena V.</p>
                <p class="user-email"><EMAIL></p>
            </div>
        </div>
    </aside>

    <div class="main-content-wrapper" id="mainContentWrapper">
        <header class="app-header">
            <div class="header-title-container">
                <button class="menu-toggle-btn" id="menuToggleBtn"><i class="fas fa-bars"></i></button>
                <h2 class="header-title" id="headerTitle">Dashboard</h2>
            </div>
            <div class="header-actions">
                <a href="#" class="btn-gradient-action" id="newPromptBtn"><i class="fas fa-plus-circle"></i> New Prompt</a>
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <img src="https://i.pravatar.cc/80?u=UserElenaV" alt="User Avatar">
                        <span class="user-name-header">Elena V.</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user-circle"></i> My Profile</a>
                        <a href="#"><i class="fas fa-credit-card"></i> Subscription</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </header>

        <main class="content-area">
            <!-- DASHBOARD VIEW -->
            <div id="dashboardView" class="view-content">
                <div class="welcome-section">
                    <h2>Welcome back, Elena!</h2>
                    <p>Ready to craft some amazing prompts today?</p>
                </div>
                <div class="section-title"><i class="fas fa-chart-line"></i> Your Activity Snapshot</div>
                <div class="grid-container" style="margin-bottom: 30px;">
                    <div class="dashboard-card">
                        <div class="card-header"><h3>Saved Prompts</h3><i class="fas fa-save icon"></i></div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">27</p>
                        <p class="text-muted" style="font-size:0.9rem;">Organized and ready to use.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header"><h3>Prompts Used (Month)</h3><i class="fas fa-cogs icon"></i></div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">152</p>
                        <p class="text-muted" style="font-size:0.9rem;">Generating ideas non-stop.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header"><h3>Active Favorites</h3><i class="fas fa-star icon" style="color: var(--color-star-active);"></i></div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-star-active);">8</p>
                        <p class="text-muted" style="font-size:0.9rem;">Your star prompts at a glance.</p>
                    </div>
                </div>
                <div class="section-title"><i class="fas fa-bolt"></i> Quick Access: Favorite Prompts</div>
                <div id="favoritePromptsDashboard">
                    <div class="enhanced-prompt-item">
                        <div class="enhanced-prompt-item-header">
                            <h5>Enhance Article with CoT</h5>
                            <i class="fas fa-star star-icon favorited" title="Remove from favorites"></i>
                        </div>
                        <div class="enhanced-prompt-item-meta-minimal">
                            <span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                            <span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                            <span class="meta-item"><i class="far fa-clock"></i> Time: ~2s</span>
                        </div>
                        <p class="full-prompt-display-summary">Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process: Identify key components...</p>
                        <div class="prompt-actions">
                             <button class="stylish-action-button"><i class="fas fa-play-circle"></i> Use in Playground</button>
                             <button class="stylish-action-button"><i class="fas fa-edit"></i> Edit</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- MY PROMPTS VIEW -->
            <div id="myPromptsView" class="view-content hidden">
                <div class="section-title"><i class="fas fa-lightbulb"></i> My Prompts</div>
                <div class="prompts-controls-bar">
                    <button class="btn-gradient-action" id="createNewPromptFromMyPromptsBtn"><i class="fas fa-plus"></i> Create New Prompt</button>
                    <div class="prompts-view-toggle">
                        <button class="active" data-viewmode="grid"><i class="fas fa-th-large"></i> Grid</button>
                        <button data-viewmode="list"><i class="fas fa-list"></i> List</button>
                    </div>
                </div>
                <div class="prompts-filter-panel">
                    <div class="filter-group">
                        <label for="filterTechnique">Technique:</label>
                        <select id="filterTechnique" class="form-control">
                            <option value="">All Techniques</option>
                            <option value="cot">Chain of Thought (CoT)</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn-filter-toggle" data-filter="most-used">Most Used</button>
                        <button class="btn-filter-toggle" data-filter="recent">Recent</button>
                        <button class="btn-filter-toggle active" data-filter="favorites">Favorites <i class="fas fa-star" style="color: var(--color-star-active); margin-left: 5px;"></i></button>
                    </div>
                     <div class="filter-group" style="margin-left: auto;">
                        <input type="text" class="form-control" placeholder="Search my prompts..." style="min-width: 250px;">
                    </div>
                </div>
                <div id="myPromptsGrid" class="grid-container">
                    <div class="prompt-card-item-v2">
                        <div class="prompt-card-header-v2">
                            <h5 class="prompt-card-title-v2">Social Media Campaign Strategy</h5>
                            <i class="fas fa-star prompt-card-fav-icon-v2 favorited" title="Toggle Favorite"></i>
                        </div>
                        <div class="prompt-card-tags-v2">
                            <span class="tag">Marketing</span> <span class="tag">Strategy</span>
                        </div>
                        <p class="prompt-card-summary-v2">Outline a comprehensive social media campaign strategy...</p>
                        <div class="prompt-card-footer-v2">
                            <div class="prompt-card-stats-v2"><span><i class="fas fa-sync-alt"></i> Used: 12</span> • <span>Last used: 2d ago</span></div>
                            <div class="prompt-card-actions-v2">
                                 <button class="stylish-action-button" title="Use"><i class="fas fa-play"></i></button>
                                 <button class="stylish-action-button" title="Edit"><i class="fas fa-edit"></i></button>
                                 <button class="stylish-action-button delete" title="Delete"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HISTORY VIEW -->
            <div id="historyView" class="view-content hidden">
                 <div class="section-title"><i class="fas fa-history"></i> Prompt History</div>
                 <div class="prompts-filter-panel" style="margin-bottom:25px;">
                     <input type="date" class="form-control">
                     <select class="form-control"><option value="">All Models</option></select>
                    <input type="text" class="form-control" placeholder="Search history...">
                </div>
                <div id="promptHistoryList">
                    <div class="history-list-item">
                        <div class="history-item-details">
                            <div class="history-item-prompt">"Generate three taglines..."</div>
                            <div class="history-item-meta"><span>Oct 26, 2023</span><span class="model-chip">GPT-4</span></div>
                        </div>
                        <div class="history-item-actions">
                            <button class="stylish-action-button"><i class="fas fa-redo"></i> Re-run</button>
                            <button class="stylish-action-button"><i class="fas fa-save"></i> Save</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- PLAYGROUND VIEW -->
            <div id="playgroundView" class="view-content hidden">
                <div class="section-title"><i class="fas fa-rocket"></i> Playground</div>
                <div class="search-area-container" id="playgroundSearchAreaContainer">
                    <div class="search-bar-card" id="playgroundSearchBarCard">
                        <textarea class="search-input" id="playgroundSearchInput" placeholder="Type your prompt here..." rows="1"></textarea>
                        <div class="toolbar-bottom">
                            <div class="model-selector-container">
                                <button class="model-selector-btn" id="playgroundModelSelectorBtn">
                                    <span id="playgroundCurrentModelName">Model</span> <i class="fas fa-chevron-down"></i>
                                </button>
                            </div>
                            <div class="playground-save-action">
                                <button class="stylish-action-button" id="playgroundSavePromptBtn"><i class="fas fa-save"></i> Save This Prompt</button>
                            </div>
                            <div class="search-actions">
                                <i class="fas fa-microphone action-icon" id="playgroundMicBtn" title="Voice Input"></i>
                                <i class="fas fa-wand-magic-sparkles action-icon" id="playgroundGuidedCreateBtn" title="Guided Creation"></i>
                                <i class="fas fa-copy action-icon" id="playgroundCopyBtn" title="Copy Prompt"></i>
                                <i class="fas fa-paper-plane action-icon" id="playgroundSendBtn" title="Send Prompt"></i>
                            </div>
                        </div>
                        <div id="playgroundDynamicContentArea" class="hidden">
                            <div class="analyzing-indicator hidden" id="playgroundAnalyzingIndicator"><i class="fas fa-spinner fa-spin"></i> Analyzing...</div>
                            <div class="simple-suggestions-container hidden" id="playgroundSimplePromptSuggestionsContainer"></div>
                            <div class="enhanced-prompt-suggestions hidden" id="playgroundEnhancedPromptSuggestionsContainer"></div>
                             <div class="favorite-prompts-section hidden" id="playgroundFavoritePromptsSection">
                                <h4><i class="fas fa-star"></i> Popular Prompts</h4>
                                <div class="favorite-prompts-list" id="playgroundFavoritePromptsList"></div>
                            </div>
                        </div>
                    </div>
                    <div class="model-dropdown hidden" id="playgroundModelDropdownList">
                        <div class="model-search-container"><i class="fas fa-search"></i><input type="text" id="playgroundModelSearchInput" placeholder="Search models"></div>
                        <ul></ul>
                    </div>
                </div>
            </div>

            <!-- SETTINGS VIEW -->
            <div id="settingsView" class="view-content hidden">
                 <div class="section-title"><i class="fas fa-cog"></i> Settings</div>
                 <div class="settings-tabs">
                    <a href="#" class="tab-link active" data-tab="profile">Profile</a>
                    <a href="#" class="tab-link" data-tab="api-keys">API Keys</a>
                    <a href="#" class="tab-link" data-tab="preferences">Preferences</a>
                    <a href="#" class="tab-link" data-tab="subscription">Subscription</a>
                </div>
                <div id="profileTab" class="settings-tab-content active"><h4>Profile Information</h4> <!-- Forms here --></div>
                <div id="apiKeysTab" class="settings-tab-content"><h4>API Keys</h4> <!-- API Key Forms --></div>
                <div id="preferencesTab" class="settings-tab-content"><h4>Preferences</h4> <!-- Preference Forms --></div>
                <div id="subscriptionTab" class="settings-tab-content"><h4>Subscription</h4> <!-- Subscription Info --></div>
            </div>
        </main>
    </div>

<script>
    // --- MAIN PRIVATE AREA SCRIPT ---
    document.addEventListener('DOMContentLoaded', () => {
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const menuToggleBtn = document.getElementById('menuToggleBtn');
        const sidebar = document.getElementById('sidebar');
        const headerTitle = document.getElementById('headerTitle');
        const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
        const views = document.querySelectorAll('.view-content');
        const newPromptBtn = document.getElementById('newPromptBtn');
        const createNewPromptMyPromptsBtn = document.getElementById('createNewPromptFromMyPromptsBtn');

        if (userMenuBtn && userDropdown) { /* User menu logic */ }
        if (menuToggleBtn && sidebar) { /* Sidebar toggle logic */ }

        function switchToView(viewId) {
            navLinks.forEach(l => l.classList.remove('active'));
            const activeLink = document.querySelector(`.sidebar-nav .nav-link[data-view="${viewId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
                headerTitle.textContent = activeLink.textContent.trim();
            } else if (viewId === 'playground') {
                 headerTitle.textContent = 'Playground';
                 const playgroundLink = document.querySelector(`.sidebar-nav .nav-link[data-view="playground"]`);
                 if(playgroundLink) playgroundLink.classList.add('active');
            }

            views.forEach(view => view.classList.toggle('hidden', view.id !== `${viewId}View`));

            if (window.innerWidth <= 992 && sidebar.classList.contains('open')) sidebar.classList.remove('open');
            if (viewId === 'playground' && typeof initializePlaygroundScript === 'function' && !window.playgroundScriptInitialized) {
                initializePlaygroundScript();
                window.playgroundScriptInitialized = true; 
            }
        }

        navLinks.forEach(link => link.addEventListener('click', (e) => { e.preventDefault(); switchToView(link.dataset.view); }));
        if (newPromptBtn) newPromptBtn.addEventListener('click', (e) => { e.preventDefault(); switchToView('playground'); });
        if (createNewPromptMyPromptsBtn) createNewPromptMyPromptsBtn.addEventListener('click', (e) => { e.preventDefault(); switchToView('playground'); });
        
        const filterToggles = document.querySelectorAll('.prompts-filter-panel .btn-filter-toggle');
        filterToggles.forEach(toggle => toggle.addEventListener('click', () => toggle.classList.toggle('active')));
        
        const settingsTabLinks = document.querySelectorAll('#settingsView .settings-tabs .tab-link');
        const settingsTabContents = document.querySelectorAll('#settingsView .settings-tab-content');
        settingsTabLinks.forEach(link => {
            link.addEventListener('click', (e) => { 
                e.preventDefault();
                const tabId = link.dataset.tab;
                settingsTabLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
                settingsTabContents.forEach(content => content.classList.toggle('active', content.id === `${tabId}Tab`));
             });
        });

        const initialActiveLink = document.querySelector('.sidebar-nav .nav-link.active') || (navLinks.length > 0 ? navLinks[0] : null);
        if (initialActiveLink) switchToView(initialActiveLink.dataset.view);
    });

    // --- PLAYGROUND SCRIPT (ADAPTED FROM PUBLIC HTML - Scoped and Simplified) ---
    function initializePlaygroundScript() {
        console.log("Initializing Playground Script with Manrope theme...");
        const searchAreaContainer = document.getElementById('playgroundSearchAreaContainer');
        const searchBarCard = document.getElementById('playgroundSearchBarCard');
        const searchInput = document.getElementById('playgroundSearchInput');
        // ... (other playground element getters, ensure all IDs are prefixed) ...
        const modelSelectorBtn = document.getElementById('playgroundModelSelectorBtn');
        const currentModelNameSpan = document.getElementById('playgroundCurrentModelName');
        const modelDropdownList = document.getElementById('playgroundModelDropdownList');
        const modelSearchInput = document.getElementById('playgroundModelSearchInput');
        const modelListUl = modelDropdownList ? modelDropdownList.querySelector('ul') : null;
        const dynamicContentArea = document.getElementById('playgroundDynamicContentArea');


        let isGuidedModeActive = false; // Playground specific state
        let favoritePrompts = []; // Playground specific state or shared
        let blockSuggestionsOnNextFocus = false; // Playground specific state

        const modelsData = [ /* Playground modelsData */
            { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
        ];

        function pg_debounce(func, delay) { /* Debounce implementation */ let t; return (...a) => {clearTimeout(t); t=setTimeout(()=>func.apply(this,a),delay)} }
        function pg_autoResizeTextarea(textarea) { if(!textarea) return; textarea.style.height='auto'; textarea.style.height=textarea.scrollHeight+'px'; textarea.style.overflowY='hidden'; }
        
        // ... (other pg_ helper functions from previous response, ensure they use playground scoped elements)
        function pg_updateDynamicContentAreaVisibility() {
            if (!dynamicContentArea) return;
            const simpleSuggestionsContainer = document.getElementById('playgroundSimplePromptSuggestionsContainer');
            const enhancedPromptSuggestionsContainer = document.getElementById('playgroundEnhancedPromptSuggestionsContainer');
            const favoritePromptsSection = document.getElementById('playgroundFavoritePromptsSection');
            const analyzingIndicator = document.getElementById('playgroundAnalyzingIndicator');

            if (!simpleSuggestionsContainer || !enhancedPromptSuggestionsContainer || !favoritePromptsSection || !analyzingIndicator) return;

            const isSimpleVisible = !simpleSuggestionsContainer.classList.contains('hidden') && simpleSuggestionsContainer.innerHTML.trim() !== '';
            const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedPromptSuggestionsContainer.innerHTML.trim() !== '';
            const favoritePromptsList = document.getElementById('playgroundFavoritePromptsList');
            const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && (favoritePromptsList && favoritePromptsList.innerHTML.trim() !== '');
            const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden');
            dynamicContentArea.classList.toggle('hidden', !(isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible));
        }

        function pg_hideAllDynamicContentExcept(exceptContainer = null) {
            const simpleSuggestionsContainer = document.getElementById('playgroundSimplePromptSuggestionsContainer');
            const enhancedPromptSuggestionsContainer = document.getElementById('playgroundEnhancedPromptSuggestionsContainer');
            const favoritePromptsSection = document.getElementById('playgroundFavoritePromptsSection');
            const analyzingIndicator = document.getElementById('playgroundAnalyzingIndicator');
            [simpleSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => {
                if (container && container !== exceptContainer) {
                    container.classList.add('hidden');
                    if (container !== analyzingIndicator && container.innerHTML !== undefined) container.innerHTML = '';
                }
            });
        }
        function pg_setDefaultModel() { /* Sets default model for playground */ if(!currentModelNameSpan || !modelsData[0]) return; currentModelNameSpan.textContent = modelsData[0].shortName; currentModelNameSpan.dataset.selectedModelId = modelsData[0].id; }
        function pg_populateModelDropdown() { /* Populates playground model dropdown */ 
            if(!modelListUl || !modelSearchInput || !currentModelNameSpan) return;
            modelListUl.innerHTML = '';
            const searchTerm = modelSearchInput.value.toLowerCase();
            modelsData.filter(m => m.displayName.toLowerCase().includes(searchTerm)).forEach(model => {
                const li = document.createElement('li'); /* ... create li ... */
                li.addEventListener('click', (e) => { /* ... handle model selection ... */ });
                modelListUl.appendChild(li);
            });
        }


        if (modelSelectorBtn) { modelSelectorBtn.addEventListener('click', (e) => { /* Playground model selector logic */ }); }
        if (modelSearchInput) { modelSearchInput.addEventListener('input', pg_populateModelDropdown); /* ... */ }
        
        function pg_renderFavoritePrompts() { /* Renders favorites in playground dynamic area */ }
        const pg_handleSimpleInput = pg_debounce(function() { /* Handles simple suggestions in playground */ }, 300);

        if (searchInput) {
            searchInput.addEventListener('input', () => { pg_autoResizeTextarea(searchInput); pg_handleSimpleInput(); });
            searchInput.addEventListener('focus', () => { /* Playground input focus logic */ });
            searchInput.addEventListener('blur', () => { /* Playground input blur logic */ });
        }
        
        // ... (other event listeners for copy, send, guided create, mic - specific to playground IDs)
        const savePromptBtnPlayground = document.getElementById('playgroundSavePromptBtn');
        if(savePromptBtnPlayground) { savePromptBtnPlayground.addEventListener('click', () => { /* Playground save logic */ }); }

        pg_autoResizeTextarea(searchInput);
        pg_setDefaultModel();
        if (searchInput && searchInput.value === '') pg_renderFavoritePrompts();
    } 
</script>

</body>
</html>