<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Área - Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            /* TYPOGRAPHY */
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;

            /* NEW COLOR PALETTE - GLASSMORPHISM */
            --color-vibrant-magenta: #C645F9;
            --color-indigo-blue: #5E6CE7;
            --color-pure-white: #FFFFFF;
            --color-deep-purple-dark: #0D0425;

            /* Glassmorphism Background */
            --color-bg: linear-gradient(135deg, rgba(179, 30, 243, 0.1) 0%, rgba(22, 41, 207, 0.1) 25%, rgba(255, 255, 255, 0.05) 50%, rgba(196, 69, 249, 0.08) 75%, rgba(0, 0, 0, 0.1) 100%);
            
            /* Glass surfaces */
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-weak: rgba(255, 255, 255, 0.05);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px rgba(196, 69, 249, 0.1);
            --glass-shadow-strong: 0 12px 40px rgba(94, 108, 231, 0.15);
            
            --color-sidebar-bg: rgba(255, 255, 255, 0.1);
            --color-card-bg: rgba(255, 255, 255, 0.1);
            --color-text: #0D0425;
            --color-text-muted: rgba(13, 4, 37, 0.7);
            --color-cta: #5E6CE7;
            --color-cta-text: #FFFFFF;
            --color-shadow-outset-primary: rgba(255, 255, 255, 0.3);
            --color-shadow-outset-secondary: rgba(255, 255, 255, 0.2);
            --color-accent-start: #C645F9;
            --color-accent-middle: #5E6CE7;
            --color-accent-end: #C645F9;
            --color-icon-gray: rgba(13, 4, 37, 0.6);
            --color-hover-bg: rgba(255, 255, 255, 0.15);
            --color-border-light: rgba(255, 255, 255, 0.2);
            --border-radius-card: 20px;
            --border-radius-pill: 9999px;
            --border-radius-element: 12px;
            --color-star-active: #FFD700;
            --sidebar-width: 260px;
            --header-height: 70px;
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background: var(--color-bg);
            background-attachment: fixed;
            color: var(--color-text);
            display: flex;
            min-height: 100vh;
            overflow-x: hidden;
            backdrop-filter: blur(20px);
        }

        /* Floating background elements for glassmorphism effect */
        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(254, 253, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(230, 233, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 60% 20%, rgba(13, 4, 37, 0.2) 0%, transparent 50%);
            z-index: -1;
        }


        /* --- SIDEBAR --- */
        .sidebar {
            width: var(--sidebar-width);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            padding: 25px 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            z-index: 1000;
            box-shadow: var(--glass-shadow);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-vibrant-magenta), var(--color-indigo-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            padding: 0 25px;
            margin-bottom: 30px;
            text-align: center;
        }

        .sidebar-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav .nav-item {
            margin-bottom: 5px;
        }

        .sidebar-nav .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 25px;
            color: var(--color-text-muted);
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            transition: all 0.3s ease;
            border-left: 4px solid transparent;
            backdrop-filter: blur(10px);
        }

        .sidebar-nav .nav-link i {
            margin-right: 15px;
            width: 20px;
            text-align: center;
            font-size: 1.1em;
        }

        .sidebar-nav .nav-link:hover {
            background: var(--color-hover-bg);
            backdrop-filter: blur(15px);
            color: var(--color-cta);
            transform: translateX(5px);
        }

        .sidebar-nav .nav-link.active {
            color: var(--color-cta);
            background: var(--glass-bg-strong);
            backdrop-filter: blur(25px);
            border-left-color: var(--color-cta);
            font-weight: 600;
            box-shadow: inset 0 0 20px rgba(94, 108, 231, 0.1);
        }
        .sidebar-nav .nav-link.active i {
            color: var(--color-cta);
        }

        .sidebar-user-profile {
            margin-top: auto;
            padding: 20px 25px;
            border-top: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            gap: 12px;
            background: var(--glass-bg-weak);
            backdrop-filter: blur(15px);
        }
        .sidebar-user-profile img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--glass-border);
            box-shadow: 0 4px 15px rgba(196, 69, 249, 0.2);
        }
        .sidebar-user-profile .user-info p { margin: 0; }
        .sidebar-user-profile .user-info .user-name { font-weight: 600; font-size: 0.9rem; }
        .sidebar-user-profile .user-info .user-email { font-size: 0.75rem; color: var(--color-text-muted); }

        /* --- MAIN CONTENT WRAPPER --- */
        .main-content-wrapper {
            margin-left: var(--sidebar-width);
            width: calc(100% - var(--sidebar-width));
            display: flex;
            flex-direction: column;
        }

        /* --- APP HEADER --- */
        .app-header {
            height: var(--header-height);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            position: sticky;
            top: 0;
            z-index: 900;
            box-shadow: var(--glass-shadow);
        }
        
        .header-title {
            font-family: var(--font-header);
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--color-text);
        }

        .header-actions { display: flex; align-items: center; gap: 20px; }
        
        .btn-new-prompt {
            padding: 12px 24px;
            border-radius: var(--border-radius-pill);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex;
            align-items: center;
            gap: 8px;
            background: linear-gradient(135deg, var(--color-accent-start), var(--color-accent-middle));
            color: var(--color-cta-text);
            border: 1px solid var(--glass-border);
            backdrop-filter: blur(20px);
            box-shadow: 0 8px 25px rgba(196, 69, 249, 0.3);
        }
        .btn-new-prompt:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 15px 35px rgba(196, 69, 249, 0.4);
            background: linear-gradient(135deg, var(--color-accent-start), var(--color-accent-end));
        }
        .btn-new-prompt i { font-size: 1.1em; }

        .user-menu { position: relative; }
        .user-menu-btn {
            background: var(--glass-bg-weak);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            cursor: pointer;
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 12px;
            border-radius: var(--border-radius-element);
            transition: all 0.3s ease;
        }
        .user-menu-btn:hover {
            background: var(--color-hover-bg);
            backdrop-filter: blur(20px);
            transform: translateY(-1px);
        }
        .user-menu-btn img {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--glass-border);
        }
        .user-menu-btn .user-name-header { font-weight: 500; font-size:0.9rem; }
        .user-menu-btn .fa-chevron-down { color: var(--color-text-muted); font-size: 0.8em; }
        
        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: var(--glass-bg-strong);
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: var(--glass-shadow-strong);
            width: 200px;
            padding: 8px 0;
            z-index: 1010;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all 0.3s ease;
        }
        .user-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        .user-dropdown a {
            display: block;
            padding: 12px 15px;
            color: var(--color-text);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all 0.2s ease;
        }
        .user-dropdown a:hover {
            background: var(--color-hover-bg);
            backdrop-filter: blur(20px);
            transform: translateX(5px);
        }
        .user-dropdown a i { margin-right: 10px; color: var(--color-text-muted); }
        .user-dropdown .divider { height: 1px; background: var(--glass-border); margin: 8px 0; }

        /* --- CONTENT AREA --- */
        .content-area {
            flex-grow: 1;
            padding: 30px;
            overflow-y: auto;
        }

        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }

        .dashboard-card, .prompt-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            transition: all 0.3s ease;
        }
        .dashboard-card:hover, .prompt-card:hover {
            transform: translateY(-5px);
            box-shadow: var(--glass-shadow-strong);
            background: var(--glass-bg-strong);
            backdrop-filter: blur(25px);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-header h3 {
            margin: 0;
            font-size: 1.2rem;
            font-weight: 600;
        }
        .card-header .icon {
            font-size: 1.5rem;
            color: var(--color-cta);
            opacity: 0.8;
        }
        
        .enhanced-prompt-item {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 20px;
            margin-bottom: 20px;
            box-shadow: var(--glass-shadow);
            transition: all 0.3s ease;
        }
        .enhanced-prompt-item:hover {
            transform: translateY(-3px);
            box-shadow: var(--glass-shadow-strong);
            background: var(--glass-bg-strong);
            backdrop-filter: blur(25px);
        }
        .enhanced-prompt-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .enhanced-prompt-item-header h5 { margin: 0; font-size: 1.05rem; font-weight: 600; color: var(--color-text); }
        .enhanced-prompt-item-header .star-icon { font-size: 1rem; color: var(--color-icon-gray); cursor:pointer; transition: all 0.3s ease; }
        .enhanced-prompt-item-header .star-icon:hover { transform: scale(1.2); }
        .enhanced-prompt-item-header .star-icon.favorited { color: var(--color-star-active); }
        
        .enhanced-prompt-item-meta-minimal {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
            font-size: 0.75rem;
            color: var(--color-text-muted);
            margin-bottom: 12px;
            padding: 8px 12px;
            background: var(--glass-bg-weak);
            backdrop-filter: blur(10px);
            border-radius: 8px;
            border: 1px solid var(--glass-border);
        }
        .meta-item { display: flex; align-items: center; gap: 5px; }
        .meta-item i { color: var(--color-cta); font-size: 0.9em; }
        
        .full-prompt-display-summary {
            font-size: 0.85rem;
            line-height: 1.6;
            color: var(--color-text-muted);
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            margin-bottom: 15px;
        }
        
        .prompt-actions { display: flex; gap: 10px; margin-top: 15px; }
        .prompt-action-btn {
            background: var(--glass-bg-weak);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            color: var(--color-text-muted);
            padding: 8px 16px;
            border-radius: var(--border-radius-element);
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }
        .prompt-action-btn:hover {
            background: var(--color-cta);
            color: var(--color-cta-text);
            border-color: var(--color-cta);
            transform: translateY(-2px);
            backdrop-filter: blur(20px);
        }
        .prompt-action-btn.primary {
            background: linear-gradient(135deg, var(--color-cta), var(--color-vibrant-magenta));
            color: var(--color-cta-text);
            border-color: var(--color-cta);
        }
        .prompt-action-btn.primary:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 20px rgba(94, 108, 231, 0.3);
        }

        /* Welcome section with glassmorphism */
        .welcome-section {
            background: linear-gradient(135deg, 
                rgba(196, 69, 249, 0.15), 
                rgba(94, 108, 231, 0.15), 
                rgba(13, 4, 37, 0.1)
            );
            backdrop-filter: blur(25px);
            -webkit-backdrop-filter: blur(25px);
            border: 1px solid var(--glass-border);
            padding: 30px 40px;
            border-radius: var(--border-radius-card);
            margin-bottom: 30px;
            color: var(--color-text);
            box-shadow: var(--glass-shadow-strong);
        }
        .welcome-section h2 {
            font-family: var(--font-header);
            font-size: 2rem;
            margin-top: 0;
            margin-bottom: 5px;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-vibrant-magenta), var(--color-indigo-blue));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }
        .welcome-section p {
            font-size: 1rem;
            opacity: 0.8;
            margin-bottom: 0;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--color-text);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--glass-border);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section-title i { color: var(--color-cta); }

        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content-wrapper {
                margin-left: 0;
                width: 100%;
            }
            .app-header { padding: 0 15px; }
            .menu-toggle-btn { display: block; font-size:1.5rem; cursor:pointer; color: var(--color-text); margin-right:15px; }
            .sidebar-logo { font-size: 1.5rem;}
            .header-title { font-size: 1.3rem;}
        }
        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: 1fr;
            }
            .welcome-section h2 { font-size: 1.6rem; }
            .welcome-section p { font-size: 0.9rem; }
            .btn-new-prompt { padding: 8px 18px; font-size: 0.85rem; }
            .content-area { padding: 20px; }
        }

        .menu-toggle-btn { display: none; }
        .hidden { display: none !important; }
    </style>
</head>
<body>

    <aside class="sidebar" id="sidebar">
        <div class="sidebar-logo">allhub</div>
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-view="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="my-prompts">
                        <i class="fas fa-lightbulb"></i> Mis Prompts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="history">
                        <i class="fas fa-history"></i> Historial
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="playground">
                        <i class="fas fa-rocket"></i> Playground
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="settings">
                        <i class="fas fa-cog"></i> Configuración
                    </a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-user-profile">
            <img src="https://i.pravatar.cc/80?u=UserExample" alt="User Avatar">
            <div class="user-info">
                <p class="user-name">Elena V.</p>
                <p class="user-email"><EMAIL></p>
            </div>
        </div>
    </aside>

    <div class="main-content-wrapper">
        <header class="app-header">
            <div style="display: flex; align-items: center;">
                <button class="menu-toggle-btn" id="menuToggleBtn"><i class="fas fa-bars"></i></button>
                <h2 class="header-title" id="headerTitle">Dashboard</h2>
            </div>
            <div class="header-actions">
                <a href="#" class="btn-new-prompt"><i class="fas fa-plus-circle"></i> Nuevo Prompt</a>
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <img src="https://i.pravatar.cc/80?u=UserExample" alt="User Avatar">
                        <span class="user-name-header">Elena V.</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user-circle"></i> Mi Perfil</a>
                        <a href="#"><i class="fas fa-credit-card"></i> Suscripción</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a>
                    </div>
                </div>
            </div>
        </header>

        <main class="content-area">
            <!-- VISTA DASHBOARD (Visible por defecto) -->
            <div id="dashboardView">
                <div class="welcome-section">
                    <h2>¡Bienvenida de nuevo, Elena!</h2>
                    <p>Lista para crear prompts increíbles hoy?</p>
                </div>

                <div class="section-title"><i class="fas fa-chart-line"></i> Tu Actividad Reciente</div>
                <div class="grid-container" style="margin-bottom: 30px;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Prompts Guardados</h3>
                            <i class="fas fa-save icon"></i>
                        </div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">27</p>
                        <p class="text-muted" style="font-size:0.9rem;">Organizados y listos para usar.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Prompts Usados (Mes)</h3>
                            <i class="fas fa-cogs icon"></i>
                        </div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">152</p>
                        <p class="text-muted" style="font-size:0.9rem;">Generando ideas sin parar.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Favoritos Activos</h3>
                            <i class="fas fa-star icon"></i>
                        </div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-star-active);">8</p>
                        <p class="text-muted" style="font-size:0.9rem;">Tus prompts estrella.</p>
                    </div>
                </div>

                <div class="section-title"><i class="fas fa-bolt"></i> Acceso Rápido: Favoritos</div>
                <div id="favoritePromptsDashboard">
                    <div class="enhanced-prompt-item">
                        <div class="enhanced-prompt-item-header">
                            <h5>Mejorar Artículo con CoT</h5>
                            <i class="fas fa-star star-icon favorited" title="Quitar de favoritos"></i>
                        </div>
                        <div class="enhanced-prompt-item-meta-minimal">
                            <span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                            <span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                            <span class="meta-item"><i class="far fa-clock"></i> Time: ~2s</span>
                        </div>
                        <p class="full-prompt-display-summary">Escribe un artículo de blog de 400 palabras sobre cómo resumir un PDF. Antes de escribir, desglosa el proceso...</p>
                        <div class="prompt-actions">
                             <a href="#" class="prompt-action-btn primary"><i class="fas fa-play-circle"></i> Usar en Playground</a>
                             <a href="#" class="prompt-action-btn"><i class="fas fa-edit"></i> Editar</a>
                        </div>
                    </div>
                     <div class="enhanced-prompt-item">
                        <div class="enhanced-prompt-item-header">
                            <h5>Resumen PDF con Few-Shot</h5>
                             <i class="fas fa-star star-icon favorited" title="Quitar de favoritos"></i>
                        </div>
                        <div class="enhanced-prompt-item-meta-minimal">
                            <span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input: 170</span>
                            <span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output: ~500</span>
                            <span class="meta-item"><i class="far fa-clock"></i> Time: ~1.5s</span>
                        </div>
                        <p class="full-prompt-display-summary">Escribe un artículo de blog sobre cómo resumir un PDF. Sigue el estilo y estructura de estos ejemplos: Ejemplo 1...</p>
                        <div class="prompt