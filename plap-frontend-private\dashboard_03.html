<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>allhub - Private Area</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            --color-primary: #C645F9;
            --color-secondary: #000d83;
            --color-dark: #0D0425;
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-hover: rgba(255, 255, 255, 0.18);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.06);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-border-highlight: rgba(255, 255, 255, 0.35);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.12);
            --glass-hover-shadow: 0 12px 40px rgba(13, 4, 37, 0.18);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 70%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 50%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.2);
            --color-active-accent: rgba(94, 108, 231, 0.3);
            --border-radius-card: 24px;
            --border-radius-pill: 50px;
            --border-radius-element: 16px;
            --transition-speed: 0.3s;
            --transition-ease: ease-in-out;
            --color-star-active: #FFD700;
            --color-placeholder: var(--color-text-muted);
            --color-icon-gray: var(--color-text-secondary);
            --sidebar-width: 280px;
            --header-height: 80px;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            color: var(--color-text-primary);
            background: linear-gradient(135deg, rgba(179, 30, 243, 0.1) 0%, rgba(22, 41, 207, 0.1) 25%, rgba(255, 255, 255, 0.05) 50%, rgba(196, 69, 249, 0.08) 75%, rgba(0, 0, 0, 0.1) 100%);
            background-attachment: fixed;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        body::before {
            content: '';
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background: 
                radial-gradient(circle at 20% 30%, rgba(254, 253, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 80% 70%, rgba(230, 233, 255, 0.3) 0%, transparent 50%),
                radial-gradient(circle at 60% 20%, rgba(13, 4, 37, 0.2) 0%, transparent 50%);
            z-index: -1;
            animation: float 20s infinite ease-in-out;
        }

        @keyframes float {
            0%, 100% { transform: translate(0, 0); }
            50% { transform: translate(10%, 10%); }
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            padding: 30px 20px;
            position: fixed;
            height: 100%;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            box-shadow: var(--glass-shadow);
            transition: transform var(--transition-speed) var(--transition-ease);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 30px;
        }

        .sidebar-nav {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 14px 20px;
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--color-text-primary);
            text-decoration: none;
            border-radius: var(--border-radius-element);
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
            overflow: hidden;
        }

        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .sidebar-nav a:hover::before { left: 100%; }

        .sidebar-nav a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(8px);
            box-shadow: var(--glass-hover-shadow);
        }

        .sidebar-nav a.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            font-weight: 600;
            box-shadow: inset 0 0 20px rgba(198, 69, 249, 0.15);
        }

        .sidebar-nav a i {
            font-size: 1.1rem;
            color: var(--color-icon-gray);
        }

        .sidebar-nav a.active i {
            color: var(--color-primary);
        }

        .main-content {
            margin-left: var(--sidebar-width);
            flex-grow: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--glass-shadow);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            color: var(--color-text-primary);
            cursor: pointer;
            background: none;
            border: none;
        }

        .header-title {
            font-family: var(--font-header);
            font-size: 1.6rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .new-prompt-btn {
            padding: 12px 24px;
            border-radius: var(--border-radius-pill);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-prompt-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px color-mix(in srgb, var(--color-primary) 30%, transparent);
        }

        .user-menu {
            position: relative;
        }

        .user-menu-btn {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 8px 12px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .user-menu-btn:hover {
            background: var(--glass-bg-hover);
            transform: translateY(-2px);
            box-shadow: var(--glass-hover-shadow);
        }

        .user-avatar {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 1.1rem;
        }

        .user-menu-btn span {
            font-size: 0.9rem;
            font-weight: 500;
        }

        .user-menu-btn i {
            font-size: 0.8rem;
            color: var(--color-text-muted);
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            right: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: var(--glass-hover-shadow);
            width: 200px;
            padding: 10px 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1000;
        }

        .user-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            color: var(--color-text-primary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .user-dropdown a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(5px);
        }

        .user-dropdown a i {
            margin-right: 10px;
            color: var(--color-text-muted);
        }

        .user-dropdown .divider {
            height: 1px;
            background: var(--glass-border);
            margin: 8px 0;
        }

        .content-view {
            display: none;
            animation: fadeIn 0.5s var(--transition-ease);
        }

        .content-view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .welcome-card, .activity-card, .prompt-card, .history-card, .playground-card, .settings-card, .favorites-card, .knowledge-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .welcome-card:hover, .activity-card:hover, .prompt-card:hover, .history-card:hover, .playground-card:hover, .settings-card:hover, .favorites-card:hover, .knowledge-card:hover {
            transform: translateY(-5px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .section-title {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-title i {
            color: var(--color-primary);
        }

        /* Dashboard Styles */
        .dashboard-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 30px;
            align-items: start;
        }

        .welcome-card h1 {
            font-family: var(--font-header);
            font-size: 2rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 10px;
        }

        .welcome-card p {
            font-size: 1.1rem;
            color: var(--color-text-secondary);
            line-height: 1.6;
        }

        .activity-card {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .activity-chart {
            display: grid;
            grid-template-columns: 1fr;
            gap: 20px;
        }

        .chart-item {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 20px;
            text-align: center;
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
            overflow: hidden;
        }

        .chart-item:hover {
            transform: translateY(-3px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .chart-item canvas {
            width: 100%;
            height: 100px;
        }

        .knowledge-card {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .knowledge-card p {
            font-size: 0.95rem;
            color: var(--color-text-secondary);
            line-height: 1.6;
        }

        /* My Prompts Styles */
        .prompts-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .prompts-toolbar {
            display: flex;
            justify-content: space-between;
            align-items: center;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 15px 20px;
            box-shadow: var(--glass-shadow);
        }

        .prompts-search {
            display: flex;
            align-items: center;
            gap: 10px;
            background: var(--glass-bg-toolbar);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-pill);
            padding: 8px 15px;
            flex-grow: 1;
            max-width: 400px;
        }

        .prompts-search input {
            background: transparent;
            border: none;
            color: var(--color-text-primary);
            font-size: 0.9rem;
            width: 100%;
            outline: none;
        }

        .prompts-search input::placeholder {
            color: var(--color-placeholder);
        }

        .prompts-search i {
            color: var(--color-icon-gray);
        }

        .prompts-filter {
            display: flex;
            gap: 10px;
        }

        .filter-btn {
            padding: 8px 16px;
            border-radius: var(--border-radius-pill);
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: var(--color-text-primary);
            font-size: 0.85rem;
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .filter-btn.active, .filter-btn:hover {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            border-color: var(--color-primary);
        }

        .prompts-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .prompt-card {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .prompt-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .prompt-card h3 {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .prompt-card-meta {
            display: flex;
            gap: 12px;
            font-size: 0.8rem;
            color: var(--color-text-secondary);
        }

        .prompt-card-meta span i {
            margin-right: 5px;
            color: var(--color-primary);
        }

        .prompt-card p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            line-height: 1.5;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .prompt-card-actions {
            display: flex;
            gap: 10px;
            margin-top: auto;
        }

        .favorites-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .favorite-item {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 20px;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .favorite-item:hover {
            background: var(--glass-bg-hover);
            transform: translateY(-3px);
            box-shadow: var(--glass-hover-shadow);
        }

        .favorite-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .favorite-item h3 {
            font-size: 1.1rem;
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .favorite-item-meta {
            display: flex;
            gap: 12px;
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            margin-bottom: 12px;
        }

        .favorite-item-meta span i {
            margin-right: 5px;
            color: var(--color-primary);
        }

        .favorite-item p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            line-height: 1.5;
            margin-bottom: 15px;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .favorite-item-actions {
            display: flex;
            gap: 10px;
        }

        .favorite-action-btn {
            padding: 10px 20px;
            border-radius: var(--border-radius-pill);
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .favorite-action-btn.playground {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: none;
        }

        .favorite-action-btn.edit, .favorite-action-btn.delete {
            background: var(--glass-bg);
            color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }

        .favorite-action-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .favorite-action-btn.edit:hover, .favorite-action-btn.delete:hover {
            background: var(--glass-bg-hover);
        }

        /* History Styles */
        .history-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .history-list {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .history-card {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .history-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-card h3 {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .history-card-meta {
            display: flex;
            gap: 12px;
            font-size: 0.8rem;
            color: var(--color-text-secondary);
        }

        .history-card-meta span i {
            margin-right: 5px;
            color: var(--color-primary);
        }

        .history-card p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            line-height: 1.5;
        }

        .history-card-actions {
            display: flex;
            gap: 10px;
        }

        /* Playground Styles */
        .playground-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .playground-editor {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .editor-header h3 {
            font-family: var(--font-header);
            font-size: 1.3rem;
            font-weight: 600;
        }

        .editor-textarea {
            width: 100%;
            min-height: 200px;
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 15px;
            color: var(--color-text-primary);
            font-size: 0.9rem;
            resize: vertical;
            outline: none;
        }

        .editor-textarea::placeholder {
            color: var(--color-placeholder);
        }

        .editor-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .editor-btn {
            padding: 10px 20px;
            border-radius: var(--border-radius-pill);
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .editor-btn.run {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: none;
        }

        .editor-btn.clear {
            background: var(--glass-bg);
            color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }

        .editor-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .editor-btn.clear:hover {
            background: var(--glass-bg-hover);
        }

        .playground-output {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            min-height: 150px;
        }

        .playground-output h3 {
            font-family: var(--font-header);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .playground-output p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            line-height: 1.5;
        }

        /* Settings Styles */
        .settings-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .settings-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
        }

        .settings-section h3 {
            font-family: var(--font-header);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .settings-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .form-group label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--color-text-primary);
        }

        .form-group input, .form-group select {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 12px;
            color: var(--color-text-primary);
            font-size: 0.9rem;
            outline: none;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .form-group input:focus, .form-group select:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 10px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .form-group input::placeholder {
            color: var(--color-placeholder);
        }

        .settings-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .settings-btn {
            padding: 10px 20px;
            border-radius: var(--border-radius-pill);
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .settings-btn.save {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: none;
        }

        .settings-btn.cancel {
            background: var(--glass-bg);
            color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }

        .settings-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .settings-btn.cancel:hover {
            background: var(--glass-bg-hover);
        }

        /* Responsive Design */
        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }

            .main-content {
                margin-left: 240px;
            }

            .dashboard-content {
                grid-template-columns: 1fr;
            }

            .activity-chart {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 220px;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .menu-toggle {
                display: block;
            }

            .header {
                padding: 15px 20px;
            }

            .header-title {
                font-size: 1.4rem;
            }

            .welcome-card h1 {
                font-size: 1.6rem;
            }

            .dashboard-content {
                grid-template-columns: 1fr;
            }

            .header-actions {
                flex-direction: column;
                align-items: flex-end;
                gap: 10px;
            }

            .prompts-toolbar {
                flex-direction: column;
                align-items: flex-start;
                gap: 15px;
            }

            .prompts-search {
                max-width: 100%;
            }

            .prompts-filter {
                flex-wrap: wrap;
            }
        }

        @media (max-width: 480px) {
            .new-prompt-btn, .favorite-action-btn, .editor-btn, .settings-btn {
                padding: 8px 16px;
                font-size: 0.8rem;
            }

            .favorite-item h3, .prompt-card h3, .history-card h3 {
                font-size: 1rem;
            }

            .favorite-item-meta, .prompt-card-meta, .history-card-meta {
                flex-direction: column;
                gap: 8px;
            }

            .prompts-list {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-logo">allhub</div>
            <div class="sidebar-nav">
                <a href="#" class="active" data-view="dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="#" data-view="my-prompts"><i class="fas fa-lightbulb"></i> My Prompts</a>
                <a href="#" data-view="history"><i class="fas fa-history"></i> History</a>
                <a href="#" data-view="playground"><i class="fas fa-rocket"></i> Playground</a>
                <a href="#" data-view="settings"><i class="fas fa-cog"></i> Settings</a>
            </div>
        </nav>
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle"><i class="fas fa-bars"></i></button>
                    <h2 class="header-title" id="headerTitle">Dashboard</h2>
                </div>
                <div class="header-actions">
                    <button class="new-prompt-btn"><i class="fas fa-plus-circle"></i> New Prompt</button>
                    <div class="user-menu">
                        <button class="user-menu-btn" id="userMenuBtn">
                            <div class="user-avatar">EV</div>
                            <span>Elena V.</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        <div class="user-dropdown" id="userDropdown">
                            <a href="#"><i class="fas fa-user-circle"></i> Profile</a>
                            <a href="#"><i class="fas fa-credit-card"></i> Subscription</a>
                            <div class="divider"></div>
                            <a href="#"><i class="fas fa-sign-out-alt"></i> Log Out</a>
                        </div>
                    </div>
                </div>
            </header>
            <section id="dashboardView" class="content-view active">
                <div class="dashboard-content">
                    <div class="welcome-card">
                        <h1>Welcome back, Elena!</h1>
                        <p>Ready to create amazing prompts today?</p>
                    </div>
                    <div class="activity-card">
                        <h2 class="section-title"><i class="fas fa-chart-line"></i> Your Recent Activity</h2>
                        <div class="activity-chart">
                            <div class="chart-item">
                                <canvas id="savedPromptsChart"></canvas>
                                <p>Saved Prompts: 27</p>
                            </div>
                            <div class="chart-item">
                                <canvas id="promptsUsedChart"></canvas>
                                <p>Prompts Used (Month): 152</p>
                            </div>
                            <div class="chart-item">
                                <canvas id="activeFavoritesChart"></canvas>
                                <p>Active Favorites: 8</p>
                            </div>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h2 class="section-title"><i class="fas fa-sitemap"></i> Knowledge Graph</h2>
                        <p>helps you create and query Knowledge Graphs that evolve over time. A knowledge graph is a network of interconnected facts, such as “Kendra loves Adidas shoes.” Each fact is a “triplet” represented by two entities, or nodes (”Kendra”, “Adidas shoes”), and their relationship, or edge (”loves”).</p>
                        <p>Knowledge Graphs have been explored extensively for information retrieval. What makes Graphiti unique is its ability to autonomously build a knowledge graph while handling changing relationships and maintaining historical context.</p>
                    </div>
                </div>
            </section>
            <section id="myPromptsView" class="content-view">
                <div class="prompts-content">
                    <h2 class="section-title"><i class="fas fa-lightbulb"></i> My Prompts</h2>
                    <div class="prompts-toolbar">
                        <div class="prompts-search">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="Search prompts...">
                        </div>
                        <div class="prompts-filter">
                            <button class="filter-btn active">All</button>
                            <button class="filter-btn">Favorites</button>
                            <button class="filter-btn">Recent</button>
                        </div>
                    </div>
                    <div class="prompts-list">
                        <div class="prompt-card">
                            <div class="prompt-card-header">
                                <h3>Generate Marketing Email</h3>
                            </div>
                            <div class="prompt-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 150</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~400</span>
                                <span><i class="far fa-clock"></i> Time: ~1.8s</span>
                            </div>
                            <p>Create a professional marketing email for a new product launch...</p>
                            <div class="prompt-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                <button class="favorite-action-btn delete"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                        <div class="prompt-card">
                            <div class="prompt-card-header">
                                <h3>Social Media Post Ideas</h3>
                            </div>
                            <div class="prompt-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 120</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~300</span>
                                <span><i class="far fa-clock"></i> Time: ~1.2s</span>
                            </div>
                            <p>Generate 5 engaging social media post ideas for a tech startup...</p>
                            <div class="prompt-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                <button class="favorite-action-btn delete"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                    </div>
                    <div class="favorites-card">
                        <h2 class="section-title"><i class="fas fa-bolt"></i> Quick Access: Favorites</h2>
                        <div class="favorites-list">
                            <div class="favorite-item">
                                <div class="favorite-item-header">
                                    <h3>Improve Article with CoT</h3>
                                </div>
                                <div class="favorite-item-meta">
                                    <span><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                                    <span><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                                    <span><i class="far fa-clock"></i> Time: ~2s</span>
                                </div>
                                <p>Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process...</p>
                                <div class="favorite-item-actions">
                                    <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use in Playground</button>
                                    <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                </div>
                            </div>
                            <div class="favorite-item">
                                <div class="favorite-item-header">
                                    <h3>Summary PDF with Few-Shot</h3>
                                </div>
                                <div class="favorite-item-meta">
                                    <span><i class="fas fa-sign-in-alt"></i> Input: 170</span>
                                    <span><i class="fas fa-sign-out-alt"></i> Output: ~500</span>
                                    <span><i class="far fa-clock"></i> Time: ~1.5s</span>
                                </div>
                                <p>Write a blog article about how to summarize a PDF. Follow the style and structure of these examples...</p>
                                <div class="favorite-item-actions">
                                    <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use in Playground</button>
                                    <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="historyView" class="content-view">
                <div class="history-content">
                    <h2 class="section-title"><i class="fas fa-history"></i> History</h2>
                    <div class="history-list">
                        <div class="history-card">
                            <div class="history-card-header">
                                <h3>Improve Article with CoT</h3>
                                <span class="history-date">2025-05-22</span>
                            </div>
                            <div class="history-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                                <span><i class="far fa-clock"></i> Time: ~2s</span>
                            </div>
                            <p>Input: Write a 400-word blog article about how to summarize a PDF...</p>
                            <p>Output: A well-structured 400-word article with clear steps...</p>
                            <div class="history-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Reuse</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-copy"></i> Copy</button>
                            </div>
                        </div>
                        <div class="history-card">
                            <div class="history-card-header">
                                <h3>Summary PDF with Few-Shot</h3>
                                <span class="history-date">2025-05-21</span>
                            </div>
                            <div class="history-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 170</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~500</span>
                                <span><i class="far fa-clock"></i> Time: ~1.5s</span>
                            </div>
                            <p>Input: Write a blog article about how to summarize a PDF...</p>
                            <p>Output: A concise article following provided examples...</p>
                            <div class="history-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Reuse</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-copy"></i> Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="playgroundView" class="content-view">
                <div class="playground-content">
                    <div class="playground-editor">
                        <div class="editor-header">
                            <h3>Prompt Editor</h3>
                        </div>
                        <textarea class="editor-textarea" placeholder="Enter your prompt here..."></textarea>
                        <div class="editor-actions">
                            <button class="editor-btn run"><i class="fas fa-play"></i> Run</button>
                            <button class="editor-btn clear"><i class="fas fa-eraser"></i> Clear</button>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h2 class="section-title"><i class="fas fa-sitemap"></i> Knowledge Graph</h2>
                        <p>Explore relationships in your prompting workflow:</p>
                        <div id="knowledgeGraph" class="knowledge-graph"></div>
                    </div>
                </div>
                <div class="playground-output">
                    <h3>Output</h3>
                    <p>Results will appear here after running your prompt...</p>
                </div>
            </section>
            <section id="settingsView" class="content-view">
                <div class="settings-content">
                    <h2 class="section-title"><i class="fas fa-cog"></i> Settings</h2>
                    <div class="settings-section">
                        <h3>Profile Settings</h3>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" value="Elena V." placeholder="Enter username">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" value="<EMAIL>" placeholder="Enter email">
                            </div>
                            <div class="form-group">
                                <label for="language">Language</label>
                                <select id="language">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                </select>
                            </div>
                            <div class="settings-actions">
                                <button class="settings-btn save"><i class="fas fa-save"></i> Save</button>
                                <button class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                            </div>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h3>Notification Preferences</h3>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="email-notifications">
                                    <input type="checkbox" id="email-notifications" checked> Email Notifications
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="push-notifications">
                                    <input type="checkbox" id="push-notifications"> Push Notifications
                                </label>
                            </div>
                            <div class="settings-actions">
                                <button class="settings-btn save"><i class="fas fa-save"></i> Save</button>
                                <button class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
    <script>
        const sidebar = document.getElementById('sidebar');
        const menuToggle = document.getElementById('menuToggle');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const headerTitle = document.getElementById('headerTitle');
        const navLinks = document.querySelectorAll('.sidebar-nav a');
        const contentViews = document.querySelectorAll('.content-view');

        // Sidebar toggle for mobile
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // User dropdown toggle
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('visible');
        });

        // Close dropdowns when clicking outside
        document.addEventListener('click', (e) => {
            if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('visible');
            }
            if (!sidebar.contains(e.target) && !menuToggle.contains(e.target) && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // View switching
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const viewId = link.dataset.view + 'View';
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
                contentViews.forEach(view => view.classList.remove('active'));
                document.getElementById(viewId).classList.add('active');
                headerTitle.textContent = link.textContent;
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
                console.log(`Switched to ${link.dataset.view}`);
            });
        });

        // Prompt actions
        document.querySelectorAll('.favorite-action-btn, .prompt-card-actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.classList.contains('playground') ? 'Use in Playground' :
                              e.target.classList.contains('edit') ? 'Edit' : 'Delete';
                const promptTitle = e.target.closest('.favorite-item, .prompt-card').querySelector('h3').textContent;
                console.log(`${action} clicked for prompt: ${promptTitle}`);
            });
        });

        // History actions
        document.querySelectorAll('.history-card-actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.classList.contains('playground') ? 'Reuse' : 'Copy';
                const promptTitle = e.target.closest('.history-card').querySelector('h3').textContent;
                console.log(`${action} clicked for history item: ${promptTitle}`);
            });
        });

        // Playground actions
        const editorTextarea = document.querySelector('.editor-textarea');
        document.querySelector('.editor-btn.run').addEventListener('click', () => {
            const prompt = editorTextarea.value;
            console.log(`Running prompt: ${prompt}`);
            document.querySelector('.playground-output p').textContent = `Generated output for: ${prompt || 'No input provided'}`;
        });

        document.querySelector('.editor-btn.clear').addEventListener('click', () => {
            editorTextarea.value = '';
            document.querySelector('.playground-output p').textContent = 'Results will appear here after running your prompt...';
            console.log('Editor cleared');
        });

        // Settings actions
        document.querySelectorAll('.settings-btn.save').forEach(btn => {
            btn.addEventListener('click', () => {
                const form = btn.closest('.settings-form');
                const inputs = form.querySelectorAll('input, select');
                const settings = {};
                inputs.forEach(input => {
                    settings[input.id] = input.type === 'checkbox' ? input.checked : input.value;
                });
                console.log('Saving settings:', settings);
            });
        });

        document.querySelectorAll('.settings-btn.cancel').forEach(btn => {
            btn.addEventListener('click', () => {
                console.log('Settings changes cancelled');
            });
        });

        // New Prompt button
        document.querySelector('.new-prompt-btn').addEventListener('click', () => {
            console.log('New Prompt clicked');
            navLinks.forEach(link => link.classList.remove('active'));
            document.querySelector('[data-view="playground"]').classList.add('active');
            contentViews.forEach(view => view.classList.remove('active'));
            document.getElementById('playgroundView').classList.add('active');
            headerTitle.textContent = 'Playground';
        });

        // Dashboard Charts
        const savedPromptsChart = document.getElementById('savedPromptsChart').getContext('2d');
        const promptsUsedChart = document.getElementById('promptsUsedChart').getContext('2d');
        const activeFavoritesChart = document.getElementById('activeFavoritesChart').getContext('2d');

        new Chart(savedPromptsChart, {
            type: 'bar',
            data: {
                labels: ['Saved Prompts'],
                datasets: [{
                    label: 'Count',
                    data: [27],
                    backgroundColor: 'rgba(198, 69, 249, 0.6)',
                    borderColor: 'rgba(198, 69, 249, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: { y: { beginAtZero: true } },
                plugins: { legend: { display: false } }
            }
        });

        new Chart(promptsUsedChart, {
            type: 'bar',
            data: {
                labels: ['Prompts Used (Month)'],
                datasets: [{
                    label: 'Count',
                    data: [152],
                    backgroundColor: 'rgba(198, 69, 249, 0.6)',
                    borderColor: 'rgba(198, 69, 249, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: { y: { beginAtZero: true } },
                plugins: { legend: { display: false } }
            }
        });

        new Chart(activeFavoritesChart, {
            type: 'bar',
            data: {
                labels: ['Active Favorites'],
                datasets: [{
                    label: 'Count',
                    data: [8],
                    backgroundColor: 'rgba(198, 69, 249, 0.6)',
                    borderColor: 'rgba(198, 69, 249, 1)',
                    borderWidth: 1
                }]
            },
            options: {
                scales: { y: { beginAtZero: true } },
                plugins: { legend: { display: false } }
            }
        });
        
    </script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
</body>
</html>