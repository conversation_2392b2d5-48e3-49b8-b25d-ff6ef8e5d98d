<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>allhub - Private Area</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            --color-primary: #C645F9;
            --color-secondary: #000d83;
            --color-dark: #0D0425;
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-hover: rgba(255, 255, 255, 0.18);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.06);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-border-highlight: rgba(255, 255, 255, 0.35);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.12);
            --glass-hover-shadow: 0 12px 40px rgba(13, 4, 37, 0.18);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 70%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 50%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.2);
            --color-active-accent: rgba(94, 108, 231, 0.3);
            --border-radius-card: 24px;
            --border-radius-pill: 50px;
            --border-radius-element: 16px;
            --transition-speed: 0.3s;
            --transition-ease: ease-in-out;
            --color-star-active: #FFD700;
            --color-placeholder: var(--color-text-muted);
            --color-icon-gray: var(--color-text-secondary);
            --sidebar-width: 280px;
            --header-height: 80px;
            --user-menu-bg: linear-gradient(135deg, #C645F9, #000d83);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            color: var(--color-text-primary);
            background: linear-gradient(135deg, #F5F5F5 0%, #E0E0E0 50%, #D5D5D5 100%);
            background-attachment: fixed;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            padding: 30px 20px;
            position: fixed;
            height: 100%;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            box-shadow: var(--glass-shadow);
            transition: transform var(--transition-speed) var(--transition-ease);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 30px;
        }

        .sidebar-nav {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 14px 20px;
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--color-text-primary);
            text-decoration: none;
            border-radius: var(--border-radius-element);
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
            overflow: hidden;
        }

        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .sidebar-nav a:hover::before { left: 100%; }

        .sidebar-nav a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(8px);
            box-shadow: var(--glass-hover-shadow);
        }

        .sidebar-nav a.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            font-weight: 600;
            box-shadow: inset 0 0 20px rgba(198, 69, 249, 0.15);
        }

        .sidebar-nav a i {
            font-size: 1.1rem;
            color: var(--color-icon-gray);
        }

        .sidebar-nav a.active i {
            color: var(--color-primary);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
        }

        .user-menu {
            position: relative;
            margin-bottom: 20px;
        }

        .user-menu-btn {
            background: var(--user-menu-bg);
            border: none;
            border-radius: var(--border-radius-pill);
            padding: 10px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            width: 100%;
        }

        .user-menu-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-hover-shadow);
        }

        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--user-menu-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 1rem;
        }

        .user-dropdown {
            position: absolute;
            top: calc(100% + 10px);
            left: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: var(--glass-hover-shadow);
            width: 200px;
            padding: 10px 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1000;
        }

        .user-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            color: var(--color-text-primary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .user-dropdown a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(5px);
        }

        .user-dropdown a i {
            margin-right: 10px;
            color: var(--color-text-muted);
        }

        .user-dropdown .divider {
            height: 1px;
            background: var(--glass-border);
            margin: 8px 0;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            flex-grow: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            gap: 50px;
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--glass-shadow);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            color: var(--color-text-primary);
            cursor: pointer;
            background: none;
            border: none;
        }

        .header-title {
            font-family: var(--font-header);
            font-size: 1.6rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .new-prompt-btn {
            padding: 12px 24px;
            border-radius: var(--border-radius-pill);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-prompt-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px color-mix(in srgb, var(--color-primary) 30%, transparent);
        }

        .content-view {
            display: none;
            animation: fadeIn 0.5s var(--transition-ease);
        }

        .content-view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .welcome-card, .activity-card, .prompt-card, .history-card, .playground-card, .settings-card, .favorites-card, .quick-access-card, .recent-prompts-card, .prompting-techniques-card, .llms-used-card, .credits-acquired-card, .credit-status-card, .credit-management-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 30px;
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .welcome-card:hover, .activity-card:hover, .prompt-card:hover, .history-card:hover, .playground-card:hover, .settings-card:hover, .favorites-card:hover, .quick-access-card:hover, .recent-prompts-card:hover, .prompting-techniques-card:hover, .llms-used-card:hover, .credits-acquired-card:hover, .credit-status-card:hover, .credit-management-card:hover {
            transform: translateY(-5px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .section-title {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-title i {
            color: var(--color-primary);
        }

        .dashboard-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
    align-items: start;
}

.activity-card {
    grid-column: 1 / -1; /* Span across all columns */
    width: 100%;
    background: var(--glass-bg);
    backdrop-filter: blur(20px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-card);
    padding: 30px;
    box-shadow: var(--glass-shadow);
    transition: all var(--transition-speed) var(--transition-ease);
}

.activity-card:hover {
    transform: translateY(-5px);
    background: var(--glass-bg-hover);
    box-shadow: var(--glass-hover-shadow);
}

.activity-chart {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr)); /* Increased min width for larger charts */
    gap: 20px; /* Reduced gap for better spacing */
}

.chart-item {
    background: var(--glass-bg-toolbar);
    backdrop-filter: blur(15px);
    border: 1px solid var(--glass-border);
    border-radius: var(--border-radius-element);
    padding: 20px;
    text-align: center;
    transition: all var(--transition-speed) var(--transition-ease);
    position: relative;
    overflow: hidden;
}

.chart-item:hover {
    transform: translateY(-3px);
    background: var(--glass-bg-hover);
    box-shadow: var(--glass-hover-shadow);
}

.chart-item div {
    width: 100%;
    max-width: 350px; /* Increased max-width for larger charts */
    height: 350px; /* Increased height for larger charts */
    margin: 0 auto;
}

.chart-item p {
    margin-top: 10px;
    font-size: 1rem;
    color: var(--color-text-secondary);
}
        .chart-item:hover {
            transform: translateY(-3px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .chart-item div {
            width: 100%;
            max-width: 250px;
            height: 250px;
            margin: 0 auto;
        }

        .chart-item p {
            margin-top: 10px;
            font-size: 1rem;
            color: var(--color-text-secondary);
        }

        .playground-content {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 50px;
            align-items: start;
        }

        .playground-editor {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .editor-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .editor-header h3 {
            font-family: var(--font-header);
            font-size: 1.3rem;
            font-weight: 600;
        }

        .editor-textarea {
            width: 100%;
            min-height: 300px;
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 15px;
            color: var(--color-text-primary);
            font-size: 0.9rem;
            resize: vertical;
            outline: none;
        }

        .editor-textarea::placeholder {
            color: var(--color-placeholder);
        }

        .editor-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .editor-btn {
            padding: 10px 20px;
            border-radius: var(--border-radius-pill);
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .editor-btn.run {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: none;
        }

        .editor-btn.clear {
            background: var(--glass-bg);
            color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }

        .editor-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .editor-btn.clear:hover {
            background: var(--glass-bg-hover);
        }

        .playground-output {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            min-height: 150px;
        }

        .playground-output h3 {
            font-family: var(--font-header);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 15px;
        }

        .playground-output p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            line-height: 1.5;
        }

        .knowledge-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .knowledge-graph {
            width: 100%;
            height: 400px;
        }

        .history-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .history-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 20px;
        }

        .history-card {
            display: flex;
            flex-direction: column;
            gap: 15px;
        }

        .history-card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .history-card h3 {
            font-size: 1.1rem;
            font-weight: 600;
        }

        .history-date {
            font-size: 0.8rem;
            color: var(--color-text-muted);
        }

        .history-card-meta {
            display: flex;
            gap: 12px;
            font-size: 0.8rem;
            color: var(--color-text-secondary);
            flex-wrap: wrap;
        }

        .history-card-meta span i {
            margin-right: 5px;
            color: var(--color-primary);
        }

        .history-card p {
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            line-height: 1.5;
            max-height: 60px;
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
        }

        .history-card-actions {
            display: flex;
            gap: 10px;
            margin-top: auto;
        }

        .settings-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .settings-section {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: var(--glass-shadow);
        }

        .settings-section h3 {
            font-family: var(--font-header);
            font-size: 1.3rem;
            font-weight: 600;
            margin-bottom: 20px;
        }

        .settings-form {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .form-group label {
            font-size: 0.9rem;
            font-weight: 500;
            color: var(--color-text-primary);
        }

        .form-group input, .form-group select {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 12px;
            color: var(--color-text-primary);
            font-size: 0.9rem;
            outline: none;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .form-group input:focus, .form-group select:focus {
            border-color: var(--color-primary);
            box-shadow: 0 0 10px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .form-group input::placeholder {
            color: var(--color-placeholder);
        }

        .settings-actions {
            display: flex;
            gap: 10px;
            justify-content: flex-end;
        }

        .settings-btn {
            padding: 10px 20px;
            border-radius: var(--border-radius-pill);
            font-size: 0.85rem;
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .settings-btn.save {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: none;
        }

        .settings-btn.cancel {
            background: var(--glass-bg);
            color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }

        .settings-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }

        .settings-btn.cancel:hover {
            background: var(--glass-bg-hover);
        }

        @media (max-width: 1024px) {
    .sidebar {
        width: 240px;
    }

    .main-content {
        margin-left: 240px;
    }

    .dashboard-content {
        grid-template-columns: 1fr; /* Single column for smaller screens */
    }

    .activity-chart {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); /* Adjust chart size for smaller screens */
    }

    .chart-item div {
        max-width: 300px; /* Slightly smaller charts for tablet screens */
        height: 300px;
    }
}

@media (max-width: 768px) {
    .sidebar {
        transform: translateX(-100%);
        width: 220px;
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-left: 0;
        padding: 20px;
    }

    .menu-toggle {
        display: block;
    }

    .header {
        padding: 15px 20px;
    }

    .header-title {
        font-size: 1.4rem;
    }

    .welcome-card h1 {
        font-size: 1.6rem;
    }

    .dashboard-content {
        grid-template-columns: 1fr;
    }

    .activity-chart {
        grid-template-columns: 1fr; /* Single column for charts on mobile */
    }

    .chart-item div {
        max-width: 250px; /* Smaller charts for mobile */
        height: 250px;
    }

    .header-actions {
        flex-direction: column;
        align-items: flex-end;
        gap: 10px;
    }

    .prompts-toolbar {
        flex-direction: column;
        align-items: flex-start;
        gap: 15px;
    }

    .prompts-search {
        max-width: 100%;
    }

    .prompts-filter {
        flex-wrap: wrap;
    }

    .playground-content {
        grid-template-columns: 1fr;
    }

    .history-list {
        grid-template-columns: 1fr;
    }
}
    </style>
</head>
<body>
    <div class="app-container">
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-logo">allhub</div>
            <div class="sidebar-nav">
                <a href="#" class="active" data-view="dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="#" data-view="my-prompts"><i class="fas fa-lightbulb"></i> My Prompts</a>
                <a href="#" data-view="history"><i class="fas fa-history"></i> History</a>
                <a href="#" data-view="playground"><i class="fas fa-rocket"></i> Playground</a>
                <a href="#" data-view="settings"><i class="fas fa-cog"></i> Settings</a>
            </div>
            <div class="sidebar-footer">
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <div class="user-avatar">EV</div>
                        <span>Elena V.</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user-circle"></i> Profile</a>
                        <a href="#"><i class="fas fa-credit-card"></i> Subscription</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Log Out</a>
                    </div>
                </div>
            </div>
        </nav>
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle"><i class="fas fa-bars"></i></button>
                    <h2 class="header-title" id="headerTitle">Dashboard</h2>
                </div>
                <div class="header-actions">
                    <button class="new-prompt-btn"><i class="fas fa-plus-circle"></i> New Prompt</button>
                </div>
            </header>
            <section id="dashboardView" class="content-view active">
                <div class="dashboard-content">
                    <div class="welcome-card">
                        <h1>Welcome back, Elena!</h1>
                        <p>Ready to create amazing prompts today?</p>
                    </div>
                    <div class="activity-card">
                        <h2 class="section-title"><i class="fas fa-chart-line"></i> Your Recent Activity</h2>
                        <div class="activity-chart">
                            <div class="chart-item">
                                <div id="savedPromptsChart"></div>
                                <p>Saved Prompts: 27</p>
                            </div>
                            <div class="chart-item">
                                <div id="promptsUsedChart"></div>
                                <p>Prompts Used (Month): 152</p>
                            </div>
                            <div class="chart-item">
                                <div id="activeFavoritesChart"></div>
                                <p>Active Favorites: 8</p>
                            </div>
                            <div class="chart-item">
                                <div id="responseTimeChart"></div>
                                <p>Avg. Response Time: 1.6s</p>
                            </div>
                            <div class="chart-item">
                                <div id="userEngagementChart"></div>
                                <p>User Engagement: 85%</p>
                            </div>
                            <div class="chart-item">
                                <div id="promptQualityChart"></div>
                                <p>Prompt Quality Score: 92%</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="myPromptsView" class="content-view">
                <div class="prompts-content">
                    <h2 class="section-title"><i class="fas fa-lightbulb"></i> My Prompts</h2>
                    <div class="prompts-toolbar">
                        <div class="prompts-search">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="Search prompts...">
                        </div>
                        <div class="prompts-filter">
                            <button class="filter-btn active">All</button>
                            <button class="filter-btn">Favorites</button>
                            <button class="filter-btn">Recent</button>
                        </div>
                    </div>
                    <div class="prompts-list">
                        <div class="prompt-card">
                            <div class="prompt-card-header">
                                <h3>Generate Marketing Email</h3>
                            </div>
                            <div class="prompt-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 150</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~400</span>
                                <span><i class="far fa-clock"></i> Time: ~1.8s</span>
                            </div>
                            <p>Create a professional marketing email for a new product launch...</p>
                            <div class="prompt-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                <button class="favorite-action-btn delete"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                        <div class="prompt-card">
                            <div class="prompt-card-header">
                                <h3>Social Media Post Ideas</h3>
                            </div>
                            <div class="prompt-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 120</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~300</span>
                                <span><i class="far fa-clock"></i> Time: ~1.2s</span>
                            </div>
                            <p>Generate 5 engaging social media post ideas for a tech startup...</p>
                            <div class="prompt-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                <button class="favorite-action-btn delete"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                    </div>
                    <div class="recent-prompts-card">
                        <h2 class="section-title"><i class="fas fa-clock"></i> Recent Prompts (Showing last 3)</h2>
                        <div class="recent-prompts-list">
                            <div class="recent-prompt-item">
                                <div class="recent-prompt-item-header">
                                    <h3>Formal Email Generator</h3>
                                    <span class="recent-prompt-timestamp">Today, 10:30 AM</span>
                                </div>
                                <p>Generated a formal email for a client meeting request.</p>
                            </div>
                            <div class="recent-prompt-item">
                                <div class="recent-prompt-item-header">
                                    <h3>Advanced Sentiment Analysis</h3>
                                    <span class="recent-prompt-timestamp">Yesterday, 4:15 PM</span>
                                </div>
                                <p>Analyzed customer feedback for sentiment trends.</p>
                            </div>
                            <div class="recent-prompt-item">
                                <div class="recent-prompt-item-header">
                                    <h3>Technical Translator ES-EN</h3>
                                    <span class="recent-prompt-timestamp">3 days ago</span>
                                </div>
                                <p>Translated a technical document from Spanish to English.</p>
                            </div>
                        </div>
                    </div>
                    <div class="prompting-techniques-card">
                        <h2 class="section-title"><i class="fas fa-lightbulb"></i> Prompting Techniques Used</h2>
                        <div class="techniques-list">
                            <div class="technique-item"><p>Chain of Thought (CoT)</p></div>
                            <div class="technique-item"><p>Few-Shot Learning</p></div>
                            <div class="technique-item"><p>Zero-Shot Learning</p></div>
                            <div class="technique-item"><p>Role-Playing</p></div>
                        </div>
                    </div>
                    <div class="llms-used-card">
                        <h2 class="section-title"><i class="fas fa-robot"></i> LLMs Used</h2>
                        <div class="llms-list">
                            <div class="llm-item"><p>Grok 3</p></div>
                            <div class="llm-item"><p>Custom Model A</p></div>
                            <div class="llm-item"><p>Custom Model B</p></div>
                        </div>
                    </div>
                    <div class="favorites-card">
                        <h2 class="section-title"><i class="fas fa-bolt"></i> Quick Access: Favorites</h2>
                        <div class="favorites-list">
                            <div class="favorite-item">
                                <div class="favorite-item-header">
                                    <h3>Improve Article with CoT</h3>
                                </div>
                                <div class="favorite-item-meta">
                                    <span><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                                    <span><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                                    <span><i class="far fa-clock"></i> Time: ~2s</span>
                                </div>
                                <p>Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process...</p>
                                <div class="favorite-item-actions">
                                    <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use in Playground</button>
                                    <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                </div>
                            </div>
                            <div class="favorite-item">
                                <div class="favorite-item-header">
                                    <h3>Summary PDF with Few-Shot</h3>
                                </div>
                                <div class="favorite-item-meta">
                                    <span><i class="fas fa-sign-in-alt"></i> Input: 170</span>
                                    <span><i class="fas fa-sign-out-alt"></i> Output: ~500</span>
                                    <span><i class="far fa-clock"></i> Time: ~1.5s</span>
                                </div>
                                <p>Write a blog article about how to summarize a PDF. Follow the style and structure of these examples...</p>
                                <div class="favorite-item-actions">
                                    <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use in Playground</button>
                                    <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="historyView" class="content-view">
                <div class="history-content">

                    <div class="history-list">
                        <div class="history-card">
                            <div class="history-card-header">
                                <h3>Improve Article with CoT</h3>
                                <span class="history-date">2025-05-22</span>
                            </div>
                            <div class="history-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                                <span><i class="far fa-clock"></i> Time: ~2s</span>
                            </div>
                            <p>Input: Write a 400-word blog article about how to summarize a PDF...</p>
                            <p>Output: A well-structured 400-word article with clear steps...</p>
                            <div class="history-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Reuse</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-copy"></i> Copy</button>
                            </div>
                        </div>
                        <div class="history-card">
                            <div class="history-card-header">
                                <h3>Summary PDF with Few-Shot</h3>
                                <span class="history-date">2025-05-21</span>
                            </div>
                            <div class="history-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 170</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~500</span>
                                <span><i class="far fa-clock"></i> Time: ~1.5s</span>
                            </div>
                            <p>Input: Write a blog article about how to summarize a PDF...</p>
                            <p>Output: A concise article following provided examples...</p>
                            <div class="history-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Reuse</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-copy"></i> Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="playgroundView" class="content-view">
                <div class="playground-content">
                    <div class="playground-editor">
                        <div class="editor-header">
                            <h3>Prompt Editor</h3>
                        </div>
                        <textarea class="editor-textarea" placeholder="Enter your prompt here..."></textarea>
                        <div class="editor-actions">
                            <button class="editor-btn run"><i class="fas fa-play"></i> Run</button>
                            <button class="editor-btn clear"><i class="fas fa-eraser"></i> Clear</button>
                        </div>
                    </div>
                    <div class="knowledge-card">
                        <h2 class="section-title"><i class="fas fa-sitemap"></i> Knowledge Graph</h2>
                        <p>Explore relationships in your prompting workflow:</p>
                        <div id="knowledgeGraph" class="knowledge-graph"></div>
                    </div>
                </div>
                <div class="playground-output">
                    <h3>Output</h3>
                    <p>Results will appear here after running your prompt...</p>
                </div>
            </section>
            <section id="settingsView" class="content-view">
                <div class="settings-content">
                    <div class="settings-section">
                        <h3>Profile Settings</h3>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" value="Elena V." placeholder="Enter username">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" value="<EMAIL>" placeholder="Enter email">
                            </div>
                            <div class="form-group">
                                <label for="language">Language</label>
                                <select id="language">
                                    <option value="en" selected>English</option>
                                    <option value="es">Spanish</option>
                                    <option value="fr">French</option>
                                </select>
                            </div>
                            <div class="settings-actions">
                                <button class="settings-btn save"><i class="fas fa-save"></i> Save</button>
                                <button class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                            </div>
                        </div>
                    </div>
                    <div class="settings-section">
                        <h3>Notification Preferences</h3>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="email-notifications">
                                    <input type="checkbox" id="email-notifications" checked> Email Notifications
                                </label>
                            </div>
                            <div class="form-group">
                                <label for="push-notifications">
                                    <input type="checkbox" id="push-notifications"> Push Notifications
                                </label>
                            </div>
                            <div class="settings-actions">
                                <button class="settings-btn save"><i class="fas fa-save"></i> Save</button>
                                <button class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        const sidebar = document.getElementById('sidebar');
        const menuToggle = document.getElementById('menuToggle');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const headerTitle = document.getElementById('headerTitle');
        const navLinks = document.querySelectorAll('.sidebar-nav a');
        const contentViews = document.querySelectorAll('.content-view');

        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('visible');
        });

        document.addEventListener('click', (e) => {
            if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('visible');
            }
            if (!sidebar.contains(e.target) && !menuToggle.contains(e.target) && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const viewId = link.dataset.view + 'View';
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
                contentViews.forEach(view => view.classList.remove('active'));
                document.getElementById(viewId).classList.add('active');
                headerTitle.textContent = link.textContent;
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
                console.log(`Switched to ${link.dataset.view}`);
            });
        });

        document.querySelectorAll('.favorite-action-btn, .prompt-card-actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.classList.contains('playground') ? 'Use in Playground' :
                              e.target.classList.contains('edit') ? 'Edit' : 'Delete';
                const promptTitle = e.target.closest('.favorite-item, .prompt-card').querySelector('h3').textContent;
                console.log(`${action} clicked for prompt: ${promptTitle}`);
            });
        });

        document.querySelectorAll('.history-card-actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.classList.contains('playground') ? 'Reuse' : 'Copy';
                const promptTitle = e.target.closest('.history-card').querySelector('h3').textContent;
                console.log(`${action} clicked for history item: ${promptTitle}`);
            });
        });

        document.querySelectorAll('.quick-access-btn').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = btn.dataset.action;
                console.log(`Quick Access: ${action} clicked`);
                if (action === 'create-prompt') {
                    navLinks.forEach(link => link.classList.remove('active'));
                    document.querySelector('[data-view="playground"]').classList.add('active');
                    contentViews.forEach(view => view.classList.remove('active'));
                    document.getElementById('playgroundView').classList.add('active');
                    headerTitle.textContent = 'Playground';
                } else if (action === 'view-library') {
                    navLinks.forEach(link => link.classList.remove('active'));
                    document.querySelector('[data-view="my-prompts"]').classList.add('active');
                    contentViews.forEach(view => view.classList.remove('active'));
                    document.getElementById('myPromptsView').classList.add('active');
                    headerTitle.textContent = 'My Prompts';
                }
            });
        });

        const editorTextarea = document.querySelector('.editor-textarea');
        document.querySelector('.editor-btn.run').addEventListener('click', () => {
            const prompt = editorTextarea.value;
            console.log(`Running prompt: ${prompt}`);
            document.querySelector('.playground-output p').textContent = `Generated output for: ${prompt || 'No input provided'}`;
        });

        document.querySelector('.editor-btn.clear').addEventListener('click', () => {
            editorTextarea.value = '';
            document.querySelector('.playground-output p').textContent = 'Results will appear here after running your prompt...';
            console.log('Editor cleared');
        });

        document.querySelectorAll('.settings-btn.save').forEach(btn => {
            btn.addEventListener('click', () => {
                const form = btn.closest('.settings-form');
                const inputs = form.querySelectorAll('input, select');
                const settings = {};
                inputs.forEach(input => {
                    settings[input.id] = input.type === 'checkbox' ? input.checked : input.value;
                });
                console.log('Saving settings:', settings);
            });
        });

        document.querySelectorAll('.settings-btn.cancel').forEach(btn => {
            btn.addEventListener('click', () => {
                console.log('Settings changes cancelled');
            });
        });

        document.querySelector('.new-prompt-btn').addEventListener('click', () => {
            console.log('New Prompt clicked');
            navLinks.forEach(link => link.classList.remove('active'));
            document.querySelector('[data-view="playground"]').classList.add('active');
            contentViews.forEach(view => view.classList.remove('active'));
            document.getElementById('playgroundView').classList.add('active');
            headerTitle.textContent = 'Playground';
        });

        // Initialize ECharts for each chart
        const savedPromptsChart = echarts.init(document.getElementById('savedPromptsChart'));
        savedPromptsChart.setOption({
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: { trigger: 'item' },
            series: [{
                name: 'Saved Prompts',
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                roseType: 'radius',
                data: [
                    { value: 27, name: 'Saved' },
                    { value: 73, name: 'Remaining' }
                ].sort((a, b) => a.value - b.value),
                label: { color: 'rgba(0, 0, 0, 0.5)' },
                labelLine: {
                    lineStyle: { color: 'rgba(0, 0, 0, 0.3)' },
                    smooth: 0.2,
                    length: 10,
                    length2: 20
                },
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ]),
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                },
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: (idx) => Math.random() * 200
            }]
        });

        const promptsUsedChart = echarts.init(document.getElementById('promptsUsedChart'));
        promptsUsedChart.setOption({
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: { trigger: 'item' },
            series: [{
                name: 'Prompts Used',
                type: 'pie',
                radius: ['40%', '55%'],
                center: ['50%', '50%'],
                data: [
                    { value: 152, name: 'Used' },
                    { value: 48, name: 'Remaining' }
                ],
                label: { color: 'rgba(0, 0, 0, 0.5)' },
                labelLine: {
                    lineStyle: { color: 'rgba(0, 0, 0, 0.3)' },
                    smooth: 0.2,
                    length: 10,
                    length2: 20
                },
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ]),
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                },
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: (idx) => Math.random() * 200
            }]
        });

        const activeFavoritesChart = echarts.init(document.getElementById('activeFavoritesChart'));
        activeFavoritesChart.setOption({
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: { trigger: 'item' },
            series: [{
                name: 'Active Favorites',
                type: 'pie',
                radius: '55%',
                center: ['50%', '50%'],
                roseType: 'area',
                data: [
                    { value: 8, name: 'Active' },
                    { value: 92, name: 'Remaining' }
                ].sort((a, b) => a.value - b.value),
                label: { color: 'rgba(0, 0, 0, 0.5)' },
                labelLine: {
                    lineStyle: { color: 'rgba(0, 0, 0, 0.3)' },
                    smooth: 0.2,
                    length: 10,
                    length2: 20
                },
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ]),
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                },
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: (idx) => Math.random() * 200
            }]
        });

        const responseTimeChart = echarts.init(document.getElementById('responseTimeChart'));
        responseTimeChart.setOption({
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: { trigger: 'axis' },
            xAxis: {
                type: 'category',
                data: ['<1s', '1-2s', '>2s'],
                axisLabel: { color: 'rgba(0, 0, 0, 0.5)' }
            },
            yAxis: {
                type: 'value',
                axisLabel: { color: 'rgba(0, 0, 0, 0.5)' }
            },
            series: [{
                name: 'Response Time',
                type: 'bar',
                data: [40, 50, 10],
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ]),
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                },
                animationEasing: 'elasticOut',
                animationDelay: (idx) => Math.random() * 200
            }]
        });

        const userEngagementChart = echarts.init(document.getElementById('userEngagementChart'));
        userEngagementChart.setOption({
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: { trigger: 'item' },
            series: [{
                name: 'User Engagement',
                type: 'pie',
                radius: ['40%', '55%'],
                center: ['50%', '50%'],
                data: [
                    { value: 85, name: 'Engaged' },
                    { value: 15, name: 'Not Engaged' }
                ],
                label: { color: 'rgba(0, 0, 0, 0.5)' },
                labelLine: {
                    lineStyle: { color: 'rgba(0, 0, 0, 0.3)' },
                    smooth: 0.2,
                    length: 10,
                    length2: 20
                },
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ]),
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                },
                animationType: 'scale',
                animationEasing: 'elasticOut',
                animationDelay: (idx) => Math.random() * 200
            }]
        });

        const promptQualityChart = echarts.init(document.getElementById('promptQualityChart'));
        promptQualityChart.setOption({
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: { trigger: 'axis' },
            xAxis: {
                type: 'category',
                data: ['High', 'Medium', 'Low'],
                axisLabel: { color: 'rgba(0, 0, 0, 0.5)' }
            },
            yAxis: {
                type: 'value',
                axisLabel: { color: 'rgba(0, 0, 0, 0.5)' }
            },
            series: [{
                name: 'Prompt Quality',
                type: 'line',
                smooth: true,
                data: [92, 6, 2],
                itemStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ])
                },
                lineStyle: {
                    color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                        { offset: 0, color: '#C645F9' },
                        { offset: 1, color: '#000d83' }
                    ]),
                    shadowBlur: 20,
                    shadowColor: 'rgba(0, 0, 0, 0.3)'
                },
                animationEasing: 'elasticOut',
                animationDelay: (idx) => Math.random() * 200
            }]
        });

        const width = 600;
        const height = 400;

        const svg = d3.select("#knowledgeGraph")
            .append("svg")
            .attr("width", "100%")
            .attr("height", height)
            .attr("viewBox", `0 0 ${width} ${height}`);

        const nodes = [
            { id: "Prompt", group: 1 },
            { id: "Role", group: 2 },
            { id: "Task", group: 3 },
            { id: "Parameter", group: 4 },
            { id: "Tone", group: 5 },
            { id: "Context", group: 6 },
            { id: "Output", group: 7 },
            { id: "Input", group: 8 }
        ];

        const links = [
            { source: "Prompt", target: "Role", value: 1 },
            { source: "Prompt", target: "Task", value: 1 },
            { source: "Prompt", target: "Parameter", value: 1 },
            { source: "Prompt", target: "Tone", value: 1 },
            { source: "Prompt", target: "Context", value: 1 },
            { source: "Input", target: "Prompt", value: 1 },
            { source: "Prompt", target: "Output", value: 1 }
        ];

        const simulation = d3.forceSimulation(nodes)
            .force("link", d3.forceLink(links).id(d => d.id).distance(100))
            .force("charge", d3.forceManyBody().strength(-300))
            .force("center", d3.forceCenter(width / 2, height / 2));

        const link = svg.append("g")
            .attr("stroke", "#999")
            .attr("stroke-opacity", 0.6)
            .selectAll("line")
            .data(links)
            .join("line")
            .attr("stroke-width", d => Math.sqrt(d.value));

        const node = svg.append("g")
            .selectAll("circle")
            .data(nodes)
            .join("circle")
            .attr("r", 10)
            .attr("fill", d => d.group === 1 ? "#2196F3" : "#E91E63");

        const labels = svg.append("g")
            .selectAll("text")
            .data(nodes)
            .join("text")
            .text(d => d.id)
            .attr("font-size", 10)
            .attr("dx", 15)
            .attr("dy", 4);

        simulation.on("tick", () => {
            link
                .attr("x1", d => d.source.x)
                .attr("y1", d => d.source.y)
                .attr("x2", d => d.target.x)
                .attr("y2", d => d.target.y);

            node
                .attr("cx", d => d.x)
                .attr("cy", d => d.y);

            labels
                .attr("x", d => d.x)
                .attr("y", d => d.y);
        });
    </script>
</body>
</html>