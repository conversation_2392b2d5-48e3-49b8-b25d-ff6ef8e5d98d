<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>allhub - Private Area</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            --color-primary: #C645F9;
            --color-secondary: #000d83;
            --color-dark: #0D0425;
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.1);
            --glass-bg-hover: rgba(255, 255, 255, 0.18);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.06);
            --glass-border: rgba(255, 255, 255, 0.2);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.12);
            --glass-hover-shadow: 0 12px 40px rgba(13, 4, 37, 0.18);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 70%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 50%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.2);
            --color-active-accent: rgba(94, 108, 231, 0.3);
            --border-radius-card: 24px;
            --border-radius-pill: 50px;
            --border-radius-element: 16px;
            --transition-speed: 0.3s;
            --transition-ease: ease-in-out;
            --color-placeholder: var(--color-text-muted);
            --color-icon-gray: var(--color-text-secondary);
            --sidebar-width: 280px;
            --header-height: 80px;
            --user-menu-bg: linear-gradient(135deg, #C645F9, #000d83);
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--color-text-primary);
            background: linear-gradient(135deg, #F5F5F5 0%, #E0E0E0 50%, #D5D5D5 100%);
            background-attachment: fixed;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border-right: 1px solid var(--glass-border);
            padding: 30px 20px;
            position: fixed;
            height: 100%;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            box-shadow: var(--glass-shadow);
            transition: transform var(--transition-speed) var(--transition-ease);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 30px;
        }

        .sidebar-nav {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 10px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 14px 20px;
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--color-text-primary);
            text-decoration: none;
            border-radius: var(--border-radius-element);
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
            overflow: hidden;
        }

        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }

        .sidebar-nav a:hover::before { left: 100%; }

        .sidebar-nav a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(8px);
            box-shadow: var(--glass-hover-shadow);
        }

        .sidebar-nav a.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            font-weight: 600;
        }

        .sidebar-nav a i {
            font-size: 1.1rem;
            color: var(--color-icon-gray);
        }

        .sidebar-nav a.active i {
            color: var(--color-primary);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
        }

        .user-menu {
            position: relative;
            margin-bottom: 20px;
        }

        .user-menu-btn {
            background: var(--user-menu-bg);
            border: none;
            border-radius: var(--border-radius-pill);
            padding: 10px 15px;
            display: flex;
            align-items: center;
            gap: 10px;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            width: 100%;
        }

        .user-menu-btn:hover {
            transform: translateY(-2px);
            box-shadow: var(--glass-hover-shadow);
        }

        .user-avatar {
            width: 30px;
            height: 30px;
            border-radius: 50%;
            background: var(--user-menu-bg);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 1rem;
        }

        .user-dropdown {
            position: absolute;
            bottom: calc(100% + 10px);
            left: 0;
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: var(--glass-hover-shadow);
            width: 200px;
            padding: 10px 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1000;
        }

        .user-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            color: var(--color-text-primary);
            text-decoration: none;
            font-size: 0.9rem;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .user-dropdown a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(5px);
        }

        .user-dropdown a i {
            margin-right: 10px;
            color: var(--color-text-muted);
        }

        .user-dropdown .divider {
            height: 1px;
            background: var(--glass-border);
            margin: 8px 0;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            flex-grow: 1;
            padding: 40px;
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .header {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--glass-shadow);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            color: var(--color-text-primary);
            cursor: pointer;
            background: none;
            border: none;
        }

        .header-title {
            font-family: var(--font-header);
            font-size: 1.6rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .new-prompt-btn {
            padding: 12px 24px;
            border-radius: var(--border-radius-pill);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .new-prompt-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 10px 30px color-mix(in srgb, var(--color-primary) 30%, transparent);
        }

        .content-view {
            display: none;
            animation: fadeIn 0.5s var(--transition-ease);
        }

        .content-view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { opacity: 0; transform: translateY(20px); }
            to { opacity: 1; transform: translateY(0); }
        }

        .welcome-card, .activity-card {
            background: var(--glass-bg);
            backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 30px;
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .welcome-card:hover, .activity-card:hover {
            transform: translateY(-5px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .welcome-card {
            width: 100%;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-light);
            padding: 20px 30px;
            text-align: center;
        }

        .welcome-card h1 {
            font-family: var(--font-header);
            font-size: 2rem;
            font-weight: 700;
            margin-bottom: 10px;
        }

        .welcome-card p {
            font-size: 1rem;
            line-height: 1.5;
        }

        .section-title {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 25px;
            display: flex;
            align-items: center;
            gap: 10px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .section-title i {
            color: var(--color-primary);
        }

        .dashboard-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .activity-card {
            width: 100%;
        }

        .activity-chart {
            display: grid;
            grid-template-columns: repeat(3, 1fr);
            gap: 20px;
            max-width: 1200px;
            margin: 0 auto;
        }

        .chart-item {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(15px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 20px;
            text-align: center;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
        }

        .chart-item:hover {
            transform: translateY(-3px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .chart-item div {
            width: 100%;
            height: 300px;
            margin: 0 auto;
            flex-grow: 1;
        }

        .chart-item p {
            margin-top: 10px;
            font-size: 0.9rem;
            color: var(--color-text-secondary);
            font-weight: 500;
        }

        .echarts-tooltip {
            background: rgba(255, 255, 255, 0.1) !important;
            border: 1px solid rgba(255, 255, 255, 0.2) !important;
            backdrop-filter: blur(10px);
            -webkit-backdrop-filter: blur(10px);
            border-radius: 8px;
            padding: 10px;
        }

        @media (max-width: 1024px) {
            .sidebar {
                width: 240px;
            }

            .main-content {
                margin-left: 240px;
            }

            .activity-chart {
                grid-template-columns: repeat(3, minmax(250px, 1fr));
            }

            .chart-item div {
                height: 250px;
            }

            .welcome-card h1 {
                font-size: 1.8rem;
            }

            .welcome-card p {
                font-size: 0.9rem;
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 220px;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .menu-toggle {
                display: block;
            }

            .header {
                padding: 15px 20px;
            }

            .header-title {
                font-size: 1.4rem;
            }

            .welcome-card h1 {
                font-size: 1.6rem;
            }

            .welcome-card p {
                font-size: 0.8rem;
            }

            .activity-chart {
                grid-template-columns: 1fr;
            }

            .chart-item div {
                height: 200px;
            }
        }

        @media (max-width: 480px) {
            .new-prompt-btn {
                padding: 8px 16px;
                font-size: 0.8rem;
            }

            .welcome-card h1 {
                font-size: 1.4rem;
            }

            .welcome-card p {
                font-size: 0.7rem;
            }

            .chart-item div {
                height: 180px;
            }
        }
    </style>
</head>
<body>
    <div class="app-container">
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-logo">allhub</div>
            <div class="sidebar-nav">
                <a href="#" class="active" data-view="dashboard"><i class="fas fa-tachometer-alt"></i> Dashboard</a>
                <a href="#" data-view="my-prompts"><i class="fas fa-lightbulb"></i> My Prompts</a>
                <a href="#" data-view="history"><i class="fas fa-history"></i> History</a>
                <a href="#" data-view="playground"><i class="fas fa-rocket"></i> Playground</a>
                <a href="#" data-view="settings"><i class="fas fa-cog"></i> Settings</a>
            </div>
            <div class="sidebar-footer">
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <div class="user-avatar">EV</div>
                        <span>Elena V.</span>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user-circle"></i> Profile</a>
                        <a href="#"><i class="fas fa-credit-card"></i> Subscription</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Log Out</a>
                    </div>
                </div>
            </div>
        </nav>
        <main class="main-content">
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle"><i class="fas fa-bars"></i></button>
                    <h2 class="header-title" id="headerTitle">Dashboard</h2>
                </div>
                <div class="header-actions">
                    <button class="new-prompt-btn"><i class="fas fa-plus-circle"></i> New Prompt</button>
                </div>
            </header>
            <section id="dashboardView" class="content-view active">
                <div class="dashboard-content">
                    <div class="welcome-card">
                        <h1>Welcome, Elena!</h1>
                        <p>Ready to create prompts?</p>
                    </div>
                    <div class="activity-card">
                        <h2 class="section-title"><i class="fas fa-chart-line"></i> Activity</h2>
                        <div class="activity-chart">
                            <div class="chart-item">
                                <div id="creditStatusChart"></div>
                                <p>Credits Used</p>
                            </div>
                            <div class="chart-item">
                                <div id="promptsGeneratedChart"></div>
                                <p>Prompts Generated</p>
                            </div>
                            <div class="chart-item">
                                <div id="promptsFavoritesChart"></div>
                                <p>Favorite Prompts</p>
                            </div>
                        </div>
                        <div class="activity-chart">
                            <div class="chart-item">
                                <div id="reusedPromptsChart"></div>
                                <p>Reused Prompts</p>
                            </div>
                            <div class="chart-item">
                                <div id="newRelationshipsChart"></div>
                                <p>New Relationships</p>
                            </div>
                            <div class="chart-item">
                                <div id="mostUsedLLMsChart"></div>
                                <p>Top LLMs</p>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="myPromptsView" class="content-view">
                <div class="prompts-content">
                    <h2 class="section-title"><i class="fas fa-lightbulb"></i> My Prompts</h2>
                    <div class="prompts-toolbar">
                        <div class="prompts-search">
                            <i class="fas fa-search"></i>
                            <input type="text" placeholder="Search prompts...">
                        </div>
                        <div class="prompts-filter">
                            <button class="filter-btn active">All</button>
                            <button class="filter-btn">Favorites</button>
                            <button class="filter-btn">Recent</button>
                        </div>
                    </div>
                    <div class="prompts-list">
                        <div class="prompt-card">
                            <div class="prompt-card-header">
                                <h3>Generate Marketing Email</h3>
                            </div>
                            <div class="prompt-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 150</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~400</span>
                                <span><i class="far fa-clock"></i> Time: ~1.8s</span>
                            </div>
                            <p>Create a professional marketing email for a new product launch...</p>
                            <div class="prompt-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                <button class="favorite-action-btn delete"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                        <div class="prompt-card">
                            <div class="prompt-card-header">
                                <h3>Social Media Post Ideas</h3>
                            </div>
                            <div class="prompt-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 120</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~300</span>
                                <span><i class="far fa-clock"></i> Time: ~1.2s</span>
                            </div>
                            <p>Generate 5 engaging social media post ideas for a tech startup...</p>
                            <div class="prompt-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Use</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-edit"></i> Edit</button>
                                <button class="favorite-action-btn delete"><i class="fas fa-trash"></i> Delete</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="historyView" class="content-view">
                <div class="history-content">
                    <div class="history-list">
                        <div class="history-card">
                            <div class="history-card-header">
                                <h3>Improve Article with CoT</h3>
                                <span class="history-date">2025-05-22</span>
                            </div>
                            <div class="history-card-meta">
                                <span><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                                <span><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                                <span><i class="far fa-clock"></i> Time: ~2s</span>
                            </div>
                            <p>Input: Write a 400-word blog article about how to summarize a PDF...</p>
                            <div class="history-card-actions">
                                <button class="favorite-action-btn playground"><i class="fas fa-play-circle"></i> Reuse</button>
                                <button class="favorite-action-btn edit"><i class="fas fa-copy"></i> Copy</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
            <section id="playgroundView" class="content-view">
                <div class="playground-content">
                    <div class="playground-editor">
                        <div class="editor-header">
                            <h3>Prompt Editor</h3>
                        </div>
                        <textarea class="editor-textarea" placeholder="Enter your prompt here..."></textarea>
                        <div class="editor-actions">
                            <button class="editor-btn run"><i class="fas fa-play"></i> Run</button>
                            <button class="editor-btn clear"><i class="fas fa-eraser"></i> Clear</button>
                        </div>
                    </div>
                </div>
            </section>
            <section id="settingsView" class="content-view">
                <div class="settings-content">
                    <div class="settings-section">
                        <h3>Profile Settings</h3>
                        <div class="settings-form">
                            <div class="form-group">
                                <label for="username">Username</label>
                                <input type="text" id="username" value="Elena V." placeholder="Enter username">
                            </div>
                            <div class="form-group">
                                <label for="email">Email</label>
                                <input type="email" id="email" value="<EMAIL>" placeholder="Enter email">
                            </div>
                            <div class="settings-actions">
                                <button class="settings-btn save"><i class="fas fa-save"></i> Save</button>
                                <button class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                            </div>
                        </div>
                    </div>
                </div>
            </section>
        </main>
    </div>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <script>
        // Debounce function for resize events
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Chart style configuration
        const chartStyle = {
            backgroundColor: 'rgba(255, 255, 255, 0.06)',
            tooltip: {
                trigger: 'item',
                confine: true,
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                borderColor: 'rgba(255, 255, 255, 0.2)',
                textStyle: { color: '#FFFFFF', fontFamily: 'Manrope, sans-serif' },
                extraCssText: 'backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border-radius: 8px; padding: 10px;'
            },
            itemStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#C645F9' },
                    { offset: 1, color: '#000d83' }
                ]),
                shadowBlur: 10,
                shadowColor: 'rgba(0, 0, 0, 0.3)'
            },
            lineStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: '#C645F9' },
                    { offset: 1, color: '#000d83' }
                ])
            },
            areaStyle: {
                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                    { offset: 0, color: 'rgba(198, 69, 249, 0.5)' },
                    { offset: 1, color: 'rgba(0, 13, 131, 0.3)' }
                ])
            },
            label: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif', show: false },
            animationType: 'scale',
            animationEasing: 'elasticOut',
            animationDelay: (idx) => Math.random() * 200
        };

        // Initialize charts
        function initializeCharts() {
            // Credit Status (Pie Chart)
            const creditStatusChart = echarts.init(document.getElementById('creditStatusChart'));
            creditStatusChart.setOption({
                ...chartStyle,
                series: [{
                    name: 'Credits',
                    type: 'pie',
                    radius: '55%',
                    center: ['50%', '50%'],
                    data: [
                        { value: 125, name: 'Used' },
                        { value: 125, name: 'Remaining' }
                    ],
                    emphasis: { scale: true, scaleSize: 10 }
                }]
            });

            // Prompts Generated (Bar Chart)
            const promptsGeneratedChart = echarts.init(document.getElementById('promptsGeneratedChart'));
            promptsGeneratedChart.setOption({
                ...chartStyle,
                tooltip: { trigger: 'axis', confine: true },
                xAxis: {
                    type: 'category',
                    data: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                    axisLabel: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif' }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif' }
                },
                series: [{
                    name: 'Prompts',
                    type: 'bar',
                    data: [50, 70, 90, 120, 152],
                    barWidth: '20%',
                    itemStyle: chartStyle.itemStyle
                }]
            });

            // Prompts in Favorites (Gauge Chart)
            const promptsFavoritesChart = echarts.init(document.getElementById('promptsFavoritesChart'));
            promptsFavoritesChart.setOption({
                ...chartStyle,
                tooltip: { trigger: 'item', confine: true },
                series: [{
                    name: 'Favorites',
                    type: 'gauge',
                    radius: '80%',
                    center: ['50%', '50%'],
                    min: 0,
                    max: 100,
                    progress: { show: true, width: 18 },
                    axisLine: { lineStyle: { width: 18, color: [[1, 'rgba(255,255,255,0.2)']] } },
                    axisTick: { show: false },
                    splitLine: { show: false },
                    axisLabel: { show: false },
                    detail: { valueAnimation: true, formatter: '{value}%', color: 'rgba(0,0,0,0.5)', fontFamily: 'Manrope, sans-serif', offsetCenter: [0, '70%'] },
                    data: [{ value: 8, name: 'Favorites' }]
                }]
            });

            // Reused Prompts (Area Chart)
            const reusedPromptsChart = echarts.init(document.getElementById('reusedPromptsChart'));
            reusedPromptsChart.setOption({
                ...chartStyle,
                tooltip: { trigger: 'axis', confine: true },
                xAxis: {
                    type: 'category',
                    data: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                    axisLabel: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif' }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif' }
                },
                series: [{
                    name: 'Reused',
                    type: 'line',
                    smooth: true,
                    areaStyle: chartStyle.areaStyle,
                    lineStyle: chartStyle.lineStyle,
                    data: [10, 15, 20, 25, 30]
                }]
            });

            // New Relationships (Line Chart)
            const newRelationshipsChart = echarts.init(document.getElementById('newRelationshipsChart'));
            newRelationshipsChart.setOption({
                ...chartStyle,
                tooltip: { trigger: 'axis', confine: true },
                xAxis: {
                    type: 'category',
                    data: ['Jan', 'Feb', 'Mar', 'Apr', 'May'],
                    axisLabel: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif' }
                },
                yAxis: {
                    type: 'value',
                    axisLabel: { color: 'rgba(0, 0, 0, 0.5)', fontFamily: 'Manrope, sans-serif' }
                },
                series: [{
                    name: 'Relationships',
                    type: 'line',
                    smooth: true,
                    lineStyle: chartStyle.lineStyle,
                    data: [5, 10, 15, 20, 25]
                }]
            });

            // Most Used LLMs (Donut Chart)
            const mostUsedLLMsChart = echarts.init(document.getElementById('mostUsedLLMsChart'));
            mostUsedLLMsChart.setOption({
                ...chartStyle,
                series: [{
                    name: 'LLMs',
                    type: 'pie',
                    radius: ['40%', '55%'],
                    center: ['50%', '50%'],
                    data: [
                        { value: 60, name: 'Grok 3' },
                        { value: 25, name: 'Model A' },
                        { value: 15, name: 'Model B' }
                    ],
                    emphasis: { scale: true, scaleSize: 10 }
                }]
            });

            // Resize charts immediately after initialization
            [creditStatusChart, promptsGeneratedChart, promptsFavoritesChart, reusedPromptsChart, newRelationshipsChart, mostUsedLLMsChart].forEach(chart => {
                chart.resize();
            });

            // Debounced resize handler
            const resizeCharts = debounce(() => {
                [creditStatusChart, promptsGeneratedChart, promptsFavoritesChart, reusedPromptsChart, newRelationshipsChart, mostUsedLLMsChart].forEach(chart => {
                    chart.resize();
                });
            }, 200);

            window.addEventListener('resize', resizeCharts);
        }

        // Initialize charts after DOM is loaded
        window.addEventListener('DOMContentLoaded', initializeCharts);

        // Sidebar and navigation logic
        const sidebar = document.getElementById('sidebar');
        const menuToggle = document.getElementById('menuToggle');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const headerTitle = document.getElementById('headerTitle');
        const navLinks = document.querySelectorAll('.sidebar-nav a');
        const contentViews = document.querySelectorAll('.content-view');

        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('visible');
        });

        document.addEventListener('click', (e) => {
            if (!userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('visible');
            }
            if (!sidebar.contains(e.target) && !menuToggle.contains(e.target) && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const viewId = link.dataset.view + 'View';
                navLinks.forEach(l => l.classList.remove('active'));
                link.classList.add('active');
                contentViews.forEach(view => view.classList.remove('active'));
                document.getElementById(viewId).classList.add('active');
                headerTitle.textContent = link.textContent;
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        document.querySelectorAll('.favorite-action-btn, .prompt-card-actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.classList.contains('playground') ? 'Use in Playground' :
                              e.target.classList.contains('edit') ? 'Edit' : 'Delete';
                const promptTitle = e.target.closest('.prompt-card').querySelector('h3').textContent;
                console.log(`${action} clicked for prompt: ${promptTitle}`);
            });
        });

        document.querySelectorAll('.history-card-actions button').forEach(btn => {
            btn.addEventListener('click', (e) => {
                const action = e.target.classList.contains('playground') ? 'Reuse' : 'Copy';
                const promptTitle = e.target.closest('.history-card').querySelector('h3').textContent;
                console.log(`${action} clicked for history item: ${promptTitle}`);
            });
        });

        document.querySelector('.new-prompt-btn').addEventListener('click', () => {
            navLinks.forEach(link => link.classList.remove('active'));
            document.querySelector('[data-view="playground"]').classList.add('active');
            contentViews.forEach(view => view.classList.remove('active'));
            document.getElementById('playgroundView').classList.add('active');
            headerTitle.textContent = 'Playground';
        });
    </script>
</body>
</html>