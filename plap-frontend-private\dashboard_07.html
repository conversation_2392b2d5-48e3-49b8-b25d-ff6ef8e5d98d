<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>allhub - Private Area</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            --color-tertiary: #F9E7FE;
            --color-quaternary: #ffffff;
            --color-primary: #C645F9;
            --color-secondary: #000d83;
            --color-dark: #000d83;
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.12);
            --glass-bg-hover: rgba(255, 255, 255, 0.2);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.25);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.15);
            --glass-hover-shadow: 0 16px 48px rgba(13, 4, 37, 0.25);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 65%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 45%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.15);
            --color-active-accent: rgba(94, 108, 231, 0.2);
            --border-radius-card: 24px;
            --border-radius-pill: 50px;
            --border-radius-element: 16px;
            --transition-speed: 0.3s;
            --transition-ease: cubic-bezier(0.4, 0.0, 0.2, 1);
            --sidebar-width: 280px;
            --header-height: 80px;
            --user-menu-bg: linear-gradient(135deg, #C645F9, #000d83);
        }

        * { 
            margin: 0; 
            padding: 0; 
            box-sizing: border-box; 
        }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--color-text-primary);
            background: linear-gradient(135deg, #F5F5F5 0%, #E8E8F0 30%, #DDD9E8 70%, #D5D5D5 100%);
            background-attachment: fixed;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border-right: 1px solid var(--glass-border);
            padding: 30px 20px;
            position: fixed;
            height: 100%;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            box-shadow: var(--glass-shadow);
            transition: transform var(--transition-speed) var(--transition-ease);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 30px;
            letter-spacing: -0.5px;
        }

        .sidebar-nav {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--color-text-primary);
            text-decoration: none;
            border-radius: var(--border-radius-element);
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(8px);
            cursor: pointer;
        }

        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
            transition: left 0.6s ease;
        }

        .sidebar-nav a:hover::before { 
            left: 100%; 
        }

        .sidebar-nav a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(6px);
            box-shadow: var(--glass-hover-shadow);
        }

        .sidebar-nav a.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            font-weight: 600;
            box-shadow: var(--glass-shadow);
        }

        .sidebar-nav a i {
            font-size: 1.1rem;
            color: var(--color-text-secondary);
            transition: color var(--transition-speed) var(--transition-ease);
        }

        .sidebar-nav a.active i,
        .sidebar-nav a:hover i {
            color: var(--color-primary);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
        }

        .user-menu {
            position: relative;
            margin-bottom: 20px;
        }

        .user-menu-btn {
            background: var(--user-menu-bg);
            border: none;
            border-radius: var(--border-radius-pill);
            padding: 12px 18px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            width: 100%;
            box-shadow: var(--glass-shadow);
        }

        .user-menu-btn:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--glass-hover-shadow);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-on-gradient);
            font-weight: 700;
            font-size: 0.9rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-dropdown {
            position: absolute;
            bottom: calc(100% + 12px);
            left: 0;
            background: var(--glass-bg-strong);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: var(--glass-hover-shadow);
            width: 220px;
            padding: 12px 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1000;
        }

        .user-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            padding: 12px 18px;
            color: var(--color-text-primary);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .user-dropdown a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(6px);
        }

        .user-dropdown a i {
            margin-right: 12px;
            color: var(--color-text-muted);
            width: 16px;
            text-align: center;
        }

        .user-dropdown .divider {
            height: 1px;
            background: var(--glass-border);
            margin: 8px 0;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            flex-grow: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .header {
            background: linear-gradient(135deg, var(--color-tertiary), var(--color-quaternary)) !important;
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--glass-shadow);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            color: var(--color-text-primary);
            cursor: pointer;
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .menu-toggle:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
        }

        .header-title {
            font-family: var(--font-header);
            font-size: 1.7rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .new-prompt-btn {
            padding: 14px 28px;
            border-radius: var(--border-radius-pill);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 20px rgba(198, 69, 249, 0.3);
        }

        .new-prompt-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 40px rgba(198, 69, 249, 0.4);
        }

        .content-view {
            display: none;
            animation: fadeIn 0.6s var(--transition-ease);
        }

        .content-view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 30px;
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .card:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .welcome-card {
        background: var(--glass-bg);
        color: var(--color-sprimary) !important;
        text-align: left;
        border: none;
        box-shadow: 0 8px 32px rgba(198, 69, 249, 0.25);
    }

        .welcome-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 16px 48px rgba(198, 69, 249, 0.35);
        }

        .welcome-card h1 {
            font-family: var(--font-header);
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: -0.5px;
        }

        .welcome-card p {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .section-title {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .section-title i {
            color: var(--color-primary);
            background: none;
            -webkit-text-fill-color: var(--color-primary);
        }

        .dashboard-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .activity-chart {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .chart-item {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 24px;
            text-align: center;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            flex-direction: column;
            min-height: 280px;
        }

        .chart-item:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .chart-item h4 {
            font-family: var(--font-header);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--color-text-primary);
        }

        .chart-item .metric {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 16px 0;
        }

        .chart-item p {
            margin-top: auto;
            font-size: 0.95rem;
            color: var(--color-text-secondary);
            font-weight: 500;
        }

        /* Prompts Section Styles */
        .prompts-toolbar {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 24px;
        }

        .prompts-search {
            position: relative;
            flex: 1;
            min-width: 250px;
        }

        .prompts-search i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--color-text-muted);
        }

        .prompts-search input {
            width: 100%;
            padding: 12px 16px 12px 48px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-family: var(--font-body);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .prompts-search input:focus {
            outline: none;
            border-color: var(--color-primary);
            background: var(--glass-bg-hover);
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1);
        }

        .prompts-filter {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-pill);
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            border-color: var(--color-primary);
        }

        .prompts-list,
        .history-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 20px;
        }

        .prompt-card,
        .history-card {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 24px;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .prompt-card:hover,
        .history-card:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .prompt-card-header,
        .history-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .prompt-card-header h3,
        .history-card-header h3 {
            font-family: var(--font-header);
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--color-text-primary);
            margin: 0;
        }

        .history-date {
            font-size: 0.85rem;
            color: var(--color-text-muted);
            background: var(--glass-bg-toolbar);
            padding: 4px 12px;
            border-radius: var(--border-radius-pill);
        }

        .prompt-card-meta,
        .history-card-meta {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .prompt-card-meta span,
        .history-card-meta span {
            font-size: 0.8rem;
            color: var(--color-text-muted);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .prompt-card p,
        .history-card p {
            color: var(--color-text-secondary);
            line-height: 1.6;
            flex: 1;
        }

        .prompt-card-actions,
        .history-card-actions {
            display: flex;
            gap: 10px;
            margin-top: auto;
        }

        .action-btn {
            padding: 8px 16px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-size: 0.85rem;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn:hover {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            border-color: var(--color-primary);
            transform: translateY(-2px);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            border: none;
        }

        .action-btn.primary:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3);
        }

        /* Playground Styles */
        .playground-editor {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 24px;
            box-shadow: var(--glass-shadow);
        }

        .editor-header {
            margin-bottom: 20px;
        }

        .editor-header h3 {
            font-family: var(--font-header);
            font-size: 1.4rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
        }

        .editor-textarea {
            width: 100%;
            min-height: 300px;
            padding: 20px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(16px);
            color: var(--color-text-primary);
            font-family: var(--font-body);
            font-size: 1rem;
            line-height: 1.6;
            resize: vertical;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .editor-textarea:focus {
            outline: none;
            border-color: var(--color-primary);
            background: var(--glass-bg-hover);
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1);
        }

        .editor-actions {
            display: flex;
            gap: 12px;
            margin-top: 20px;
        }

        .editor-btn {
            padding: 12px 24px;
            border-radius: var(--border-radius-element);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .editor-btn.run {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            border: none;
        }

        .editor-btn.run:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3);
        }

        .editor-btn.clear {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: var(--color-text-primary);
        }

        .editor-btn.clear:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            border-color: var(--color-primary);
        }

        /* Settings Styles */
        .settings-section {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 30px;
            box-shadow: var(--glass-shadow);
            margin-bottom: 24px;
        }

        .settings-section h3 {
            font-family: var(--font-header);
            font-size: 1.4rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-family: var(--font-body);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--color-primary);
            background: var(--glass-bg-hover);
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1);
        }

        .settings-actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        .settings-btn {
            padding: 12px 24px;
            border-radius: var(--border-radius-element);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-btn.save {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            border: none;
        }

        .settings-btn.save:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3);
        }

        .settings-btn.cancel {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: var(--color-text-primary);
        }

        .settings-btn.cancel:hover {
            background: var(--glass-bg-hover);
            color: var(--color-text-muted);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .activity-chart {
                grid-template-columns: repeat(2, 1fr);
            }
        }

        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 260px;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .menu-toggle {
                display: block;
            }

            .header {
                padding: 16px 20px;
            }

            .header-title {
                font-size: 1.4rem;
            }

            .welcome-card h1 {
                font-size: 1.8rem;
            }

            .activity-chart {
                grid-template-columns: 1fr;
            }

            .prompts-toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .prompts-search {
                min-width: auto;
            }

            .prompts-filter {
                justify-content: center;
            }

            .prompts-list,
            .history-list {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 480px) {
            .main-content {
                padding: 15px;
            }

            .header {
                padding: 12px 15px;
            }

            .card {
                padding: 20px;
            }

            .welcome-card h1 {
                font-size: 1.5rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .new-prompt-btn {
                padding: 12px 20px;
                font-size: 0.85rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(198, 69, 249, 0.3);
            border-radius: 50%;
            border-top-color: var(--color-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg-strong);
            backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 16px 24px;
            box-shadow: var(--glass-hover-shadow);
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid #10B981;
        }

        .notification.error {
            border-left: 4px solid #EF4444;
        }

        .notification.info {
            border-left: 4px solid var(--color-primary);
        }
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-logo">allhub</div>
            
            <div class="sidebar-nav">
                <a href="#" data-view="dashboard" class="nav-link active">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
                <a href="#" data-view="prompts" class="nav-link">
                    <i class="fas fa-lightbulb"></i>
                    My Prompts
                </a>
                <a href="#" data-view="history" class="nav-link">
                    <i class="fas fa-history"></i>
                    History
                </a>
                <a href="#" data-view="playground" class="nav-link">
                    <i class="fas fa-code"></i>
                    Playground
                </a>
                <a href="#" data-view="settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>

            <div class="sidebar-footer">
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <div class="user-avatar">JD</div>
                        <span>John Doe</span>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user"></i> Profile</a>
                        <a href="#"><i class="fas fa-bell"></i> Notifications</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="header-title" id="headerTitle">Dashboard</h1>
                </div>
                <div class="header-actions">
                    <button class="new-prompt-btn" id="newPromptBtn">
                        <i class="fas fa-plus"></i>
                        New Prompt
                    </button>
                </div>
            </header>

            <!-- Dashboard View -->
            <div id="dashboard" class="content-view active">
                <div class="card welcome-card">
                    <h1>Welcome back, John!</h1>
                    <p>Ready to create something amazing today?</p>
                </div>

                <div class="dashboard-content">

                    
                    <div class="activity-chart">
                        <div class="chart-item">
                            <h4>Total Prompts</h4>
                            <div class="metric">247</div>
                            <p>+12% from last month</p>
                        </div>
                        <div class="chart-item">
                            <h4>Active Projects</h4>
                            <div class="metric">8</div>
                            <p>3 completed this week</p>
                        </div>
                        <div class="chart-item">
                            <h4>Success Rate</h4>
                            <div class="metric">94%</div>
                            <p>Above average performance</p>
                        </div>
                        <div class="chart-item">
                            <h4>Time Saved</h4>
                            <div class="metric">156h</div>
                            <p>This month</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prompts View -->
            <div id="prompts" class="content-view">
                <div class="prompts-toolbar">
                    <div class="prompts-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search prompts..." id="promptSearch">
                    </div>
                    <div class="prompts-filter">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="favorites">Favorites</button>
                        <button class="filter-btn" data-filter="recent">Recent</button>
                    </div>
                </div>

                <div class="prompts-list" id="promptsList">
                    <div class="prompt-card">
                        <div class="prompt-card-header">
                            <h3>Creative Writing Assistant</h3>
                        </div>
                        <div class="prompt-card-meta">
                            <span><i class="fas fa-star"></i> 4.8</span>
                            <span><i class="fas fa-clock"></i> 2 days ago</span>
                            <span><i class="fas fa-tag"></i> Writing</span>
                        </div>
                        <p>A comprehensive prompt for generating creative stories, helping with character development, plot structure, and narrative flow.</p>
                        <div class="prompt-card-actions">
                            <button class="action-btn primary">Use Prompt</button>
                            <button class="action-btn"><i class="fas fa-edit"></i> Edit</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-card-header">
                            <h3>Code Review Helper</h3>
                        </div>
                        <div class="prompt-card-meta">
                            <span><i class="fas fa-star"></i> 4.9</span>
                            <span><i class="fas fa-clock"></i> 1 week ago</span>
                            <span><i class="fas fa-tag"></i> Development</span>
                        </div>
                        <p>Analyze code quality, suggest improvements, and provide detailed feedback on programming best practices.</p>
                        <div class="prompt-card-actions">
                            <button class="action-btn primary">Use Prompt</button>
                            <button class="action-btn"><i class="fas fa-edit"></i> Edit</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>

                    <div class="prompt-card">
                        <div class="prompt-card-header">
                            <h3>Marketing Copy Generator</h3>
                        </div>
                        <div class="prompt-card-meta">
                            <span><i class="fas fa-star"></i> 4.7</span>
                            <span><i class="fas fa-clock"></i> 3 days ago</span>
                            <span><i class="fas fa-tag"></i> Marketing</span>
                        </div>
                        <p>Create compelling marketing copy for various channels including social media, email campaigns, and website content.</p>
                        <div class="prompt-card-actions">
                            <button class="action-btn primary">Use Prompt</button>
                            <button class="action-btn"><i class="fas fa-edit"></i> Edit</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History View -->
            <div id="history" class="content-view">

                <div class="history-list">
                    <div class="history-card">
                        <div class="history-card-header">
                            <h3>Product Launch Strategy</h3>
                            <span class="history-date">Today</span>
                        </div>
                        <div class="history-card-meta">
                            <span><i class="fas fa-message"></i> 23 messages</span>
                            <span><i class="fas fa-clock"></i> 45 min</span>
                        </div>
                        <p>Developed a comprehensive go-to-market strategy for a new SaaS product, including pricing models and customer acquisition channels.</p>
                        <div class="history-card-actions">
                            <button class="action-btn primary">Continue</button>
                            <button class="action-btn"><i class="fas fa-download"></i> Export</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>

                    <div class="history-card">
                        <div class="history-card-header">
                            <h3>Code Optimization Review</h3>
                            <span class="history-date">Yesterday</span>
                        </div>
                        <div class="history-card-meta">
                            <span><i class="fas fa-message"></i> 15 messages</span>
                            <span><i class="fas fa-clock"></i> 30 min</span>
                        </div>
                        <p>Analyzed React component performance issues and implemented optimization strategies resulting in 40% faster load times.</p>
                        <div class="history-card-actions">
                            <button class="action-btn primary">Continue</button>
                            <button class="action-btn"><i class="fas fa-download"></i> Export</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>

                    <div class="history-card">
                        <div class="history-card-header">
                            <h3>Creative Writing Session</h3>
                            <span class="history-date">2 days ago</span>
                        </div>
                        <div class="history-card-meta">
                            <span><i class="fas fa-message"></i> 31 messages</span>
                            <span><i class="fas fa-clock"></i> 1.2 hours</span>
                        </div>
                        <p>Collaborative story writing session developing characters and plot for a science fiction novel set in 2150.</p>
                        <div class="history-card-actions">
                            <button class="action-btn primary">Continue</button>
                            <button class="action-btn"><i class="fas fa-download"></i> Export</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Playground View -->
            <div id="playground" class="content-view">

                <div class="playground-editor">
                    <div class="editor-header">
                        <h3>Test Your Prompts</h3>
                    </div>
                    <textarea class="editor-textarea" id="playgroundTextarea" placeholder="Enter your prompt here to test it...">You are a helpful AI assistant. Please help me write a compelling product description for a new smartwatch that focuses on health and fitness tracking. The target audience is health-conscious professionals aged 25-45.

Key features to highlight:
- Advanced heart rate monitoring
- Sleep quality tracking
- Stress level detection
- 7-day battery life
- Water resistant to 50m
- Integration with popular fitness apps

Please make the description engaging and highlight the benefits, not just the features.</textarea>
                    <div class="editor-actions">
                        <button class="editor-btn run" id="runPrompt">
                            <i class="fas fa-play"></i>
                            Run Prompt
                        </button>
                        <button class="editor-btn clear" id="clearEditor">
                            <i class="fas fa-trash"></i>
                            Clear
                        </button>
                    </div>
                </div>
            </div>

            <!-- Settings View -->
            <div id="settings" class="content-view">


                <div class="settings-section">
                    <h3>Profile Settings</h3>
                    <div class="form-group">
                        <label for="fullName">Full Name</label>
                        <input type="text" id="fullName" value="John Doe">
                    </div>
                    <div class="form-group">
                        <label for="email">Email Address</label>
                        <input type="email" id="email" value="<EMAIL>">
                    </div>
                    <div class="form-group">
                        <label for="company">Company</label>
                        <input type="text" id="company" value="Acme Corp">
                    </div>
                    <div class="settings-actions">
                        <button class="settings-btn save">
                            <i class="fas fa-save"></i>
                            Save Changes
                        </button>
                        <button class="settings-btn cancel">
                            <i class="fas fa-times"></i>
                            Cancel
                        </button>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>API Configuration</h3>
                    <div class="form-group">
                        <label for="apiKey">API Key</label>
                        <input type="password" id="apiKey" value="sk-...">
                    </div>
                    <div class="form-group">
                        <label for="model">Default Model</label>
                        <input type="text" id="model" value="gpt-4">
                    </div>
                    <div class="settings-actions">
                        <button class="settings-btn save">
                            <i class="fas fa-save"></i>
                            Save API Settings
                        </button>
                        <button class="settings-btn cancel">
                            <i class="fas fa-times"></i>
                            Reset
                        </button>
                    </div>
                </div>

                <div class="settings-section">
                    <h3>Preferences</h3>
                    <div class="form-group">
                        <label for="theme">Theme</label>
                        <input type="text" id="theme" value="Glass Morphism" readonly>
                    </div>
                    <div class="form-group">
                        <label for="language">Language</label>
                        <input type="text" id="language" value="English">
                    </div>
                    <div class="settings-actions">
                        <button class="settings-btn save">
                            <i class="fas fa-save"></i>
                            Save Preferences
                        </button>
                        <button class="settings-btn cancel">
                            <i class="fas fa-times"></i>
                            Reset
                        </button>
                    </div>
                </div>
            </div>
        </main>
    </div>

    <script>
        // Navigation functionality
        const navLinks = document.querySelectorAll('.nav-link');
        const contentViews = document.querySelectorAll('.content-view');
        const headerTitle = document.getElementById('headerTitle');
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');

        // View titles mapping
        const viewTitles = {
            dashboard: 'Dashboard',
            prompts: 'My Prompts',
            history: 'Conversation History',
            playground: 'Prompt Playground',
            settings: 'Settings'
        };

        // Handle navigation
        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetView = link.getAttribute('data-view');
                
                // Update active nav link
                navLinks.forEach(nav => nav.classList.remove('active'));
                link.classList.add('active');
                
                // Update content view
                contentViews.forEach(view => view.classList.remove('active'));
                document.getElementById(targetView).classList.add('active');
                
                // Update header title
                headerTitle.textContent = viewTitles[targetView];
                
                // Close sidebar on mobile
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        // Mobile menu toggle
        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        // User menu dropdown
        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('visible');
        });

        // Close dropdown when clicking outside
        document.addEventListener('click', () => {
            userDropdown.classList.remove('visible');
        });

        // Prevent dropdown from closing when clicking inside
        userDropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        // Filter functionality for prompts
        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                
                const filter = btn.getAttribute('data-filter');
                // Here you would implement actual filtering logic
                showNotification(`Filtered by: ${filter}`, 'info');
            });
        });

        // Search functionality
        const promptSearch = document.getElementById('promptSearch');
        if (promptSearch) {
            promptSearch.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                // Here you would implement actual search logic
                console.log('Searching for:', searchTerm);
            });
        }

        // Playground functionality
        const playgroundTextarea = document.getElementById('playgroundTextarea');
        const runPromptBtn = document.getElementById('runPrompt');
        const clearEditorBtn = document.getElementById('clearEditor');

        if (runPromptBtn) {
            runPromptBtn.addEventListener('click', () => {
                const prompt = playgroundTextarea.value.trim();
                if (!prompt) {
                    showNotification('Please enter a prompt first', 'error');
                    return;
                }
                
                // Show loading state
                runPromptBtn.innerHTML = '<div class="loading"></div> Running...';
                runPromptBtn.disabled = true;
                
                // Simulate API call
                setTimeout(() => {
                    runPromptBtn.innerHTML = '<i class="fas fa-play"></i> Run Prompt';
                    runPromptBtn.disabled = false;
                    showNotification('Prompt executed successfully!', 'success');
                }, 2000);
            });
        }

        if (clearEditorBtn) {
            clearEditorBtn.addEventListener('click', () => {
                playgroundTextarea.value = '';
                showNotification('Editor cleared', 'info');
            });
        }

        // Settings functionality
        const saveButtons = document.querySelectorAll('.settings-btn.save');
        saveButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                // Show loading state
                const originalText = btn.innerHTML;
                btn.innerHTML = '<div class="loading"></div> Saving...';
                btn.disabled = true;
                
                // Simulate save operation
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                    showNotification('Settings saved successfully!', 'success');
                }, 1500);
            });
        });

        // New prompt button
        const newPromptBtn = document.getElementById('newPromptBtn');
        if (newPromptBtn) {
            newPromptBtn.addEventListener('click', () => {
                // Switch to playground view
                navLinks.forEach(nav => nav.classList.remove('active'));
                document.querySelector('[data-view="playground"]').classList.add('active');
                
                contentViews.forEach(view => view.classList.remove('active'));
                document.getElementById('playground').classList.add('active');
                
                headerTitle.textContent = 'Prompt Playground';
                
                // Clear and focus textarea
                if (playgroundTextarea) {
                    playgroundTextarea.value = '';
                    playgroundTextarea.focus();
                }
                
                showNotification('Ready to create a new prompt!', 'info');
            });
        }

        // Action buttons functionality
        document.addEventListener('click', (e) => {
            if (e.target.classList.contains('action-btn')) {
                const btnText = e.target.textContent.trim();
                
                if (btnText.includes('Use Prompt') || btnText.includes('Continue')) {
                    showNotification('Opening prompt in playground...', 'info');
                    // Switch to playground and populate with prompt
                    setTimeout(() => {
                        document.querySelector('[data-view="playground"]').click();
                    }, 500);
                } else if (btnText.includes('Edit')) {
                    showNotification('Opening prompt editor...', 'info');
                } else if (btnText.includes('Share')) {
                    showNotification('Sharing prompt...', 'info');
                } else if (btnText.includes('Export')) {
                    showNotification('Exporting conversation...', 'info');
                }
            }
        });

        // Notification system
        function showNotification(message, type = 'info') {
            // Remove existing notifications
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notif => notif.remove());
            
            // Create new notification
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            
            document.body.appendChild(notification);
            
            // Show notification
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            
            // Hide notification after 3 seconds
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        // Handle window resize
        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        // Initialize
        document.addEventListener('DOMContentLoaded', () => {
            showNotification('Welcome to AllHub! 🚀', 'success');
        });
    </script>
</body>
</html>