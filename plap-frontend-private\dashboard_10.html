<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>allhub - Private Area</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Fuentes del HTML base (allhub) -->
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* ESTILOS DEL HTML BASE (allhub - Private Area) */
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            --color-tertiary: #F9E7FE;
            --color-quaternary: #ffffff;
            --color-primary: #C645F9;
            --color-secondary: #000d83;
            --color-dark: #000d83; /* En el HTML nuevo, --color-dark es #0D0425, aquí se mantiene el de allhub */
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.12);
            --glass-bg-hover: rgba(255, 255, 255, 0.2);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.25);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.15);
            --glass-hover-shadow: 0 16px 48px rgba(13, 4, 37, 0.25);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 65%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 45%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.15);
            --color-active-accent: rgba(94, 108, 231, 0.2);
            --border-radius-card: 24px;
            --border-radius-pill: 50px;
            --border-radius-element: 16px;
            --transition-speed: 0.3s;
            --transition-ease: cubic-bezier(0.4, 0.0, 0.2, 1);
            --sidebar-width: 280px;
            --header-height: 80px;
            --user-menu-bg: linear-gradient(135deg, #C645F9, #000d83);

            /* Variables adicionales del CSS del playground (Prompt like a Pro) */
            --glass-border-highlight: rgba(255, 255, 255, 0.3); /* Añadido de Prompt like a pro */
            --color-star-active: #FFD700; /* Añadido de Prompt like a pro */
            --color-placeholder: var(--color-text-muted); /* Ya definido, se mantiene */
            --color-icon-gray: var(--color-text-secondary); /* Ya definido, se mantiene */
            --google-blue: #4285F4;
            --google-green: #34A853;
            --google-red: #EA4335;
            --star-black: #2c3e50;
            --star-orange: #f39c12;
            --star-blue-gemini: #4A88DA;
            --star-darkgray: #7f8c8d;
            --star-skyblue: #3498db;
        }

        * { 
            margin: 0; 
            padding: 0; 
            box-sizing: border-box; 
        }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--color-text-primary);
            /* Fondo del HTML base (allhub) */
            background: linear-gradient(135deg, #F5F5F5 0%, #E8E8F0 30%, #DDD9E8 70%, #D5D5D5 100%);
            background-attachment: fixed;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .app-container {
            display: flex;
            min-height: 100vh;
        }

        .sidebar {
            width: var(--sidebar-width);
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border-right: 1px solid var(--glass-border);
            padding: 30px 20px;
            position: fixed;
            height: 100%;
            z-index: 1000;
            display: flex;
            flex-direction: column;
            box-shadow: var(--glass-shadow);
            transition: transform var(--transition-speed) var(--transition-ease);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            text-align: center;
            margin-bottom: 30px;
            letter-spacing: -0.5px;
        }

        .sidebar-nav {
            flex-grow: 1;
            display: flex;
            flex-direction: column;
            gap: 8px;
        }

        .sidebar-nav a {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 16px 20px;
            font-size: 0.95rem;
            font-weight: 500;
            color: var(--color-text-primary);
            text-decoration: none;
            border-radius: var(--border-radius-element);
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
            overflow: hidden;
            backdrop-filter: blur(8px);
            cursor: pointer;
        }

        .sidebar-nav a::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent);
            transition: left 0.6s ease;
        }

        .sidebar-nav a:hover::before { 
            left: 100%; 
        }

        .sidebar-nav a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(6px);
            box-shadow: var(--glass-hover-shadow);
        }

        .sidebar-nav a.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            font-weight: 600;
            box-shadow: var(--glass-shadow);
        }

        .sidebar-nav a i {
            font-size: 1.1rem;
            color: var(--color-text-secondary);
            transition: color var(--transition-speed) var(--transition-ease);
        }

        .sidebar-nav a.active i,
        .sidebar-nav a:hover i {
            color: var(--color-primary);
        }

        .sidebar-footer {
            margin-top: auto;
            padding-top: 20px;
        }

        .user-menu {
            position: relative;
            margin-bottom: 20px;
        }

        .user-menu-btn {
            background: var(--user-menu-bg);
            border: none;
            border-radius: var(--border-radius-pill);
            padding: 12px 18px;
            display: flex;
            align-items: center;
            gap: 12px;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            width: 100%;
            box-shadow: var(--glass-shadow);
        }

        .user-menu-btn:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: var(--glass-hover-shadow);
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            border-radius: 50%;
            background: rgba(255, 255, 255, 0.2);
            display: flex;
            align-items: center;
            justify-content: center;
            color: var(--color-text-on-gradient);
            font-weight: 700;
            font-size: 0.9rem;
            border: 2px solid rgba(255, 255, 255, 0.3);
        }

        .user-dropdown {
            position: absolute;
            bottom: calc(100% + 12px);
            left: 0;
            background: var(--glass-bg-strong);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: var(--glass-hover-shadow);
            width: 220px;
            padding: 12px 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(10px);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1000;
        }

        .user-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .user-dropdown a {
            display: flex;
            align-items: center;
            padding: 12px 18px;
            color: var(--color-text-primary);
            text-decoration: none;
            font-size: 0.9rem;
            font-weight: 500;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .user-dropdown a:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
            transform: translateX(6px);
        }

        .user-dropdown a i {
            margin-right: 12px;
            color: var(--color-text-muted);
            width: 16px;
            text-align: center;
        }

        .user-dropdown .divider {
            height: 1px;
            background: var(--glass-border);
            margin: 8px 0;
        }

        .main-content {
            margin-left: var(--sidebar-width);
            flex-grow: 1;
            padding: 30px;
            display: flex;
            flex-direction: column;
            gap: 24px;
        }

        .header {
            background: linear-gradient(135deg, var(--color-tertiary), var(--color-quaternary)) !important;
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 20px 30px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            box-shadow: var(--glass-shadow);
            position: sticky;
            top: 0;
            z-index: 900;
        }

        .header-left {
            display: flex;
            align-items: center;
            gap: 18px;
        }

        .menu-toggle {
            display: none;
            font-size: 1.5rem;
            color: var(--color-text-primary);
            cursor: pointer;
            background: none;
            border: none;
            padding: 8px;
            border-radius: 8px;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .menu-toggle:hover {
            background: var(--glass-bg-hover);
            color: var(--color-primary);
        }

        .header-title {
            font-family: var(--font-header);
            font-size: 1.7rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .header-actions {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .new-prompt-btn {
            padding: 14px 28px;
            border-radius: var(--border-radius-pill);
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            font-weight: 600;
            font-size: 0.9rem;
            border: none;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 10px;
            box-shadow: 0 4px 20px rgba(198, 69, 249, 0.3);
        }

        .new-prompt-btn:hover {
            transform: translateY(-3px) scale(1.05);
            box-shadow: 0 12px 40px rgba(198, 69, 249, 0.4);
        }

        .content-view {
            display: none;
            animation: fadeIn 0.6s var(--transition-ease);
        }

        .content-view.active {
            display: block;
        }

        @keyframes fadeIn {
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            }
            to { 
                opacity: 1; 
                transform: translateY(0); 
            }
        }

        .card {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 30px;
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .card:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .welcome-card {
        background: var(--glass-bg);
        color: var(--color-sprimary) !important;
        text-align: left;
        border: none;
        box-shadow: 0 8px 32px rgba(198, 69, 249, 0.25);
    }

        .welcome-card:hover {
            transform: translateY(-6px) scale(1.02);
            box-shadow: 0 16px 48px rgba(198, 69, 249, 0.35);
        }

        .welcome-card h1 {
            font-family: var(--font-header);
            font-size: 2.2rem;
            font-weight: 700;
            margin-bottom: 12px;
            letter-spacing: -0.5px;
        }

        .welcome-card p {
            font-size: 1.1rem;
            opacity: 0.9;
            font-weight: 400;
        }

        .section-title { /* Estilo general de sección */
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 600;
            margin-bottom: 24px;
            display: flex;
            align-items: center;
            gap: 12px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            letter-spacing: -0.5px;
        }

        .section-title i {
            color: var(--color-primary);
            background: none;
            -webkit-text-fill-color: var(--color-primary);
        }

        .dashboard-content {
            display: flex;
            flex-direction: column;
            gap: 30px;
        }

        .activity-chart {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .chart-item {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 24px;
            text-align: center;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            flex-direction: column;
            min-height: 320px; /* Adjusted for charts */
        }

        .chart-item:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .chart-item h4 { /* Este H4 es de AG Charts, los de ECharts no lo usan */
            font-family: var(--font-header);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--color-text-primary);
        }
        
        .chart-item > div[id^="chart"], 
        .chart-item > div[id$="Chart"] {
            flex-grow: 1; 
            min-height: 200px; 
            width: 100%;
        }

        .chart-item .metric {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 16px 0;
        }

        .chart-item p {
            margin-top: auto;
            font-size: 0.95rem;
            color: var(--color-text-secondary);
            font-weight: 500;
            padding-top: 10px; 
        }

        /* Prompts Section Styles */
        .prompts-toolbar {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            flex-wrap: wrap;
            gap: 16px;
            margin-bottom: 24px;
        }

        .prompts-search {
            position: relative;
            flex: 1;
            min-width: 250px;
        }

        .prompts-search i {
            position: absolute;
            left: 16px;
            top: 50%;
            transform: translateY(-50%);
            color: var(--color-text-muted);
        }

        .prompts-search input {
            width: 100%;
            padding: 12px 16px 12px 48px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-family: var(--font-body);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .prompts-search input:focus {
            outline: none;
            border-color: var(--color-primary);
            background: var(--glass-bg-hover);
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1);
        }

        .prompts-filter {
            display: flex;
            gap: 8px;
        }

        .filter-btn {
            padding: 10px 20px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-pill);
            background: var(--glass-bg);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-weight: 500;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .filter-btn:hover,
        .filter-btn.active {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            border-color: var(--color-primary);
        }

        .prompts-list,
        .history-list {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr));
            gap: 20px;
        }

        .prompt-card,
        .history-card {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 24px;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            flex-direction: column;
            gap: 16px;
        }

        .prompt-card:hover,
        .history-card:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .prompt-card-header,
        .history-card-header {
            display: flex;
            justify-content: space-between;
            align-items: flex-start;
        }

        .prompt-card-header h3,
        .history-card-header h3 {
            font-family: var(--font-header);
            font-size: 1.2rem;
            font-weight: 600;
            color: var(--color-text-primary);
            margin: 0;
        }

        .history-date {
            font-size: 0.85rem;
            color: var(--color-text-muted);
            background: var(--glass-bg-toolbar);
            padding: 4px 12px;
            border-radius: var(--border-radius-pill);
        }

        .prompt-card-meta,
        .history-card-meta {
            display: flex;
            gap: 16px;
            flex-wrap: wrap;
        }

        .prompt-card-meta span,
        .history-card-meta span {
            font-size: 0.8rem;
            color: var(--color-text-muted);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .prompt-card p,
        .history-card p {
            color: var(--color-text-secondary);
            line-height: 1.6;
            flex: 1;
        }

        .prompt-card-actions,
        .history-card-actions {
            display: flex;
            gap: 10px;
            margin-top: auto;
        }

        .action-btn {
            padding: 8px 16px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-size: 0.85rem;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .action-btn:hover {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            border-color: var(--color-primary);
            transform: translateY(-2px);
        }

        .action-btn.primary {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            border: none;
        }

        .action-btn.primary:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3);
        }

        /* Settings Styles */
        .settings-section {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 30px;
            box-shadow: var(--glass-shadow);
            margin-bottom: 24px;
        }

        .settings-section h3 {
            font-family: var(--font-header);
            font-size: 1.4rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 24px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: var(--color-text-primary);
        }

        .form-group input {
            width: 100%;
            padding: 12px 16px;
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px);
            color: var(--color-text-primary);
            font-family: var(--font-body);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .form-group input:focus {
            outline: none;
            border-color: var(--color-primary);
            background: var(--glass-bg-hover);
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1);
        }

        .settings-actions {
            display: flex;
            gap: 12px;
            margin-top: 24px;
        }

        .settings-btn {
            padding: 12px 24px;
            border-radius: var(--border-radius-element);
            font-weight: 600;
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .settings-btn.save {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: white;
            border: none;
        }

        .settings-btn.save:hover {
            transform: translateY(-2px) scale(1.05);
            box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3);
        }

        .settings-btn.cancel {
            background: var(--glass-bg);
            border: 1px solid var(--glass-border);
            color: var(--color-text-primary);
        }

        .settings-btn.cancel:hover {
            background: var(--glass-bg-hover);
            color: var(--color-text-muted);
        }

        /* Responsive Design */
        @media (max-width: 1200px) {
            .activity-chart {
                grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); 
            }
        }

        @media (max-width: 992px) { 
             .activity-chart {
                grid-template-columns: repeat(auto-fit, minmax(260px, 1fr));
            }
        }


        @media (max-width: 768px) {
            .sidebar {
                transform: translateX(-100%);
                width: 260px;
            }

            .sidebar.active {
                transform: translateX(0);
            }

            .main-content {
                margin-left: 0;
                padding: 20px;
            }

            .menu-toggle {
                display: block;
            }

            .header {
                padding: 16px 20px;
            }

            .header-title {
                font-size: 1.4rem;
            }

            .welcome-card h1 {
                font-size: 1.8rem;
            }

            .activity-chart {
                grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); 
            }

            .prompts-toolbar {
                flex-direction: column;
                align-items: stretch;
            }

            .prompts-search {
                min-width: auto;
            }

            .prompts-filter {
                justify-content: center;
            }

            .prompts-list,
            .history-list {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 576px) { 
             .activity-chart {
                grid-template-columns: 1fr;
            }
        }


        @media (max-width: 480px) {
            .main-content {
                padding: 15px;
            }

            .header {
                padding: 12px 15px;
            }

            .card {
                padding: 20px;
            }

            .welcome-card h1 {
                font-size: 1.5rem;
            }

            .section-title {
                font-size: 1.5rem;
            }

            .new-prompt-btn {
                padding: 12px 20px;
                font-size: 0.85rem;
            }
        }

        /* Loading Animation */
        .loading {
            display: inline-block;
            width: 20px;
            height: 20px;
            border: 3px solid rgba(198, 69, 249, 0.3);
            border-radius: 50%;
            border-top-color: var(--color-primary);
            animation: spin 1s ease-in-out infinite;
        }

        @keyframes spin {
            to { transform: rotate(360deg); }
        }

        /* Notification Styles */
        .notification {
            position: fixed;
            top: 20px;
            right: 20px;
            background: var(--glass-bg-strong);
            backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 16px 24px;
            box-shadow: var(--glass-hover-shadow);
            z-index: 10000;
            opacity: 0;
            transform: translateX(100%);
            transition: all var(--transition-speed) var(--transition-ease);
        }

        .notification.show {
            opacity: 1;
            transform: translateX(0);
        }

        .notification.success {
            border-left: 4px solid #10B981;
        }

        .notification.error {
            border-left: 4px solid #EF4444;
        }

        .notification.info {
            border-left: 4px solid var(--color-primary);
        }

        /* AG Charts Tooltip Glassmorphism */
        .ag-chart-tooltip-glass {
            background: var(--glass-bg-strong) !important;
            backdrop-filter: blur(10px) !important;
            -webkit-backdrop-filter: blur(10px) !important;
            border: 1px solid var(--glass-border) !important;
            border-radius: var(--border-radius-element) !important;
            box-shadow: var(--glass-shadow) !important;
            color: var(--color-text-primary) !important;
            font-family: var(--font-body) !important;
            padding: 10px 15px !important;
        }
        .ag-chart-tooltip-glass .ag-chart-tooltip-title {
            color: var(--color-primary) !important;
            font-weight: 600 !important;
            margin-bottom: 6px !important;
            font-size: 0.9rem !important;
        }
        .ag-chart-tooltip-glass .ag-chart-tooltip-content {
            color: var(--color-text-secondary) !important;
            font-size: 0.85rem !important;
        }

        /* === ESTILOS DEL NUEVO PLAYGROUND (Prompt like a Pro) === */
        /* Ajuste para que el main-container no tenga el padding superior original, ya está dentro de .main-content */
        #playground .main-container {
            padding: 20px; /* Padding reducido, o el que se ajuste mejor al layout */
            width: 100%;
            min-height: auto; /* Para que no fuerce 100vh dentro del content-view */
            display: flex;
            flex-direction: column;
            align-items: center; /* Esto centrará el contenido del playground si no ocupa todo el ancho */
        }

        /* Ajuste del header-section para que no tenga tanto margen si el main-container ya está "contenido" */
        #playground .header-section { 
            text-align: center; 
            margin-bottom: 30px; /* Margen reducido */
            z-index: 5; /* Mantener por si acaso */
        }

        @keyframes synchronizedNeonShine {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        #playground .glass-title {
            font-family: var(--font-header);
            font-size: clamp(1.8rem, 4vw, 2.5rem); /* Ajustado para integrarse mejor */
            font-weight: 600;
            margin-bottom: 10px;
            background: linear-gradient(135deg, 
                var(--color-primary), 
                var(--color-secondary), 
                var(--color-primary), 
                var(--color-secondary), 
                var(--color-primary)
            );
            background-size: 300% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: 1.1;
            animation: synchronizedNeonShine 5s linear infinite;
        }
        #playground .glass-subtitle {
            font-size: clamp(0.9rem, 2vw, 1rem); /* Ajustado */
            font-weight: 500; color: var(--color-text-secondary);
            max-width: 550px; margin: 0 auto; line-height: 1.6;
        }

        #playground .glass-search-container { width: 100%; max-width: 800px; position: relative; z-index: 10; } /* Max-width aumentado un poco */
        #playground .glass-search-card {
            background: var(--glass-bg); /* Usar variables ya definidas en allhub si es posible */
            backdrop-filter: blur(22px); -webkit-backdrop-filter: blur(22px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card); box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
            overflow: visible; position: relative;
        }
        #playground .glass-search-card::before {
            content: ''; position: absolute; top: -1px; left: -1px; right: -1px; bottom: -1px;
            border-radius: inherit; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            padding: 1.5px;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; mask-composite: exclude;
            opacity: 0; transition: opacity var(--transition-speed) var(--transition-ease);
            pointer-events: none; z-index: -1;
        }
        #playground .glass-search-card.focused {
            transform: translateY(-3px); box-shadow: var(--glass-hover-shadow);
            border-color: transparent;
        }
        #playground .glass-search-card.focused::before { opacity: 0.7; }

        #playground .glass-search-input {
            width: 100%; background: transparent; border: none; outline: none;
            padding: 40px 26px 14px; font-size: 1rem;
            font-family: var(--font-body); font-weight: 500;
            color: var(--color-text-primary); resize: none;
            min-height: 60px; /* Aumentado un poco */
            line-height: 1.5;
        }
        #playground .glass-search-input::placeholder { color: var(--color-placeholder); font-weight: 400; }

        #playground .glass-toolbar {
            display: flex; justify-content: space-between; align-items: center;
            padding: 12px 22px; background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
        }
        #playground .glass-model-selector-container { position: relative; }
        #playground .glass-model-btn {
            display: flex; align-items: center; gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 9px 20px; font-size: 0.8rem; font-weight: 600;
            color: var(--color-text-primary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
        }
        #playground .glass-model-btn:hover {
            background: rgba(255, 255, 255, 0.2); border-color: var(--glass-border-highlight);
            transform: translateY(-1px); box-shadow: 0 2px 10px rgba(13,4,37, 0.08);
        }
        #playground .glass-model-btn .fa-chevron-down { transition: transform var(--transition-speed) var(--transition-ease); }
        #playground .glass-model-btn.open .fa-chevron-down { transform: rotate(180deg); }
        #playground .glass-model-btn > i:first-child { color: var(--color-secondary); font-size:0.9em; }

        #playground .glass-search-actions { display: flex; align-items: center; gap: 10px; }
        #playground .glass-action-btn {
            width: 38px; height: 38px;
            display: flex; align-items: center; justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: 50%; color: var(--color-text-secondary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            font-size: 0.95rem;
        }
        #playground .glass-action-btn:hover {
            background: var(--color-hover-accent); color: var(--color-primary);
            border-color: var(--color-primary); transform: translateY(-2px) scale(1.08);
            box-shadow: 0 4px 15px color-mix(in srgb, var(--color-primary) 20%, transparent);
        }
        #playground .glass-action-btn:active { transform: translateY(0px) scale(1); background: var(--color-active-accent); }
        #playground .pulse-effect { animation: pulseGlowPlayground 1.8s ease-in-out infinite; } /* Renombrada animación para evitar conflictos si existe otra */
        
        @keyframes pulseGlowPlayground { /* Renombrada */
            0%, 100% { box-shadow: 0 0 12px color-mix(in srgb, var(--color-primary) 15%, transparent), 0 0 4px var(--color-primary); }
            50% { box-shadow: 0 0 25px color-mix(in srgb, var(--color-primary) 35%, transparent), 0 0 8px var(--color-primary); }
        }

        #playground .glass-content-area {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
            padding: 18px; max-height: 380px;
            overflow-y: auto;
        }
        #playground .glass-content-area::-webkit-scrollbar { width: 6px; }
        #playground .glass-content-area::-webkit-scrollbar-track { background: transparent; }
        #playground .glass-content-area::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.2); border-radius: 3px;}
        #playground .glass-content-area::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.3); }

        #playground .glass-loading-indicator {
            display: flex; align-items: center; justify-content: center;
            padding: 25px; color: var(--color-text-secondary); font-size: 0.9rem;
            font-weight: 500;
        }
        #playground .glass-spinner {
            width: 20px; height: 20px;
            border: 2.5px solid color-mix(in srgb, var(--color-primary) 20%, transparent);
            border-top-color: var(--color-primary);
            border-radius: 50%; animation: spinPlayground 0.8s linear infinite; margin-right: 12px; /* Renombrada animación */
        }
        @keyframes spinPlayground { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } /* Renombrada */

        #playground .suggestions-panel { animation: fadeInUpPlayground 0.4s var(--transition-ease) forwards; } /* Renombrada */

        #playground .simple-suggestions-list { display: flex; flex-direction: column; gap: 10px; }
        #playground .simple-suggestion-item {
            background: rgba(255, 255, 255, 0.07);
            backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 14px 18px; cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            font-size: 0.9rem; color: var(--color-text-primary); line-height: 1.5;
        }
        #playground .simple-suggestion-item:hover {
            background: rgba(255, 255, 255, 0.16); border-color: var(--glass-border-highlight);
            transform: translateY(-2px); box-shadow: 0 5px 18px rgba(13,4,37, 0.07);
        }
        #playground .simple-suggestion-item strong { font-weight: 700; color: var(--color-primary); }

        #playground .enhanced-prompt-list { display: flex; flex-direction: column; gap: 16px; }
        #playground .enhanced-prompt-card {
            background: rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element); padding: 18px;
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
        }
        #playground .enhanced-prompt-card:hover {
            background: rgba(255, 255, 255, 0.12); border-color: var(--glass-border-highlight);
            transform: translateY(-3px); box-shadow: 0 10px 30px rgba(13,4,37, 0.1);
        }
        #playground .enhanced-card-header {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 10px;
        }
        #playground .enhanced-card-title {
            font-size: 1.05rem; font-weight: 600; color: var(--color-text-primary);
            line-height: 1.3;
        }
        #playground .glass-star-btn {
            background: rgba(255, 255, 255, 0.1); border: 1px solid var(--glass-border);
            border-radius: 50%; width: 30px; height: 30px;
            display: flex; align-items: center; justify-content: center;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-muted); font-size: 0.85rem;
        }
        #playground .glass-star-btn:hover {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-primary); transform: scale(1.1);
        }
        #playground .glass-star-btn.favorited {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-star-active);
            border-color: var(--color-primary);
        }
        #playground .glass-star-btn.favorited i { font-weight: 900; }

        #playground .enhanced-prompt-meta-minimal {
            display: flex; flex-wrap: wrap; gap: 6px 12px;
            margin-bottom: 10px; font-size: 0.75rem; color: var(--color-text-secondary);
        }
        #playground .meta-item { display: flex; align-items: center; gap: 5px; }
        #playground .meta-item i { font-size: 0.9em; }

        #playground .full-prompt-display {
            font-size: 0.85rem; line-height: 1.6; color: var(--color-text-secondary);
            margin-bottom: 12px; white-space: pre-wrap;
        }
        #playground .prompt-heading {
            font-weight: 600; color: var(--color-text-primary); display: block; margin-top: 8px;
        }
        #playground .full-prompt-display br + .prompt-heading { margin-top: 8px; }

        #playground .select-prompt-button {
            display: inline-flex; align-items: center; gap: 8px;
            padding: 8px 16px; border-radius: var(--border-radius-pill);
            font-family: var(--font-body); font-size: 0.8rem; font-weight: 600;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            text-decoration: none; border: none;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            margin-top: 8px;
        }
        #playground .select-prompt-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }
        #playground .select-prompt-button i { font-size: 0.9em; }

        #playground .popular-prompts-section-title {
            color: var(--color-text-primary); margin-bottom: 14px;
            display: flex; align-items: center; gap: 8px;
            font-weight: 600; font-size: 1.05rem;
        }
        #playground .popular-prompts-section-title i { color: var(--color-primary); }

        #playground .glass-model-dropdown {
            position: absolute; top: calc(100% + 10px); left: 0;
            width: auto; min-width: 280px; max-width: 350px;
            background: var(--glass-bg); /* Usar var de allhub */
            backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: 0 10px 30px rgba(13,4,37,0.1);
            opacity: 0; visibility: hidden;
            transform: translateY(-8px) scale(0.98);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1100; /* Z-index alto para superponerse */
            padding: 8px;
        }
        #playground .glass-model-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0) scale(1); }
        
        #playground .model-search-input-container {
            position: relative; margin-bottom: 15px;
        }
        #playground .model-search-input-container .fa-search {
            position: absolute; top: 50%; left: 12px; transform: translateY(-50%);
            color: var(--color-text-muted); font-size: 0.9em;
        }
        #playground #modelSearchInput { /* ID específico */
            width: 100%;
            padding: 10px 12px 10px 34px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);
            border: 1px solid var(--glass-border);
            border-radius: calc(var(--border-radius-element) - 4px);
            font-family: var(--font-body); font-size: 0.85rem;
            color: var(--color-text-primary); outline: none;
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
        #playground #modelSearchInput::placeholder { color: var(--color-placeholder); }
        #playground #modelSearchInput:focus { border-color: var(--glass-border-highlight); }

        #playground .model-list-ul {
            list-style: none; padding: 0; margin: 0;
            max-height: 250px; overflow-y: auto;
        }
        #playground .model-list-ul::-webkit-scrollbar { width: 5px; }
        #playground .model-list-ul::-webkit-scrollbar-track { background: transparent; }
        #playground .model-list-ul::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.25); border-radius: 3px;}
        #playground .model-list-ul::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.35); }

        #playground .model-list-ul li {
            padding: 10px 14px;
            color: var(--color-text-primary); font-weight: 500; font-size: 0.85rem;
            cursor: pointer; transition: background-color var(--transition-speed) var(--transition-ease);
            border-radius: calc(var(--border-radius-element) - 6px);
            display: flex; align-items: center; gap: 10px;
            margin-bottom: 4px;
        }
        #playground .model-list-ul li:last-child { margin-bottom: 0; }
        #playground .model-list-ul li:hover { background: var(--color-hover-accent); color: var(--color-primary); }
        #playground .model-list-ul li.selected-model-item {
            background-color: var(--color-active-accent);
            color: var(--color-primary); font-weight: 600;
        }
        #playground .model-list-ul li img, #playground .model-list-ul li .model-icon {
            width: 18px; height: 18px; object-fit: contain; flex-shrink: 0;
        }
        #playground .model-list-ul li .model-icon { font-size: 1em; text-align: center; }
        #playground .model-icon.google-blue { color: var(--google-blue); }
        #playground .model-icon.google-green { color: var(--google-green); }
        #playground .model-icon.google-red { color: var(--google-red); }
        #playground .model-icon.star-black { color: var(--star-black); }
        #playground .model-icon.star-orange { color: var(--star-orange); }
        #playground .model-icon.star-blue-gemini { color: var(--star-blue-gemini); }
        #playground .model-icon.star-darkgray { color: var(--star-darkgray); }
        #playground .model-icon.star-skyblue { color: var(--star-skyblue); }

        #playground .glass-footer {
            margin-top: 20px; /* Reducido el margen superior para mejor integración */
            padding: 20px; 
            text-align: center;
            color: var(--color-text-muted); font-size: 0.8rem;
            width: 100%; /* Asegurar que ocupe el ancho del contenedor del playground */
        }
        #playground .glass-footer i.fa-heart { color: var(--color-primary); }

        @keyframes fadeInUpPlayground { from { opacity: 0; transform: translateY(15px) scale(0.99); } to { opacity: 1; transform: translateY(0) scale(1); } } /* Renombrada */
        /* fadeInDown no se usa en el script extraído */

        .hidden { display: none !important; } /* Utilidad global */

        /* Media Queries específicas para el playground integrado */
        @media (max-width: 768px) {
            #playground .main-container { padding: 15px; }
            #playground .glass-title { font-size: clamp(1.5rem, 6vw, 2.2rem); }
            #playground .glass-subtitle { font-size: clamp(0.8rem, 2.8vw, 0.9rem); }
            
            #playground .glass-toolbar { flex-direction: column; gap: 14px; padding: 14px; align-items: stretch; }
            #playground .glass-model-selector-container { width: 100%; }
            #playground .glass-model-btn { justify-content: center; }
            #playground .glass-search-actions { justify-content: space-around; width: 100%; }
            
            #playground .glass-search-input { min-height: 50px; padding: 20px 20px 10px;} /* Ajuste padding */
            #playground .glass-content-area { padding: 16px; max-height: 280px;}
            #playground .enhanced-prompt-card, #playground .simple-suggestion-item { padding: 14px; }
            #playground .glass-model-dropdown { 
                min-width: calc(100% - 30px); /* Respecto al padding del main-container del playground */
                left: 50%; 
                transform: translateX(-50%) translateY(-8px) scale(0.98); 
            }
            #playground .glass-model-dropdown.visible { transform: translateX(-50%) translateY(0) scale(1); }
        }
         @media (max-width: 480px) {
            #playground .glass-action-btn { width: 34px; height: 34px; font-size: 0.85rem;}
            #playground .glass-search-actions { gap: 6px;}
         }
        
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-logo">allhub</div>
            
            <div class="sidebar-nav">
                <a href="#" data-view="dashboard" class="nav-link active">
                    <i class="fas fa-chart-line"></i>
                    Dashboard
                </a>
                <a href="#" data-view="prompts" class="nav-link">
                    <i class="fas fa-lightbulb"></i>
                    My Prompts
                </a>
                <a href="#" data-view="history" class="nav-link">
                    <i class="fas fa-history"></i>
                    History
                </a>
                <a href="#" data-view="playground" class="nav-link">
                    <i class="fas fa-code"></i>
                    Playground
                </a>
                <a href="#" data-view="settings" class="nav-link">
                    <i class="fas fa-cog"></i>
                    Settings
                </a>
            </div>

            <div class="sidebar-footer">
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <div class="user-avatar">JD</div>
                        <span>John Doe</span>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user"></i> Profile</a>
                        <a href="#"><i class="fas fa-bell"></i> Notifications</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle">
                        <i class="fas fa-bars"></i>
                    </button>
                    <h1 class="header-title" id="headerTitle">Dashboard</h1>
                </div>
                <div class="header-actions">
                    <button class="new-prompt-btn" id="newPromptGlobalBtn"> <!-- ID cambiado para evitar conflicto con el del playground -->
                        <i class="fas fa-plus"></i>
                        New Prompt
                    </button>
                </div>
            </header>

            <!-- Dashboard View -->
            <div id="dashboard" class="content-view active">
                <div class="card welcome-card">
                    <h1>Welcome back, John!</h1>
                    <p>Ready to create something amazing today?</p>
                </div>

                <div class="dashboard-content">
                    <div class="activity-chart">
                        <div class="chart-item">
                            <div id="savedPromptsChart"></div>
                            <p>Saved Prompts: 27</p>
                        </div>
                        <div class="chart-item">
                            <div id="promptsUsedChart"></div>
                            <p>Prompts Used (Month): 152</p>
                        </div>
                        <div class="chart-item">
                            <div id="activeFavoritesChart"></div>
                            <p>Active Favorites: 8</p>
                        </div>
                        <div class="chart-item">
                            <div id="responseTimeChart"></div>
                            <p>Avg. Response Time: 1.6s</p>
                        </div>
                        <div class="chart-item">
                            <div id="userEngagementChart"></div>
                            <p>User Engagement: 85%</p>
                        </div>
                        <div class="chart-item">
                            <div id="promptQualityChart"></div>
                            <p>Prompt Quality Score: 92%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prompts View -->
            <div id="prompts" class="content-view">
                <div class="prompts-toolbar">
                    <div class="prompts-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search prompts..." id="promptSearchAllhub"> <!-- ID cambiado para evitar conflicto -->
                    </div>
                    <div class="prompts-filter">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="favorites">Favorites</button>
                        <button class="filter-btn" data-filter="recent">Recent</button>
                    </div>
                </div>

                <div class="prompts-list" id="promptsList">
                    <div class="prompt-card">
                        <div class="prompt-card-header">
                            <h3>Creative Writing Assistant</h3>
                        </div>
                        <div class="prompt-card-meta">
                            <span><i class="fas fa-star"></i> 4.8</span>
                            <span><i class="fas fa-clock"></i> 2 days ago</span>
                            <span><i class="fas fa-tag"></i> Writing</span>
                        </div>
                        <p>A comprehensive prompt for generating creative stories, helping with character development, plot structure, and narrative flow.</p>
                        <div class="prompt-card-actions">
                            <button class="action-btn primary">Use Prompt</button>
                            <button class="action-btn"><i class="fas fa-edit"></i> Edit</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>
                    <!-- Otros prompt-card -->
                </div>
            </div>

            <!-- History View -->
            <div id="history" class="content-view">
                <div class="history-list">
                    <div class="history-card">
                        <div class="history-card-header">
                            <h3>Product Launch Strategy</h3>
                            <span class="history-date">Today</span>
                        </div>
                        <div class="history-card-meta">
                            <span><i class="fas fa-message"></i> 23 messages</span>
                            <span><i class="fas fa-clock"></i> 45 min</span>
                        </div>
                        <p>Developed a comprehensive go-to-market strategy for a new SaaS product, including pricing models and customer acquisition channels.</p>
                        <div class="history-card-actions">
                            <button class="action-btn primary">Continue</button>
                            <button class="action-btn"><i class="fas fa-download"></i> Export</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                        </div>
                    </div>
                    <!-- Otros history-card -->
                </div>
            </div>

            <!-- Playground View - CONTENIDO REEMPLAZADO -->
            <div id="playground" class="content-view">
                <main class="main-container">
                    <section class="header-section">
                        <h1 class="glass-title">Prompt like a pro</h1>
                        <p class="glass-subtitle">Craft the perfect prompts with intelligent suggestions</p>
                    </section>
            
                    <div class="glass-search-container" id="searchAreaContainer"> <!-- ID es único para el script del playground -->
                        <div class="glass-search-card" id="searchBarCard">
                            <textarea 
                            class="glass-search-input" 
                            id="searchInput" 
                            placeholder="Type your prompt here..."></textarea>
            
                            <div class="glass-toolbar">
                                <div class="glass-model-selector-container">
                                    <button class="glass-model-btn" id="modelSelectorBtn">
                                        <i class="fas fa-cogs"></i>
                                        <span id="currentModelName">Model</span>
                                        <i class="fas fa-chevron-down"></i>
                                    </button>
                                </div>
            
                                <div class="glass-search-actions" id="searchActionsContainer">
                                    <button class="glass-action-btn" id="micBtn" title="Voice Input"><i class="fas fa-microphone"></i></button>
                                    <button class="glass-action-btn" id="guidedCreateBtn" title="Guided Creation"><i class="fas fa-wand-magic-sparkles"></i></button>
                                    <button class="glass-action-btn" id="copyBtn" title="Copy Prompt"><i class="fas fa-copy"></i></button>
                                    <button class="glass-action-btn pulse-effect" id="sendBtn" title="Send Prompt"><i class="fas fa-paper-plane"></i></button>
                                </div>
                            </div>
                            
                            <div class="glass-content-area hidden" id="dynamicContentArea">
                                <div class="glass-loading-indicator hidden" id="analyzingIndicator">
                                    <div class="glass-spinner"></div>
                                    Analyzing your prompt...
                                </div>
                                <div class="suggestions-panel hidden" id="simplePromptSuggestionsContainer">
                                    <div class="simple-suggestions-list">
                                    </div>
                                </div>
                                <div class="suggestions-panel hidden" id="enhancedPromptSuggestionsContainer">
                                     <div class="enhanced-prompt-list">
                                     </div>
                                </div>
                                <div class="suggestions-panel hidden" id="favoritePromptsSection">
                                    <h3 class="popular-prompts-section-title">
                                        <i class="fas fa-star"></i>
                                        Popular Prompts
                                    </h3>
                                    <div class="enhanced-prompt-list" id="favoritePromptsList">
                                    </div>
                                </div>
                            </div>
                        </div>
                        
                        <div class="glass-model-dropdown hidden" id="modelDropdownList">
                            <div class="model-search-input-container">
                                <i class="fas fa-search"></i>
                                <input type="text" id="modelSearchInput" placeholder="Search models">
                            </div>
                            <ul class="model-list-ul"></ul>
                        </div>
                    </div>
            
                    <footer class="glass-footer">
                        Copyright © <span id="copyrightYear">2024</span> All Hub. Crafted with <i class="fas fa-heart"></i> & AI.
                    </footer>
                </main>
            </div>

            <!-- Settings View -->
            <div id="settings" class="content-view">
                <h2 class="section-title"><i class="fas fa-cog"></i> Settings</h2>
                <div class="settings-section">
                    <h3>Profile Settings</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label for="settingsFullName">Full Name</label>
                            <input type="text" id="settingsFullName" value="John Doe">
                        </div>
                        <div class="form-group">
                            <label for="settingsEmail">Email Address</label>
                            <input type="email" id="settingsEmail" value="<EMAIL>">
                        </div>
                         <div class="form-group">
                            <label for="settingsPassword">New Password (leave blank to keep current)</label>
                            <input type="password" id="settingsPassword" placeholder="Enter new password">
                        </div>
                        <div class="form-group">
                            <label for="settingsCompany">Company</label>
                            <input type="text" id="settingsCompany" value="Acme Corp">
                        </div>
                        <div class="form-group">
                            <label for="settingsLanguage">Language</label>
                            <select id="settingsLanguage">
                                <option value="en" selected>English</option>
                                <option value="es">Español</option>
                                <option value="fr">Français</option>
                            </select>
                        </div>
                        <div class="settings-actions">
                            <button type="button" class="settings-btn save"><i class="fas fa-save"></i> Save Changes</button>
                            <button type="reset" class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                        </div>
                    </form>
                </div>

                <div class="settings-section">
                    <h3>API Configuration</h3>
                     <form class="settings-form">
                        <div class="form-group">
                            <label for="settingsApiKey">API Key</label>
                            <input type="password" id="settingsApiKey" value="sk-...">
                        </div>
                        <div class="form-group">
                            <label for="settingsDefaultModel">Default Model</label>
                             <select id="settingsDefaultModel">
                                <option value="openai-gpt-4" selected>OpenAI: GPT-4</option>
                                <option value="anthropic-claude-3">Anthropic: Claude 3</option>
                                <option value="google-gemini-1.5">Google: Gemini 1.5</option>
                            </select>
                        </div>
                        <div class="settings-actions">
                            <button type="button" class="settings-btn save"><i class="fas fa-save"></i> Save API Settings</button>
                        </div>
                    </form>
                </div>
                 <div class="settings-section">
                    <h3>Notification Preferences</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label for="emailNotifications">
                                <input type="checkbox" id="emailNotifications" checked> Email Notifications
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="appNotifications">
                                <input type="checkbox" id="appNotifications"> In-App Notifications
                            </label>
                        </div>
                         <div class="settings-actions">
                            <button type="button" class="settings-btn save"><i class="fas fa-save"></i> Save Preferences</button>
                        </div>
                    </form>
                </div>
            </div>
                <!-- Otros settings-section -->
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/ag-charts-community@latest/dist/ag-charts-community.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <script>
        // Script base de allhub
        const navLinks = document.querySelectorAll('.nav-link');
        const contentViews = document.querySelectorAll('.content-view');
        const headerTitle = document.getElementById('headerTitle');
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');

        const viewTitles = {
            dashboard: 'Dashboard',
            prompts: 'My Prompts',
            history: 'Conversation History',
            playground: 'Playground', // Título actualizado
            settings: 'Settings'
        };

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetView = link.getAttribute('data-view');
                
                navLinks.forEach(nav => nav.classList.remove('active'));
                link.classList.add('active');
                
                contentViews.forEach(view => view.classList.remove('active'));
                const targetElement = document.getElementById(targetView);
                if (targetElement) {
                    targetElement.classList.add('active');
                     headerTitle.textContent = viewTitles[targetView];
                } else {
                    console.error(`View element with ID "${targetView}" not found.`);
                }
                
                if (window.innerWidth <= 768) {
                    sidebar.classList.remove('active');
                }
            });
        });

        menuToggle.addEventListener('click', () => {
            sidebar.classList.toggle('active');
        });

        userMenuBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            userDropdown.classList.toggle('visible');
        });

        document.addEventListener('click', (event) => { // `event` para uso en playground
            if (!userMenuBtn.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('visible');
            }
            // El resto de la lógica de cierre de dropdown del playground se manejará en su propio script
        });
        
        userDropdown.addEventListener('click', (e) => {
            e.stopPropagation();
        });

        const filterBtns = document.querySelectorAll('.filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                filterBtns.forEach(b => b.classList.remove('active'));
                btn.classList.add('active');
                const filter = btn.getAttribute('data-filter');
                showNotification(`Filtered by: ${filter}`, 'info');
            });
        });

        const promptSearchAllhub = document.getElementById('promptSearchAllhub');
        if (promptSearchAllhub) {
            promptSearchAllhub.addEventListener('input', (e) => {
                const searchTerm = e.target.value.toLowerCase();
                console.log('Searching in My Prompts:', searchTerm);
            });
        }

        const saveButtons = document.querySelectorAll('.settings-btn.save');
        saveButtons.forEach(btn => {
            btn.addEventListener('click', () => {
                const originalText = btn.innerHTML;
                btn.innerHTML = '<div class="loading"></div> Saving...';
                btn.disabled = true;
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                    showNotification('Settings saved successfully!', 'success');
                }, 1500);
            });
        });

        const newPromptGlobalBtn = document.getElementById('newPromptGlobalBtn');
        if (newPromptGlobalBtn) {
            newPromptGlobalBtn.addEventListener('click', () => {
                navLinks.forEach(nav => nav.classList.remove('active'));
                const playgroundLink = document.querySelector('[data-view="playground"]');
                if (playgroundLink) playgroundLink.classList.add('active');
                
                contentViews.forEach(view => view.classList.remove('active'));
                const playgroundView = document.getElementById('playground');
                if (playgroundView) playgroundView.classList.add('active');
                
                headerTitle.textContent = viewTitles.playground;
                
                // Si el nuevo playground tiene un textarea principal para enfocar, se podría hacer aquí
                // const mainPlaygroundTextarea = document.getElementById('searchInput'); // ID del nuevo textarea
                // if (mainPlaygroundTextarea) {
                //     mainPlaygroundTextarea.value = '';
                //     mainPlaygroundTextarea.focus();
                // }
                showNotification('Switched to Playground!', 'info');
            });
        }

        document.addEventListener('click', (e) => {
            if (e.target.closest('.action-btn')) { // Más robusto para iconos dentro del botón
                const btn = e.target.closest('.action-btn');
                const btnText = btn.textContent.trim();
                if (btnText.includes('Use Prompt') || btnText.includes('Continue')) {
                    showNotification('Opening prompt in playground...', 'info');
                    setTimeout(() => {
                        document.querySelector('[data-view="playground"]').click();
                    }, 500);
                } else if (btnText.includes('Edit')) {
                    showNotification('Opening prompt editor...', 'info');
                } else if (btnText.includes('Share')) {
                    showNotification('Sharing prompt...', 'info');
                } else if (btnText.includes('Export')) {
                    showNotification('Exporting conversation...', 'info');
                }
            }
        });

        function showNotification(message, type = 'info') {
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notif => notif.remove());
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => {
                notification.classList.add('show');
            }, 100);
            setTimeout(() => {
                notification.classList.remove('show');
                setTimeout(() => {
                    notification.remove();
                }, 300);
            }, 3000);
        }

        window.addEventListener('resize', () => {
            if (window.innerWidth > 768 && sidebar.classList.contains('active')) {
                sidebar.classList.remove('active');
            }
        });

        document.addEventListener('DOMContentLoaded', () => {
            showNotification('Welcome to AllHub! 🚀', 'success');

            // AG CHARTS (si existieran en otras secciones)
            // ...

            // ECharts Initialization Code (Dashboard)
            if (typeof echarts !== 'undefined') {
                const chartsToInit = [
                    { id: 'savedPromptsChart', options: { name: 'Saved Prompts', type: 'pie', radius: '55%', roseType: 'radius', data: [{ value: 27, name: 'Saved' }, { value: 73, name: 'Remaining' }] } },
                    { id: 'promptsUsedChart', options: { name: 'Prompts Used', type: 'pie', radius: ['40%', '55%'], data: [{ value: 152, name: 'Used' }, { value: 48, name: 'Remaining' }] } },
                    { id: 'activeFavoritesChart', options: { name: 'Active Favorites', type: 'pie', radius: '55%', roseType: 'area', data: [{ value: 8, name: 'Active' }, { value: 92, name: 'Remaining' }] } },
                    { id: 'responseTimeChart', options: { name: 'Response Time', type: 'bar', xAxisData: ['<1s', '1-2s', '>2s'], data: [40, 50, 10] } },
                    { id: 'userEngagementChart', options: { name: 'User Engagement', type: 'pie', radius: ['40%', '55%'], data: [{ value: 85, name: 'Engaged' }, { value: 15, name: 'Not Engaged' }] } },
                    { id: 'promptQualityChart', options: { name: 'Prompt Quality', type: 'line', smooth: true, xAxisData: ['High', 'Medium', 'Low'], data: [92, 6, 2] } }
                ];

                chartsToInit.forEach(chartConfig => {
                    const chartDiv = document.getElementById(chartConfig.id);
                    if (chartDiv) {
                        const chartInstance = echarts.init(chartDiv);
                        let seriesOpt = {
                            name: chartConfig.options.name,
                            type: chartConfig.options.type,
                            data: chartConfig.options.data,
                            itemStyle: {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: '#C645F9' }, { offset: 1, color: '#000d83' }
                                ]),
                                shadowBlur: 20, shadowColor: 'rgba(0, 0, 0, 0.3)'
                            },
                            animationType: 'scale', animationEasing: 'elasticOut',
                            animationDelay: (idx) => Math.random() * 200
                        };
                        if (chartConfig.options.type === 'pie') {
                            seriesOpt.radius = chartConfig.options.radius;
                            seriesOpt.center = ['50%', '50%'];
                            if(chartConfig.options.roseType) seriesOpt.roseType = chartConfig.options.roseType;
                             seriesOpt.label = { color: 'rgba(0, 0, 0, 0.5)' };
                             seriesOpt.labelLine = { lineStyle: { color: 'rgba(0, 0, 0, 0.3)' }, smooth: 0.2, length: 10, length2: 20 };
                        }
                         if (chartConfig.options.type === 'bar' || chartConfig.options.type === 'line') {
                            seriesOpt.lineStyle = chartConfig.options.type === 'line' ? {
                                color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                                    { offset: 0, color: '#C645F9' }, { offset: 1, color: '#000d83' }
                                ]),
                                shadowBlur: 20, shadowColor: 'rgba(0, 0, 0, 0.3)'
                            } : undefined;
                            if(chartConfig.options.smooth) seriesOpt.smooth = chartConfig.options.smooth;
                        }

                        chartInstance.setOption({
                            backgroundColor: 'rgba(255, 255, 255, 0.06)',
                            tooltip: { trigger: chartConfig.options.type === 'pie' ? 'item' : 'axis' },
                            xAxis: chartConfig.options.xAxisData ? { type: 'category', data: chartConfig.options.xAxisData, axisLabel: { color: 'rgba(0, 0, 0, 0.5)' } } : undefined,
                            yAxis: chartConfig.options.type !== 'pie' ? { type: 'value', axisLabel: { color: 'rgba(0, 0, 0, 0.5)' } } : undefined,
                            series: [seriesOpt]
                        });

                        window.addEventListener('resize', () => {
                            if (echarts.getInstanceByDom(chartDiv)) { echarts.getInstanceByDom(chartDiv).resize(); }
                        });
                    }
                });
            } else {
                console.error("ECharts library not loaded. Dashboard charts will not be initialized.");
            }

            // === SCRIPT DEL NUEVO PLAYGROUND (Prompt like a Pro) ===
            const playgroundViewElement = document.getElementById('playground');
            if (playgroundViewElement) { // Solo ejecutar si estamos en la vista de playground o el contenedor existe
                const searchAreaContainer = playgroundViewElement.querySelector('#searchAreaContainer');
                const searchBarCard = playgroundViewElement.querySelector('#searchBarCard');
                const searchInput = playgroundViewElement.querySelector('#searchInput');
                
                const guidedCreateBtn = playgroundViewElement.querySelector('#guidedCreateBtn');
                const micBtn = playgroundViewElement.querySelector('#micBtn');
                const copyBtn = playgroundViewElement.querySelector('#copyBtn');
                const sendBtn = playgroundViewElement.querySelector('#sendBtn');

                const dynamicContentArea = playgroundViewElement.querySelector('#dynamicContentArea');
                const analyzingIndicator = playgroundViewElement.querySelector('#analyzingIndicator');
                const simplePromptSuggestionsContainer = playgroundViewElement.querySelector('#simplePromptSuggestionsContainer');
                const enhancedPromptSuggestionsContainer = playgroundViewElement.querySelector('#enhancedPromptSuggestionsContainer');
                const favoritePromptsSection = playgroundViewElement.querySelector('#favoritePromptsSection');
                const favoritePromptsList = playgroundViewElement.querySelector('#favoritePromptsList');

                const modelSelectorBtn = playgroundViewElement.querySelector('#modelSelectorBtn');
                const currentModelNameSpan = playgroundViewElement.querySelector('#currentModelName');
                const modelDropdownList = playgroundViewElement.querySelector('#modelDropdownList');
                const modelSearchInput = playgroundViewElement.querySelector('#modelSearchInput');
                const modelListUl = modelDropdownList ? modelDropdownList.querySelector('.model-list-ul') : null;

                let isGuidedModeActive = false;
                let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHub')) || [];
                let blockSuggestionsOnNextFocus = false;
                // inputDebounceTimeout no es necesario aquí ya que debounce devuelve la función debounced directamente

                const modelsData = [
                    { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
                    { id: "anthropic-claude-3", displayName: "Anthropic: Claude 3", shortName: "Claude 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-orange" },
                    { id: "google-gemini-1.5", displayName: "Google: Gemini 1.5", shortName: "Gemini 1.5", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-blue-gemini" },
                    { id: "mistral-large", displayName: "Mistral: Large", shortName: "Mistral Large", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-darkgray" },
                    { id: "meta-llama-3", displayName: "Meta: LLaMA 3", shortName: "LLaMA 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-skyblue" },
                ];
                
                function debouncePlayground(func, delay) { // Renombrar para evitar conflicto
                    let timeout;
                    const debounced = function(...args) {
                        const context = this;
                        clearTimeout(timeout);
                        timeout = setTimeout(() => func.apply(context, args), delay);
                    };
                    debounced.cancel = function() { clearTimeout(timeout); };
                    return debounced;
                }

                function autoResizeTextarea(textarea) {
                    if (!textarea) return;
                    textarea.style.height = 'auto';
                    let scrollHeight = textarea.scrollHeight;
                    const minHeight = parseInt(window.getComputedStyle(textarea).minHeight, 10) || 60;
                    textarea.style.height = Math.max(minHeight, scrollHeight) + 'px';
                    textarea.style.overflowY = (scrollHeight > minHeight && scrollHeight > textarea.clientHeight) ? 'auto' : 'hidden';
                }
                
                function formatFullPromptForDisplay(fullPromptText) {
                    if (!fullPromptText) return "";
                    let html = fullPromptText.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;"); 
                    html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim, (match) => `<span class="prompt-heading">${match.trim()}</span>`);
                    return html.replace(/\n/g, '<br>');
                }

                function updateDynamicContentAreaVisibility() {
                    if (!dynamicContentArea || !simplePromptSuggestionsContainer || !enhancedPromptSuggestionsContainer || !favoritePromptsSection || !analyzingIndicator || !favoritePromptsList) return;
                    const isSimpleVisible = !simplePromptSuggestionsContainer.classList.contains('hidden') && simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')?.children.length > 0;
                    const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')?.children.length > 0;
                    const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && favoritePromptsList.children.length > 0;
                    const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden');

                    if (isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible) {
                        dynamicContentArea.classList.remove('hidden');
                    } else {
                        dynamicContentArea.classList.add('hidden');
                    }
                }

                function hideAllDynamicContentExcept(exceptContainer = null) {
                    [simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => {
                        if (container && container !== exceptContainer) {
                            container.classList.add('hidden');
                            if (container !== analyzingIndicator && container.id !== 'simplePromptSuggestionsContainer' && container.id !== 'enhancedPromptSuggestionsContainer' && container.id !== 'favoritePromptsSection' ) {
                                // No hacer nada específico
                            } else if (container.id === 'simplePromptSuggestionsContainer' && container.querySelector('.simple-suggestions-list')) {
                                container.querySelector('.simple-suggestions-list').innerHTML = '';
                            } else if (container.id === 'enhancedPromptSuggestionsContainer' && container.querySelector('.enhanced-prompt-list')) {
                                container.querySelector('.enhanced-prompt-list').innerHTML = '';
                            } else if (container.id === 'favoritePromptsSection' && favoritePromptsList) {
                                favoritePromptsList.innerHTML = '';
                            }
                        }
                    });
                     if (exceptContainer) exceptContainer.classList.remove('hidden');
                }

                function setDefaultModel() {
                    if (!currentModelNameSpan) return;
                    if (modelsData.length > 0) {
                        const defaultModel = modelsData[0];
                        currentModelNameSpan.textContent = defaultModel.shortName;
                        currentModelNameSpan.dataset.selectedModelId = defaultModel.id;
                    } else {
                        currentModelNameSpan.textContent = "Model";
                        delete currentModelNameSpan.dataset.selectedModelId;
                    }
                }

                function populateModelDropdown() {
                    if (!modelListUl || !modelSearchInput || !currentModelNameSpan) return;
                    modelListUl.innerHTML = '';
                    const searchTerm = modelSearchInput.value.toLowerCase();
                    const currentSelectedModelId = currentModelNameSpan.dataset.selectedModelId;

                    modelsData.filter(model => model.displayName.toLowerCase().includes(searchTerm)).forEach(model => {
                        const li = document.createElement('li');
                        li.dataset.modelId = model.id;
                        li.dataset.modelShortName = model.shortName;
                        li.dataset.modelDisplayName = model.displayName;

                        let iconElement;
                        if (model.iconType === "fa") {
                            iconElement = document.createElement('i');
                            iconElement.className = `model-icon ${model.iconClass} ${model.iconColorClass || ''}`;
                        } else if (model.iconType === "img") {
                            iconElement = document.createElement('img');
                            iconElement.src = model.iconUrl;
                            iconElement.alt = model.displayName.split(':')[0];
                            iconElement.onerror = () => { iconElement.style.display = 'none'; };
                        } else {
                            iconElement = document.createElement('span');
                            iconElement.className = 'model-icon icon-placeholder';
                            iconElement.textContent = '●';
                        }
                        li.appendChild(iconElement);

                        const span = document.createElement('span');
                        span.textContent = model.displayName;
                        li.appendChild(span);

                        if (currentSelectedModelId && currentSelectedModelId === model.id) {
                            li.classList.add('selected-model-item');
                        }

                        li.addEventListener('click', (e) => {
                            e.stopPropagation();
                            currentModelNameSpan.textContent = model.shortName;
                            currentModelNameSpan.dataset.selectedModelId = model.id;

                            modelListUl.querySelectorAll('li').forEach(item => item.classList.remove('selected-model-item'));
                            li.classList.add('selected-model-item');

                            modelDropdownList.classList.remove('visible');
                            modelDropdownList.classList.add('hidden');
                            if(modelSelectorBtn) modelSelectorBtn.classList.remove('open');
                            
                            blockSuggestionsOnNextFocus = true;
                            if(searchInput) searchInput.focus();
                            hideAllDynamicContentExcept();
                            updateDynamicContentAreaVisibility();
                        });
                        modelListUl.appendChild(li);
                    });
                }

                function renderFavoritePrompts() {
                    if (!favoritePromptsList || !searchInput || !favoritePromptsSection || (modelDropdownList && modelDropdownList.classList.contains('visible'))) return;
                    if (isGuidedModeActive) return;

                    hideAllDynamicContentExcept(favoritePromptsSection);
                    favoritePromptsList.innerHTML = '';

                    if (favoritePrompts.length > 0 && searchInput.value.length === 0) {
                        favoritePrompts.forEach(promptData => {
                            const item = createPromptCard(promptData, false, true);
                            favoritePromptsList.appendChild(item);
                        });
                        favoritePromptsSection.classList.remove('hidden');
                    } else {
                        favoritePromptsSection.classList.add('hidden');
                    }
                    updateDynamicContentAreaVisibility();
                }

                function toggleFavorite(promptData, starIconElement) { // starIconElement es el <i>
                    const promptId = promptData.id || promptData.title;
                    let index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptId);
                    let wasFavorited = index > -1;

                    if (wasFavorited) {
                        favoritePrompts.splice(index, 1);
                    } else {
                        const favData = { 
                            id: promptData.id, 
                            title: promptData.title, 
                            fullPrompt: promptData.fullPrompt, 
                            metaMinimal: promptData.metaMinimal, 
                            description: promptData.description || "N/A" 
                        };
                        favoritePrompts.push(favData);
                    }
                    localStorage.setItem('favoritePromptsAllHub', JSON.stringify(favoritePrompts));
                    
                    // Actualizar todos los botones de estrella para este prompt en la página
                    playgroundViewElement.querySelectorAll(`.glass-star-btn[data-prompt-id="${promptId}"]`).forEach(starBtn => {
                        const iElement = starBtn.querySelector('i');
                        if (wasFavorited) { // Significa que ahora NO es favorito
                            starBtn.classList.remove('favorited');
                            if(iElement) { iElement.classList.remove('fas'); iElement.classList.add('far'); }
                        } else { // Significa que ahora SÍ es favorito
                            starBtn.classList.add('favorited');
                            if(iElement) { iElement.classList.remove('far'); iElement.classList.add('fas'); }
                        }
                    });

                    if (!isGuidedModeActive && searchInput && searchInput.value === '') {
                        renderFavoritePrompts();
                    }
                }
                
                function createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) {
                    const card = document.createElement('div');
                    card.classList.add('enhanced-prompt-card');
                    if (isFavoriteListItem) card.classList.add('favorite-list-item-card');
                    const uniqueId = promptData.id || (promptData.title ? promptData.title.replace(/\s+/g, '-').toLowerCase() : Date.now().toString());
                    card.dataset.promptId = uniqueId;

                    const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === (promptData.id || promptData.title));
                    const starIconClass = isFavorited ? 'fas fa-star' : 'far fa-star';

                    let metaMinimalHTML = '';
                    if (promptData.metaMinimal) {
                        metaMinimalHTML = '<div class="enhanced-prompt-meta-minimal">';
                        if (promptData.metaMinimal.inputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-in-alt"></i> ${promptData.metaMinimal.inputTokens}</span>`;
                        if (promptData.metaMinimal.outputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-out-alt"></i> ${promptData.metaMinimal.outputTokens}</span>`;
                        if (promptData.metaMinimal.time) metaMinimalHTML += `<span class="meta-item"><i class="far fa-clock"></i> ${promptData.metaMinimal.time}</span>`;
                        if (promptData.metaMinimal.reuse) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-redo-alt"></i> ${promptData.metaMinimal.reuse}</span>`;
                        metaMinimalHTML += '</div>';
                    }

                    const buttonText = isFavoriteListItem ? 'Use Favorite' : 'Select Prompt';

                    card.innerHTML = `
                        <div class="enhanced-card-header">
                            <h5 class="enhanced-card-title">${promptData.title || "Prompt"}</h5>
                            <button class="glass-star-btn" title="Mark as favorite" data-prompt-id="${uniqueId}">
                                <i class="${starIconClass}"></i>
                            </button>
                        </div>
                        ${metaMinimalHTML}
                        <div class="full-prompt-display">${formatFullPromptForDisplay(promptData.fullPrompt || promptData.title)}</div>
                        <button class="select-prompt-button">
                            <i class="fas fa-check-circle"></i> ${buttonText}
                        </button>
                    `;
                    const starButton = card.querySelector('.glass-star-btn');
                    if (starButton) {
                        starButton.addEventListener('click', (e) => {
                            e.stopPropagation();
                            toggleFavorite(promptData, e.currentTarget.querySelector('i'));
                        });
                    }
                    
                    const selectButton = card.querySelector('.select-prompt-button');
                    if (selectButton) {
                         selectButton.addEventListener('click', () => {
                            if(!searchInput) return;
                            let promptTextToSet = promptData.fullPrompt || promptData.title;
                            if (promptData.id === "few-shot-summarize-pdf") { // Caso especial del ejemplo
                                promptTextToSet = promptTextToSet.replace(/\n{2,}/g, '\n'); 
                            }
                            searchInput.value = promptTextToSet;
                            autoResizeTextarea(searchInput);
                            exitGuidedModeIfNeeded();
                            hideAllDynamicContentExcept();
                            updateDynamicContentAreaVisibility();
                            blockSuggestionsOnNextFocus = true;
                            searchInput.focus();
                        });
                    }
                    return card;
                }

                function displayGuidedPromptList(suggestions) {
                    if (!enhancedPromptSuggestionsContainer || !searchBarCard) return;
                    isGuidedModeActive = true;
                    hideAllDynamicContentExcept(enhancedPromptSuggestionsContainer);
                    const listContainer = enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list');
                    if (!listContainer) return;
                    listContainer.innerHTML = '';

                    if (suggestions && suggestions.length > 0) {
                        suggestions.forEach(suggData => {
                            const card = createPromptCard(suggData);
                            listContainer.appendChild(card);
                        });
                        enhancedPromptSuggestionsContainer.classList.remove('hidden');
                    }
                    searchBarCard.classList.add('focused');
                    updateDynamicContentAreaVisibility();
                }

                function generateGuidedPrompts() {
                    // Estas son las sugerencias fijas del ejemplo
                    let suggestions = [];
                    suggestions.push({ title: "Improve with Few-Shot Examples", id: "few-shot-summarize-pdf", fullPrompt: `Write a blog article about how to summarize a PDF. Follow the style and structure of these examples:\n\nExample 1:\nTitle: "How to Create a Mind Map"\nIntroduction: Mind maps are great for organizing thoughts. This article explains simple steps to create one.\nStep 1: Choose a central topic...\nStep 2: Add branches for subtopics...\nConclusion: Mind mapping is easy and fun. Try it today!\n\nExample 2:\nTitle: "How to Take Better Notes"\nIntroduction: Good notes help you study better. Here’s how to do it.\nStep 1: Use a clear format...\nStep 2: Highlight key points...\nConclusion: Better notes lead to better learning. Start now! Write a 400-word blog article titled "How to Summarize a PDF Effectively" with an introduction, 3 practical steps, and a conclusion. Use a clear and simple tone for students.`, metaMinimal: { inputTokens: "170", outputTokens: "~400-600", time: "~1-2s", reuse: "High" }, description: "Uses few-shot examples." });
                    suggestions.push({ title: "Enhance with Chain of Thought", id: "cot-summarize-pdf", fullPrompt: `Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process as follows: Identify the key components of a blog article (e.g., introduction, main steps, conclusion). List 3 practical methods for summarizing a PDF (e.g., using software, manual highlighting, or online tools). For each method, explain one benefit and one challenge. Structure the article with a clear introduction, a section for each method, and a conclusion encouraging readers to try summarizing. Use a professional yet accessible tone for small business owners. Title the article "Mastering PDF Summarization: A Step-by-Step Guide."`, metaMinimal: { inputTokens: "195", outputTokens: "~500-800", time: "~1-3s", reuse: "High" }, description: "Uses Chain of Thought." });
                    return suggestions;
                }

                async function handleGuidedCreateClick() {
                    if (modelDropdownList && modelDropdownList.classList.contains('visible')) {
                        modelDropdownList.classList.remove('visible');
                        modelDropdownList.classList.add('hidden');
                        if(modelSelectorBtn) modelSelectorBtn.classList.remove('open');
                    }
                    if (!analyzingIndicator || !searchBarCard) return;
                    hideAllDynamicContentExcept(analyzingIndicator);
                    analyzingIndicator.classList.remove('hidden');
                    searchBarCard.classList.add('focused');
                    updateDynamicContentAreaVisibility();

                    await new Promise(resolve => setTimeout(resolve, 1200));
                    const guidedPrompts = generateGuidedPrompts();
                    displayGuidedPromptList(guidedPrompts);
                }

                function exitGuidedModeIfNeeded() {
                    if (isGuidedModeActive) {
                        isGuidedModeActive = false;
                        hideAllDynamicContentExcept();
                        if (document.activeElement !== searchInput && searchBarCard) {
                            searchBarCard.classList.remove('focused');
                        }
                        if (searchInput && searchInput.value === '') renderFavoritePrompts();
                        updateDynamicContentAreaVisibility();
                    }
                }

                function generateSimpleSuggestions(inputText) {
                    const baseSuggestions = [
                        "Write a blog post about how to automatically extract key points from a PDF",
                        "Write an article about summarizing research papers using AI",
                        "Write a tweet about the best free tools to summarize PDFs",
                        "Write a blog post about building a PDF summarizer with Python",
                        "Write a blog article about summarizing legal documents without reading them",
                        "Write a blog post about syncing summarized PDF meeting notes with Notion"
                    ];
                    if (!inputText) return [];
                    const lowerInput = inputText.toLowerCase();
                    return baseSuggestions.filter(sugg => sugg.toLowerCase().includes(lowerInput)).slice(0, 4);
                }

                const handleSimpleInput = debouncePlayground(function() {
                    if (isGuidedModeActive || (modelDropdownList && modelDropdownList.classList.contains('visible'))) {
                        hideAllDynamicContentExcept();
                        updateDynamicContentAreaVisibility();
                        return;
                    }
                    if (!searchInput || !simplePromptSuggestionsContainer) return;

                    const inputText = searchInput.value;
                    hideAllDynamicContentExcept(simplePromptSuggestionsContainer);
                    const listContainer = simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list');
                    if (!listContainer) return;
                    listContainer.innerHTML = '';

                    if (inputText.length < 1) {
                        simplePromptSuggestionsContainer.classList.add('hidden');
                        if (inputText.length === 0) renderFavoritePrompts();
                        updateDynamicContentAreaVisibility();
                        return;
                    }

                    const suggestions = generateSimpleSuggestions(inputText);
                    if (suggestions.length > 0) {
                        simplePromptSuggestionsContainer.classList.remove('hidden');
                        suggestions.forEach(suggText => {
                            const item = document.createElement('div');
                            item.classList.add('simple-suggestion-item');
                            const matchIndex = suggText.toLowerCase().indexOf(inputText.toLowerCase());
                            if (matchIndex > -1) {
                                item.innerHTML = suggText.substring(0, matchIndex) +
                                                 `<strong>${suggText.substring(matchIndex, matchIndex + inputText.length)}</strong>` +
                                                 suggText.substring(matchIndex + inputText.length);
                            } else {
                                item.textContent = suggText;
                            }
                            item.addEventListener('click', () => {
                                searchInput.value = suggText;
                                autoResizeTextarea(searchInput);
                                hideAllDynamicContentExcept();
                                updateDynamicContentAreaVisibility();
                                blockSuggestionsOnNextFocus = true;
                                searchInput.focus();
                            });
                            listContainer.appendChild(item);
                        });
                    } else {
                        simplePromptSuggestionsContainer.classList.add('hidden');
                    }
                    updateDynamicContentAreaVisibility();
                }, 300);

                if(searchInput) {
                    searchInput.addEventListener('input', () => {
                        autoResizeTextarea(searchInput);
                        if (isGuidedModeActive && searchInput.value === '') {
                            exitGuidedModeIfNeeded();
                        } else if (!isGuidedModeActive && (!modelDropdownList || !modelDropdownList.classList.contains('visible'))) {
                            blockSuggestionsOnNextFocus = false;
                            handleSimpleInput();
                        } else if (modelDropdownList && modelDropdownList.classList.contains('visible')) {
                            hideAllDynamicContentExcept();
                            updateDynamicContentAreaVisibility();
                        }
                    });

                    searchInput.addEventListener('focus', () => {
                        if (!searchBarCard) return;
                        searchBarCard.classList.add('focused');
                        autoResizeTextarea(searchInput);

                        if ((modelDropdownList && modelDropdownList.classList.contains('visible')) || isGuidedModeActive) {
                            return;
                        }
                        
                        if (searchInput.value === '') {
                            renderFavoritePrompts();
                        } else {
                            if (blockSuggestionsOnNextFocus) {
                                blockSuggestionsOnNextFocus = false;
                            } else {
                                handleSimpleInput.cancel(); // Cancelar cualquier debounce pendiente
                                handleSimpleInput(); // Ejecutar inmediatamente o con nuevo debounce
                            }
                        }
                    });
                    
                    let blurTimeoutPlayground; // Renombrar para evitar conflicto
                    searchInput.addEventListener('blur', () => {
                        blurTimeoutPlayground = setTimeout(() => {
                            if (!searchBarCard || !playgroundViewElement) return;
                            const activeEl = document.activeElement;
                            const isFocusWithinRelevantArea = 
                                searchBarCard.contains(activeEl) ||
                                (modelDropdownList && modelDropdownList.contains(activeEl)) ||
                                (dynamicContentArea && dynamicContentArea.contains(activeEl));

                            if (!isFocusWithinRelevantArea) {
                                searchBarCard.classList.remove('focused');
                                if (modelDropdownList && !modelDropdownList.classList.contains('visible') && !isGuidedModeActive) {
                                    hideAllDynamicContentExcept();
                                    updateDynamicContentAreaVisibility();
                                }
                            }
                        }, 150);
                    });

                    [modelDropdownList, dynamicContentArea].forEach(el => {
                        if(el) { // Chequear si el elemento existe
                            el.addEventListener('focusin', () => clearTimeout(blurTimeoutPlayground));
                            el.addEventListener('mousedown', () => clearTimeout(blurTimeoutPlayground)); // Prevenir blur al hacer click dentro
                        }
                    });
                }


                if(modelSelectorBtn) {
                    modelSelectorBtn.addEventListener('click', (e) => {
                        e.stopPropagation();
                        if (!modelDropdownList) return;
                        const isCurrentlyHidden = modelDropdownList.classList.contains('hidden');
                        
                        exitGuidedModeIfNeeded();
                        hideAllDynamicContentExcept();

                        if (isCurrentlyHidden) {
                            modelDropdownList.style.top = ''; 
                            modelDropdownList.style.left = '';
                            modelDropdownList.style.width = '';

                            modelDropdownList.classList.remove('hidden');
                            modelDropdownList.classList.add('visible');
                            modelSelectorBtn.classList.add('open');
                            if(modelSearchInput) modelSearchInput.value = '';
                            populateModelDropdown();
                            if(modelSearchInput) modelSearchInput.focus();
                        } else {
                            modelDropdownList.classList.remove('visible');
                            modelDropdownList.classList.add('hidden');
                            modelSelectorBtn.classList.remove('open');
                        }
                        updateDynamicContentAreaVisibility();
                    });
                }

                if(modelSearchInput) {
                    modelSearchInput.addEventListener('input', populateModelDropdown);
                    modelSearchInput.addEventListener('click', (e) => e.stopPropagation());
                }

                if(guidedCreateBtn) guidedCreateBtn.addEventListener('click', handleGuidedCreateClick);
                if (micBtn) { micBtn.addEventListener('click', () => {
                    showNotification("Voice input feature coming soon!", "info");
                    console.log("Micrófono presionado (implementar funcionalidad)");
                }); }
                if (copyBtn) { copyBtn.addEventListener('click', (e) => { 
                    e.stopPropagation(); 
                    if(searchInput && searchInput.value) navigator.clipboard.writeText(searchInput.value)
                        .then(() => showNotification('Prompt Copied!', 'success'))
                        .catch(err => {
                            showNotification('Failed to copy prompt.', 'error');
                            console.error('Failed to copy prompt: ', err);
                        }); 
                });}
                if (sendBtn) { sendBtn.addEventListener('click', (e) => { 
                    e.stopPropagation(); 
                    if(searchInput && searchInput.value && currentModelNameSpan) {
                        showNotification(`Sending prompt with ${currentModelNameSpan.textContent}...`, 'info');
                        console.log('Prompt sent: ' + searchInput.value + ' (Model: ' + currentModelNameSpan.textContent + ')');
                    } else if (searchInput && !searchInput.value) {
                        showNotification('Please enter a prompt first.', 'error');
                    }
                });}

                // Cierre de dropdowns y control de foco general para el playground
                document.addEventListener('click', (event) => {
                    if (modelDropdownList && modelSelectorBtn && !modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target) && modelDropdownList.classList.contains('visible')) {
                        modelDropdownList.classList.remove('visible');
                        modelDropdownList.classList.add('hidden');
                        modelSelectorBtn.classList.remove('open');
                    }

                    if (searchAreaContainer) { // Asegurarse que existe antes de acceder a .contains
                        const isClickInsideSearchArea = searchAreaContainer.contains(event.target) || 
                                                        (modelDropdownList && modelDropdownList.contains(event.target));

                        if (!isClickInsideSearchArea && searchBarCard) { // Si searchBarCard existe
                            searchBarCard.classList.remove('focused');
                            if (!isGuidedModeActive && (!modelDropdownList || !modelDropdownList.classList.contains('visible'))) {
                                hideAllDynamicContentExcept();
                                if(searchInput && searchInput.value === '') renderFavoritePrompts();
                                updateDynamicContentAreaVisibility();
                            }
                        }
                    }
                });
                
                // Inicialización específica del playground
                if(searchInput) autoResizeTextarea(searchInput);
                setDefaultModel();
                const copyrightYearEl = playgroundViewElement.querySelector('#copyrightYear');
                if(copyrightYearEl) copyrightYearEl.textContent = new Date().getFullYear();

                if (searchInput && searchInput.value === '') {
                    renderFavoritePrompts();
                }
                if (document.activeElement === searchInput && searchBarCard) {
                    searchBarCard.classList.add('focused');
                }
                // Asegurarse que los contenedores de listas existen antes de añadirles hijos
                if (simplePromptSuggestionsContainer && !simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')) {
                    const list = document.createElement('div');
                    list.className = 'simple-suggestions-list';
                    simplePromptSuggestionsContainer.appendChild(list);
                }
                if (enhancedPromptSuggestionsContainer && !enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')) {
                    const list = document.createElement('div');
                    list.className = 'enhanced-prompt-list';
                    enhancedPromptSuggestionsContainer.appendChild(list);
                }
            } // Fin del if (playgroundViewElement)
        });
    </script>
</body>
</html>