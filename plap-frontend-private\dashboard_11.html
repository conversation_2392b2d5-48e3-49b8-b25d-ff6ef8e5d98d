<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>allhub - Private Area</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@300;400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
:root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            --color-tertiary: #F9E7FE;
            --color-quaternary: #ffffff;
            --color-primary: #C645F9; /* Rosa/Púrpura para nodos principales */
            --color-secondary: #007bff; /* Azul para nodos de concepto/agrupación (ajustado para mejor contraste) */
            --color-dark: #000d83; /* Color oscuro de texto principal de allhub */
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.12);
            --glass-bg-hover: rgba(255, 255, 255, 0.2);
            --glass-bg-strong: rgba(255, 255, 255, 0.15);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.08);
            --glass-border: rgba(255, 255, 255, 0.25);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.15);
            --glass-hover-shadow: 0 16px 48px rgba(13, 4, 37, 0.25);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 65%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 45%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.15);
            --color-active-accent: rgba(94, 108, 231, 0.2);
            --border-radius-card: 24px;
            --border-radius-pill: 50px;
            --border-radius-element: 16px;
            --transition-speed: 0.3s;
            --transition-ease: cubic-bezier(0.4, 0.0, 0.2, 1);
            --sidebar-width: 280px;
            --header-height: 80px; 
            --main-content-padding-top: 30px; 
            --main-content-padding-bottom: 30px;
            --playground-gap: 24px; 

            /* Variables específicas para el knowledge graph */
            --kg-node-pink: #E91E63; /* Rosa más similar a la imagen */
            --kg-node-blue: #2196F3; /* Azul más similar a la imagen */
            --kg-link-color: #AEB6BF; /* Gris para enlaces */
            --kg-link-label-color: #5D6D7E; /* Gris oscuro para etiquetas de enlace */

            --glass-border-highlight: rgba(255, 255, 255, 0.3);
            --color-star-active: #FFD700;
            --color-placeholder: var(--color-text-muted);
            --color-icon-gray: var(--color-text-secondary);
            --google-blue: #4285F4; /* No usado en este grafo, pero se mantiene por si acaso */
            /* ... otras variables de color de modelos ... */
        }

        /* Reset y estilos base */
        * { 
            margin: 0; 
            padding: 0; 
            box-sizing: border-box; 
        }
        
        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            color: var(--color-text-primary);
            /* Fondo del HTML base (allhub) */
            background: linear-gradient(135deg, #F5F5F5 0%, #E8E8F0 30%, #DDD9E8 70%, #D5D5D5 100%);
            background-attachment: fixed;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }
        
        .app-container { 
            display: flex; 
            min-height: 100vh; 
        }
        
        .sidebar { 
            width: var(--sidebar-width); 
            background: var(--glass-bg); 
            backdrop-filter: blur(24px); 
            -webkit-backdrop-filter: blur(24px); 
            border-right: 1px solid var(--glass-border); 
            padding: 30px 20px; 
            position: fixed; 
            height: 100%; 
            z-index: 1000; 
            display: flex; 
            flex-direction: column; 
            box-shadow: var(--glass-shadow); 
            transition: transform var(--transition-speed) var(--transition-ease); 
        }
        
        .sidebar-logo { 
            font-family: var(--font-header); 
            font-size: 1.8rem; 
            font-weight: 700; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); 
            -webkit-background-clip: text; 
            -webkit-text-fill-color: transparent; 
            text-align: center; 
            margin-bottom: 30px; 
            letter-spacing: -0.5px; 
        }
        
        .sidebar-nav { 
            flex-grow: 1; 
            display: flex; 
            flex-direction: column; 
            gap: 8px; 
        }
        
        .sidebar-nav a { 
            display: flex; 
            align-items: center; 
            gap: 12px; 
            padding: 16px 20px; 
            font-size: 0.95rem; 
            font-weight: 500; 
            color: var(--color-text-primary); 
            text-decoration: none; 
            border-radius: var(--border-radius-element); 
            transition: all var(--transition-speed) var(--transition-ease); 
            position: relative; 
            overflow: hidden; 
            backdrop-filter: blur(8px); 
            cursor: pointer; 
        }
        
        .sidebar-nav a::before { 
            content: ''; 
            position: absolute; 
            top: 0; 
            left: -100%; 
            width: 100%; 
            height: 100%; 
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.15), transparent); 
            transition: left 0.6s ease; 
        }
        
        .sidebar-nav a:hover::before { 
            left: 100%; 
        }
        
        .sidebar-nav a:hover { 
            background: var(--glass-bg-hover); 
            color: var(--color-primary); 
            transform: translateX(6px); 
            box-shadow: var(--glass-hover-shadow); 
        }
        
        .sidebar-nav a.active { 
            background: var(--color-hover-accent); 
            color: var(--color-primary); 
            font-weight: 600; 
            box-shadow: var(--glass-shadow); 
        }
        
        .sidebar-nav a i { 
            font-size: 1.1rem; 
            color: var(--color-text-secondary); 
            transition: color var(--transition-speed) var(--transition-ease); 
        }
        
        .sidebar-nav a.active i, 
        .sidebar-nav a:hover i { 
            color: var(--color-primary); 
        }
        
        .sidebar-footer { 
            margin-top: auto; 
            padding-top: 20px; 
        }
        
        .user-menu { 
            position: relative; 
            margin-bottom: 20px; 
        }
        
        .user-menu-btn { 
            background: var(--glass-bg-strong); 
            border: none; 
            border-radius: var(--border-radius-pill); 
            padding: 12px 18px; 
            display: flex; 
            align-items: center; 
            gap: 12px; 
            cursor: pointer; 
            transition: all var(--transition-speed) var(--transition-ease); 
            color: var(--color-text-primary); 
            font-weight: 600; 
            font-size: 0.9rem; 
            width: 100%; 
            box-shadow: var(--glass-shadow); 
        }
        
        .user-menu-btn:hover { 
            transform: translateY(-2px) scale(1.02); 
            box-shadow: var(--glass-hover-shadow); 
        }
        
        .user-avatar { 
            width: 32px; 
            height: 32px; 
            border-radius: 50%; 
            background: rgba(255, 255, 255, 0.2); 
            display: flex; 
            align-items: center; 
            justify-content: center; 
            color: var(--color-text-primary); 
            font-weight: 700; 
            font-size: 0.9rem; 
            border: 2px solid rgba(255, 255, 255, 0.3); 
        }
        
        .user-dropdown { 
            position: absolute; 
            bottom: calc(100% + 12px); 
            left: 0; 
            background: var(--glass-bg-strong); 
            backdrop-filter: blur(24px); 
            -webkit-backdrop-filter: blur(24px); 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-element); 
            box-shadow: var(--glass-hover-shadow); 
            width: 220px; 
            padding: 12px 0; 
            opacity: 0; 
            visibility: hidden; 
            transform: translateY(10px); 
            transition: all var(--transition-speed) var(--transition-ease); 
            z-index: 1000; 
        }
        
        .user-dropdown.visible { 
            opacity: 1; 
            visibility: visible; 
            transform: translateY(0); 
        }
        
        .user-dropdown a { 
            display: flex; 
            align-items: center; 
            padding: 12px 18px; 
            color: var(--color-text-primary); 
            text-decoration: none; 
            font-size: 0.9rem; 
            font-weight: 500; 
            transition: all var(--transition-speed) var(--transition-ease); 
        }
        
        .user-dropdown a:hover { 
            background: var(--glass-bg-hover); 
            color: var(--color-primary); 
            transform: translateX(6px); 
        }
        
        .user-dropdown a i { 
            margin-right: 12px; 
            color: var(--color-text-muted); 
            width: 16px; 
            text-align: center; 
        }
        
        .user-dropdown .divider { 
            height: 1px; 
            background: var(--glass-border); 
            margin: 8px 0; 
        }
        
        .main-content { 
            margin-left: var(--sidebar-width); 
            flex-grow: 1; 
            padding-top: var(--main-content-padding-top); 
            padding-right: 30px; 
            padding-bottom: var(--main-content-padding-bottom); 
            padding-left: 30px; 
            display: flex; 
            flex-direction: column; 
        }
        
        .header { 
            background: linear-gradient(135deg, var(--color-tertiary), var(--color-quaternary)) !important; 
            backdrop-filter: blur(24px); 
            -webkit-backdrop-filter: blur(24px); 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-card); 
            padding: 20px 30px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            box-shadow: var(--glass-shadow); 
            position: sticky; 
            top: 0; 
            z-index: 900; 
            margin-bottom: 24px; 
        }
        
        .header-left { 
            display: flex; 
            align-items: center; 
            gap: 18px; 
        }
        
        .menu-toggle { 
            display: none; 
            font-size: 1.5rem; 
            color: var(--color-text-primary); 
            cursor: pointer; 
            background: none; 
            border: none; 
            padding: 8px; 
            border-radius: 8px; 
            transition: all var(--transition-speed) var(--transition-ease); 
        }
        
        .menu-toggle:hover { 
            background: var(--glass-bg-hover); 
            color: var(--color-primary); 
        }
        
        .header-title { 
            font-family: var(--font-header); 
            font-size: 1.7rem; 
            font-weight: 600; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); 
            -webkit-background-clip: text; 
            -webkit-text-fill-color: transparent; 
            letter-spacing: -0.5px; 
        }
        
        .header-actions { 
            display: flex; 
            align-items: center; 
            gap: 15px; 
        }
        
        .new-prompt-btn { 
            padding: 14px 28px; 
            border-radius: var(--border-radius-pill); 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); 
            color: var(--color-text-on-gradient); 
            font-weight: 600; 
            font-size: 0.9rem; 
            border: none; 
            cursor: pointer; 
            transition: all var(--transition-speed) var(--transition-ease); 
            display: flex; 
            align-items: center; 
            gap: 10px; 
            box-shadow: 0 4px 20px rgba(198, 69, 249, 0.3); 
        }
        
        .new-prompt-btn:hover { 
            transform: translateY(-3px) scale(1.05); 
            box-shadow: 0 12px 40px rgba(198, 69, 249, 0.4); 
        }
        
        .content-view { 
            display: none; 
            animation: fadeIn 0.6s var(--transition-ease); 
            flex-grow: 1; 
            min-height: 0; 
            overflow-y: auto; 
        }
        
        .content-view.active { 
            display: flex; 
            flex-direction: column; 
        }
        
        @keyframes fadeIn { 
            from { 
                opacity: 0; 
                transform: translateY(30px); 
            } 
            to { 
                opacity: 1; 
                transform: translateY(0); 
            } 
        }
        
        .card { 
            background: var(--glass-bg); 
            backdrop-filter: blur(24px); 
            -webkit-backdrop-filter: blur(24px); 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-card); 
            padding: 30px; 
            box-shadow: var(--glass-shadow); 
            transition: all var(--transition-speed) var(--transition-ease); 
        }
        
        .card:hover { 
            transform: translateY(-4px); 
            background: var(--glass-bg-hover); 
            box-shadow: var(--glass-hover-shadow); 
        }
        
        .welcome-card { 
            background: var(--glass-bg); 
            color: var(--color-text-primary) !important; 
            text-align: left; 
            border: none; 
            box-shadow: 0 8px 32px rgba(198, 69, 249, 0.25); 
            margin-bottom: 24px; 
        }
        
        .welcome-card:hover { 
            transform: translateY(-6px) scale(1.02); 
            box-shadow: 0 16px 48px rgba(198, 69, 249, 0.35); 
        }
        
        .welcome-card h1 { 
            font-family: var(--font-header); 
            font-size: 2.2rem; 
            font-weight: 700; 
            margin-bottom: 12px; 
            letter-spacing: -0.5px; 
        }
        
        .welcome-card p { 
            font-size: 1.1rem; 
            opacity: 0.9; 
            font-weight: 400; 
        }
        
        .section-title { 
            font-family: var(--font-header); 
            font-size: 1.8rem; 
            font-weight: 600; 
            margin-bottom: 24px; 
            display: flex; 
            align-items: center; 
            gap: 12px; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); 
            -webkit-background-clip: text; 
            -webkit-text-fill-color: transparent; 
            letter-spacing: -0.5px;
        }
        
        .section-title i { 
            color: var(--color-primary); 
            background: none; 
            -webkit-text-fill-color: var(--color-primary);
        }
        
        .dashboard-content { 
            display: flex; 
            flex-direction: column; 
            gap: 30px; 
        }
        
        .activity-chart {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(320px, 1fr));
            gap: 24px;
            margin-top: 20px;
        }

        .chart-item {
            background: var(--glass-bg-toolbar);
            backdrop-filter: blur(20px);
            -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 24px;
            text-align: center;
            transition: all var(--transition-speed) var(--transition-ease);
            display: flex;
            flex-direction: column;
            min-height: 320px; /* Adjusted for charts */
        }

        .chart-item:hover {
            transform: translateY(-4px);
            background: var(--glass-bg-hover);
            box-shadow: var(--glass-hover-shadow);
        }

        .chart-item h4 { /* Este H4 es de AG Charts, los de ECharts no lo usan */
            font-family: var(--font-header);
            font-size: 1.2rem;
            font-weight: 600;
            margin-bottom: 16px;
            color: var(--color-text-primary);
        }
        
        .chart-item > div[id^="chart"], 
        .chart-item > div[id$="Chart"] {
            flex-grow: 1; 
            min-height: 200px; 
            width: 100%;
        }

        .chart-item .metric {
            font-size: 2.5rem;
            font-weight: 800;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin: 16px 0;
        }

        .chart-item p {
            margin-top: auto;
            font-size: 0.95rem;
            color: var(--color-text-secondary);
            font-weight: 500;
            padding-top: 10px; 
        }
        
        .prompts-toolbar, .history-toolbar { 
            background: var(--glass-bg-toolbar); 
            backdrop-filter: blur(20px); 
            -webkit-backdrop-filter: blur(20px); 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-element); 
            padding: 20px; 
            display: flex; 
            justify-content: space-between; 
            align-items: center; 
            flex-wrap: wrap; 
            gap: 16px; 
            margin-bottom: 24px; 
        }
        
        .prompts-search, .history-search { 
            position: relative; 
            flex: 1; 
            min-width: 250px; 
        }
        
        .prompts-search i, .history-search i { 
            position: absolute; 
            left: 16px; 
            top: 50%; 
            transform: translateY(-50%); 
            color: var(--color-text-muted); 
        }
        
        .prompts-search input, .history-search input { 
            width: 100%; 
            padding: 12px 16px 12px 48px; 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-element); 
            background: var(--glass-bg); 
            backdrop-filter: blur(12px); 
            color: var(--color-text-primary); 
            font-family: var(--font-body); 
            transition: all var(--transition-speed) var(--transition-ease); 
        }
        
        .prompts-search input:focus, .history-search input:focus { 
            outline: none; 
            border-color: var(--color-primary); 
            background: var(--glass-bg-hover); 
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1); 
        }
        
        .prompts-filter, .history-filter { 
            display: flex; 
            gap: 8px; 
        }
        
        .filter-btn { 
            padding: 10px 20px; 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-pill); 
            background: var(--glass-bg); 
            backdrop-filter: blur(12px); 
            color: var(--color-text-primary); 
            font-weight: 500; 
            cursor: pointer; 
            transition: all var(--transition-speed) var(--transition-ease); 
        }
        
        .filter-btn:hover, .filter-btn.active { 
            background: var(--color-hover-accent); 
            color: var(--color-primary); 
            border-color: var(--color-primary); 
        }
        
        .prompts-list, .history-list { 
            display: grid; 
            grid-template-columns: repeat(auto-fill, minmax(380px, 1fr)); 
            gap: 20px; 
        }
        
        .prompt-card, .history-card { 
            background: var(--glass-bg); 
            backdrop-filter: blur(24px); 
            -webkit-backdrop-filter: blur(24px); 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-card); 
            padding: 24px; 
            transition: all var(--transition-speed) var(--transition-ease); 
            display: flex; 
            flex-direction: column; 
            gap: 16px; 
        }
        
        .prompt-card:hover, .history-card:hover { 
            transform: translateY(-4px); 
            background: var(--glass-bg-hover); 
            box-shadow: var(--glass-hover-shadow); 
        }
        
        .prompt-card-header, .history-card-header { 
            display: flex; 
            justify-content: space-between; 
            align-items: flex-start; 
        }
        
        .prompt-card-header h3, .history-card-header h3 { 
            font-family: var(--font-header); 
            font-size: 1.2rem; 
            font-weight: 600; 
            color: var(--color-text-primary); 
            margin: 0; 
        }
        
        .history-date { 
            font-size: 0.85rem; 
            color: var(--color-text-muted); 
            background: var(--glass-bg-toolbar); 
            padding: 4px 12px; 
            border-radius: var(--border-radius-pill); 
        }
        
        .prompt-card-meta, .history-card-meta { 
            display: flex; 
            gap: 16px; 
            flex-wrap: wrap; 
        }
        
        .prompt-card-meta span, .history-card-meta span { 
            font-size: 0.8rem; 
            color: var(--color-text-muted); 
            display: flex; 
            align-items: center; 
            gap: 6px; 
        }
        
        .prompt-card p, .history-card p { 
            color: var(--color-text-secondary); 
            line-height: 1.6; 
            flex: 1; 
        }
        
        .prompt-card-actions, .history-card-actions { 
            display: flex; 
            gap: 10px; 
            margin-top: auto; 
        }
        
        .action-btn { 
            padding: 8px 16px; 
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-element); 
            background: var(--glass-bg-toolbar); 
            backdrop-filter: blur(12px); 
            color: var(--color-text-primary); 
            font-size: 0.85rem; 
            cursor: pointer; 
            transition: all var(--transition-speed) var(--transition-ease); 
            display: flex; 
            align-items: center; 
            gap: 6px; 
        }
        
        .action-btn:hover { 
            background: var(--color-hover-accent); 
            color: var(--color-primary); 
            border-color: var(--color-primary); 
            transform: translateY(-2px); 
        }
        
        .action-btn.primary { 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); 
            color: white; 
            border: none; 
        }
        
        .action-btn.primary:hover { 
            transform: translateY(-2px) scale(1.05); 
            box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3); 
        }

        /* Settings Styles - CORREGIDOS PARA EVITAR SOMBRAS CORTADAS */
        .settings-section {
            background: var(--glass-bg);
            backdrop-filter: blur(24px);
            -webkit-backdrop-filter: blur(24px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            padding: 40px; /* Aumentado el padding para dar más espacio */
            box-shadow: var(--glass-shadow);
            margin-bottom: 40px; /* Aumentado el margen para evitar superposición */
            overflow: visible; /* CRÍTICO: Permite que las sombras se rendericen fuera del contenedor */
            position: relative; /* Establece contexto de apilamiento */
            z-index: 1; /* Contexto base */
        }

        .settings-section h3 {
            font-family: var(--font-header);
            font-size: 1.4rem;
            font-weight: 600;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            margin-bottom: 30px; /* Aumentado para dar más espacio */
        }

        /* SOLUCIÓN PRINCIPAL: Aumentar espaciado y mejorar gestión de sombras */
        .form-group { 
            margin-bottom: 35px; /* Aumentado significativamente para evitar superposición */
            position: relative; /* Para gestionar z-index de sombras */
        }

        .form-group:last-child {
            margin-bottom: 20px; /* Menor margen para el último elemento */
        }

        .form-group label { 
            display: block; 
            margin-bottom: 10px; /* Ligeramente aumentado */
            font-weight: 600; 
            color: var(--color-text-primary); 
        }

        /* Inputs con sombras mejoradas y no cortadas */
        .form-group input[type="text"], 
        .form-group input[type="email"], 
        .form-group input[type="password"], 
        .form-group select { 
            width: 100%; 
            padding: 14px 18px; /* Ligeramente aumentado */
            border: 1px solid var(--glass-border); 
            border-radius: var(--border-radius-element); 
            background: var(--glass-bg-toolbar); 
            backdrop-filter: blur(12px); 
            color: var(--color-text-primary); 
            font-family: var(--font-body); 
            transition: all var(--transition-speed) var(--transition-ease); 
            /* Sombra suave que no se cortará */
            box-shadow: 0 2px 8px rgba(13, 4, 37, 0.08), 0 1px 3px rgba(13, 4, 37, 0.1); 
            position: relative;
            z-index: 2; /* Asegura que esté por encima de otros elementos */
        }

        .form-group input[type="checkbox"] { 
            margin-right: 10px; /* Ligeramente aumentado */
            accent-color: var(--color-primary); 
            transform: scale(1.1); /* Hace el checkbox ligeramente más grande */
        }

        /* THE FOLLOWING LINES WERE CAUSING THE CONFLICT AND SHOULD BE REMOVED */
        /* .form-group { margin-bottom: 20px; } */ /* <-- REMOVE THIS LINE */
        /* .form-group label { display: block; margin-bottom: 8px; font-weight: 600; color: var(--color-text-primary); } */ /* <-- REMOVE THIS LINE */
        /* .form-group input[type="text"], .form-group input[type="email"], .form-group input[type="password"], .form-group select { width: 100%; padding: 12px 16px; border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); background: var(--glass-bg-toolbar); backdrop-filter: blur(12px); color: var(--color-text-primary); font-family: var(--font-body); transition: all var(--transition-speed) var(--transition-ease); } */ /* <-- REMOVE THIS LINE */
        /* .form-group input[type="checkbox"] { margin-right: 8px; accent-color: var(--color-primary); } */ /* <-- REMOVE THIS LINE */
        
        /* These focus styles are fine to keep, as they apply to a different state and don't conflict with the base shadow display */
        .form-group input[type="text"]:focus, .form-group input[type="email"]:focus, .form-group input[type="password"]:focus, .form-group select:focus { 
            outline: none; 
            border-color: var(--color-primary); 
            background: var(--glass-bg-hover); 
            box-shadow: 0 0 0 3px rgba(198, 69, 249, 0.1); 
        }
        
        .settings-actions { display: flex; gap: 12px; margin-top: 24px; justify-content: flex-end;}
        /* ... (rest of the CSS) ... */
        .settings-btn { padding: 12px 24px; border-radius: var(--border-radius-element); font-weight: 600; cursor: pointer; transition: all var(--transition-speed) var(--transition-ease); display: flex; align-items: center; gap: 8px; }
        .settings-btn.save { background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); color: white; border: none; }
        .settings-btn.save:hover { transform: translateY(-2px) scale(1.05); box-shadow: 0 8px 24px rgba(198, 69, 249, 0.3); }
        .settings-btn.cancel { background: var(--glass-bg); border: 1px solid var(--glass-border); color: var(--color-text-primary); }
        .settings-btn.cancel:hover { background: var(--glass-bg-hover); color: var(--color-text-muted); }
        @media (max-width: 1200px) { .activity-chart { grid-template-columns: repeat(auto-fit, minmax(280px, 1fr)); } }
        @media (max-width: 992px) { .activity-chart { grid-template-columns: repeat(auto-fit, minmax(260px, 1fr)); } }
        @media (max-width: 768px) { .sidebar { transform: translateX(-100%); width: 260px; } .sidebar.active { transform: translateX(0); } .main-content { margin-left: 0; padding: 20px; } .menu-toggle { display: block; } .header { padding: 16px 20px; } .header-title { font-size: 1.4rem; } .welcome-card h1 { font-size: 1.8rem; } .activity-chart { grid-template-columns: repeat(auto-fit, minmax(250px, 1fr)); } .prompts-toolbar { flex-direction: column; align-items: stretch; } .prompts-search { min-width: auto; } .prompts-filter { justify-content: center; } .prompts-list, .history-list { grid-template-columns: 1fr; } }
        @media (max-width: 576px) { .activity-chart { grid-template-columns: 1fr; } }
        @media (max-width: 480px) { .main-content { padding: 15px; } .header { padding: 12px 15px; } .card { padding: 20px; } .welcome-card h1 { font-size: 1.5rem; } .section-title { font-size: 1.5rem; } .new-prompt-btn { padding: 12px 20px; font-size: 0.85rem; } }
        .loading { display: inline-block; width: 20px; height: 20px; border: 3px solid rgba(198, 69, 249, 0.3); border-radius: 50%; border-top-color: var(--color-primary); animation: spin 1s ease-in-out infinite; }
        @keyframes spin { to { transform: rotate(360deg); } }
        .notification { position: fixed; top: 20px; right: 20px; background: var(--glass-bg-strong); backdrop-filter: blur(24px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); padding: 16px 24px; box-shadow: var(--glass-hover-shadow); z-index: 10000; opacity: 0; transform: translateX(100%); transition: all var(--transition-speed) var(--transition-ease); }
        .notification.show { opacity: 1; transform: translateX(0); }
        .notification.success { border-left: 4px solid #10B981; } .notification.error { border-left: 4px solid #EF4444; } .notification.info { border-left: 4px solid var(--color-primary); }
        .hidden { display: none !important; }

        /* === ESTILOS DEL PLAYGROUND INTEGRADO === */
        #playground .playground-layout-grid { display: grid; grid-template-columns: 1fr; gap: var(--playground-gap); height: 100%; overflow: auto; }
        #playground .playground-prompt-editor-area { display: flex; flex-direction: column; min-height: 0; }
        #playground .main-container { padding: 0; width: 100%; flex-grow: 1; display: flex; flex-direction: column; align-items: stretch; background: var(--glass-bg); border-radius: var(--border-radius-card); box-shadow: var(--glass-shadow); overflow: hidden; }
        #playground .header-section { text-align: center; padding: 20px 15px 15px; z-index: 5; flex-shrink: 0; }
        @keyframes synchronizedNeonShine { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }
        #playground .glass-title { font-size: clamp(1.5rem, 3vw, 2rem); margin-bottom: 8px; font-family: var(--font-header); font-weight: 600; background: linear-gradient(135deg, var(--color-primary), var(--color-secondary), var(--color-primary), var(--color-secondary), var(--color-primary)); background-size: 300% 100%; -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text; letter-spacing: -0.02em; line-height: 1.1; animation: synchronizedNeonShine 5s linear infinite; }
        #playground .glass-subtitle { font-size: clamp(0.8rem, 1.8vw, 0.9rem); font-weight: 500; color: var(--color-text-secondary); max-width: 500px; margin: 0 auto; line-height: 1.6; }
        #playground .glass-search-container { width: auto; margin: 0 20px; max-width: none; position: relative; z-index: 10; flex-grow: 1; display: flex; flex-direction: column; }
        #playground .glass-search-card { background: var(--glass-bg-strong); backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); box-shadow: var(--glass-shadow); transition: all var(--transition-speed) var(--transition-ease); overflow: visible; position: relative; display: flex; flex-direction: column; flex-grow: 1; }
        #playground .glass-search-card::before { content: ''; position: absolute; top: -1px; left: -1px; right: -1px; bottom: -1px; border-radius: inherit; background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); padding: 1.5px; -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0); -webkit-mask-composite: destination-out; mask-composite: exclude; opacity: 0; transition: opacity var(--transition-speed) var(--transition-ease); pointer-events: none; z-index: -1; }
        #playground .glass-search-card.focused { transform: translateY(-3px); box-shadow: var(--glass-hover-shadow); border-color: transparent; }
        #playground .glass-search-card.focused::before { opacity: 0.7; }
        #playground .glass-search-input { padding: 20px 20px 14px; font-size: 0.9rem; min-height: 100px; flex-grow: 1; width: 100%; background: transparent; border: none; outline: none; font-family: var(--font-body); font-weight: 500; color: var(--color-text-primary); resize: none; line-height: 1.5; }
        #playground .glass-search-input::placeholder { color: var(--color-placeholder); font-weight: 400; }
        #playground .glass-toolbar { padding: 10px 15px; border-top: 1px solid var(--glass-border); flex-shrink: 0; display: flex; justify-content: space-between; align-items: center; background: var(--glass-bg-toolbar); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); }
        #playground .glass-model-selector-container { position: relative; }
        #playground .glass-model-btn { display: flex; align-items: center; gap: 8px; background: rgba(255, 255, 255, 0.1); backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); padding: 8px 16px; font-size: 0.75rem; font-weight: 600; color: var(--color-text-primary); cursor: pointer; transition: all var(--transition-speed) var(--transition-ease); }
        #playground .glass-model-btn:hover { background: rgba(255, 255, 255, 0.2); border-color: var(--glass-border-highlight); transform: translateY(-1px); box-shadow: 0 2px 10px rgba(13,4,37, 0.08); }
        #playground .glass-model-btn .fa-chevron-down { transition: transform var(--transition-speed) var(--transition-ease); }
        #playground .glass-model-btn.open .fa-chevron-down { transform: rotate(180deg); }
        #playground .glass-model-btn > i:first-child { color: var(--color-secondary); font-size:0.9em; }
        #playground .glass-search-actions { display: flex; align-items: center; gap: 8px; }
        #playground .glass-action-btn { width: 36px; height: 36px; display: flex; align-items: center; justify-content: center; background: rgba(255, 255, 255, 0.08); backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px); border: 1px solid var(--glass-border); border-radius: 50%; color: var(--color-text-secondary); cursor: pointer; transition: all var(--transition-speed) var(--transition-ease); font-size: 0.9rem; }
        #playground .glass-action-btn:hover { background: var(--color-hover-accent); color: var(--color-primary); border-color: var(--color-primary); transform: translateY(-2px) scale(1.08); box-shadow: 0 4px 15px color-mix(in srgb, var(--color-primary) 20%, transparent); }
        #playground .glass-action-btn:active { transform: translateY(0px) scale(1); background: var(--color-active-accent); }
        #playground .pulse-effect { animation: pulseGlowPlayground 1.8s ease-in-out infinite; } 
        @keyframes pulseGlowPlayground { 0%, 100% { box-shadow: 0 0 10px color-mix(in srgb, var(--color-primary) 15%, transparent), 0 0 3px var(--color-primary); } 50% { box-shadow: 0 0 20px color-mix(in srgb, var(--color-primary) 35%, transparent), 0 0 6px var(--color-primary); } }
        #playground .glass-content-area { padding: 15px; max-height: 300px; flex-shrink: 1; min-height: 50px; background: rgba(255, 255, 255, 0.03); backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px); border-top: 1px solid var(--glass-border); overflow-y: auto; }
        #playground .glass-content-area::-webkit-scrollbar { width: 6px; } #playground .glass-content-area::-webkit-scrollbar-track { background: transparent; } #playground .glass-content-area::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.2); border-radius: 3px;} #playground .glass-content-area::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.3); }
        #playground .glass-loading-indicator { display: flex; align-items: center; justify-content: center; padding: 20px; color: var(--color-text-secondary); font-size: 0.85rem; font-weight: 500; }
        #playground .glass-spinner { width: 18px; height: 18px; border: 2px solid color-mix(in srgb, var(--color-primary) 20%, transparent); border-top-color: var(--color-primary); border-radius: 50%; animation: spinPlayground 0.8s linear infinite; margin-right: 10px; }
        @keyframes spinPlayground { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } 
        #playground .suggestions-panel { animation: fadeInUpPlayground 0.4s var(--transition-ease) forwards; } 
        #playground .simple-suggestions-list { display: flex; flex-direction: column; gap: 8px; } 
        #playground .simple-suggestion-item { background: rgba(255, 255, 255, 0.07); backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); padding: 12px 16px; cursor: pointer; transition: all var(--transition-speed) var(--transition-ease); font-size: 0.85rem; color: var(--color-text-primary); line-height: 1.5; }
        #playground .simple-suggestion-item:hover { background: rgba(255, 255, 255, 0.16); border-color: var(--glass-border-highlight); transform: translateY(-2px); box-shadow: 0 5px 18px rgba(13,4,37, 0.07); }
        #playground .simple-suggestion-item strong { font-weight: 700; color: var(--color-primary); }
        #playground .enhanced-prompt-list { display: flex; flex-direction: column; gap: 12px; } 
        #playground .enhanced-prompt-card { background: rgba(255, 255, 255, 0.06); backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); padding: 16px; transition: all var(--transition-speed) var(--transition-ease); position: relative; }
        #playground .enhanced-prompt-card:hover { background: rgba(255, 255, 255, 0.12); border-color: var(--glass-border-highlight); transform: translateY(-3px); box-shadow: 0 10px 30px rgba(13,4,37, 0.1); }
        #playground .enhanced-card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
        #playground .enhanced-card-title { font-size: 1rem; font-weight: 600; color: var(--color-text-primary); line-height: 1.3; }
        #playground .glass-star-btn { background: rgba(255, 255, 255, 0.1); border: 1px solid var(--glass-border); border-radius: 50%; width: 28px; height: 28px; display: flex; align-items: center; justify-content: center; cursor: pointer; transition: all var(--transition-speed) var(--transition-ease); color: var(--color-text-muted); font-size: 0.8rem; }
        #playground .glass-star-btn:hover { background: color-mix(in srgb, var(--color-primary) 15%, transparent); color: var(--color-primary); transform: scale(1.1); }
        #playground .glass-star-btn.favorited { background: color-mix(in srgb, var(--color-primary) 15%, transparent); color: var(--color-star-active); border-color: var(--color-primary); }
        #playground .glass-star-btn.favorited i { font-weight: 900; }
        #playground .enhanced-prompt-meta-minimal { display: flex; flex-wrap: wrap; gap: 5px 10px; margin-bottom: 8px; font-size: 0.7rem; color: var(--color-text-secondary); }
        #playground .meta-item { display: flex; align-items: center; gap: 4px; } 
        #playground .meta-item i { font-size: 0.9em; }
        #playground .full-prompt-display { font-size: 0.8rem; line-height: 1.6; color: var(--color-text-secondary); margin-bottom: 10px; white-space: pre-wrap; }
        #playground .prompt-heading { font-weight: 600; color: var(--color-text-primary); display: block; margin-top: 6px; }
        #playground .full-prompt-display br + .prompt-heading { margin-top: 6px; }
        #playground .select-prompt-button { display: inline-flex; align-items: center; gap: 6px; padding: 7px 14px; border-radius: var(--border-radius-pill); font-family: var(--font-body); font-size: 0.75rem; font-weight: 600; cursor: pointer; transition: all var(--transition-speed) var(--transition-ease); text-decoration: none; border: none; background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); color: var(--color-text-on-gradient); margin-top: 6px; }
        #playground .select-prompt-button:hover { transform: translateY(-2px) scale(1.02); box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent); }
        #playground .select-prompt-button i { font-size: 0.9em; }
        #playground .popular-prompts-section-title { color: var(--color-text-primary); margin-bottom: 12px; display: flex; align-items: center; gap: 8px; font-weight: 600; font-size: 1rem; }
        #playground .popular-prompts-section-title i { color: var(--color-primary); }
        #playground .glass-model-dropdown { position: absolute; top: calc(100% + 8px); left: 0; width: auto; min-width: 260px; max-width: 320px; background: var(--glass-bg); backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-element); box-shadow: 0 10px 30px rgba(13,4,37,0.1); opacity: 0; visibility: hidden; transform: translateY(-8px) scale(0.98); transition: all var(--transition-speed) var(--transition-ease); z-index: 1100; padding: 6px; }
        #playground .glass-model-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0) scale(1); }
        #playground .model-search-input-container { position: relative; margin-bottom: 10px; }
        #playground .model-search-input-container .fa-search { position: absolute; top: 50%; left: 10px; transform: translateY(-50%); color: var(--color-text-muted); font-size: 0.85em; }
        #playground #pgModelSearchInput { width: 100%; padding: 8px 10px 8px 30px; background: rgba(255, 255, 255, 0.08); backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px); border: 1px solid var(--glass-border); border-radius: calc(var(--border-radius-element) - 4px); font-family: var(--font-body); font-size: 0.8rem; color: var(--color-text-primary); outline: none; transition: border-color var(--transition-speed) var(--transition-ease); }
        #playground #pgModelSearchInput::placeholder { color: var(--color-placeholder); } #playground #pgModelSearchInput:focus { border-color: var(--glass-border-highlight); }
        #playground .model-list-ul { list-style: none; padding: 0; margin: 0; max-height: 220px; overflow-y: auto; }
        #playground .model-list-ul::-webkit-scrollbar { width: 5px; } #playground .model-list-ul::-webkit-scrollbar-track { background: transparent; } #playground .model-list-ul::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.25); border-radius: 3px;} #playground .model-list-ul::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.35); }
        #playground .model-list-ul li { padding: 8px 12px; color: var(--color-text-primary); font-weight: 500; font-size: 0.8rem; cursor: pointer; transition: background-color var(--transition-speed) var(--transition-ease); border-radius: calc(var(--border-radius-element) - 6px); display: flex; align-items: center; gap: 8px; margin-bottom: 3px; }
        #playground .model-list-ul li:last-child { margin-bottom: 0; } #playground .model-list-ul li:hover { background: var(--color-hover-accent); color: var(--color-primary); }
        #playground .model-list-ul li.selected-model-item { background-color: var(--color-active-accent); color: var(--color-primary); font-weight: 600; }
        #playground .model-list-ul li img, #playground .model-list-ul li .model-icon { width: 16px; height: 16px; object-fit: contain; flex-shrink: 0; }
        #playground .model-list-ul li .model-icon { font-size: 1em; text-align: center; }
        #playground .model-icon.google-blue { color: var(--google-blue); }

        #playground .glass-footer { padding: 15px; text-align: center; color: var(--color-text-muted); font-size: 0.75rem; width: 100%; flex-shrink: 0; border-top: 1px solid var(--glass-border); margin-top: auto; }
        #playground .glass-footer i.fa-heart { color: var(--color-primary); }
        @keyframes fadeInUpPlayground { from { opacity: 0; transform: translateY(15px) scale(0.99); } to { opacity: 1; transform: translateY(0) scale(1); } } 

        /* Knowledge Graph Area Styles */
        #playground .playground-knowledge-graph-area { background: var(--glass-bg); backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px); border: 1px solid var(--glass-border); border-radius: var(--border-radius-card); padding: 20px; box-shadow: var(--glass-shadow); display: flex; flex-direction: column; height: calc(100vh - var(--header-height) - var(--main-content-padding-top) - var(--main-content-padding-bottom) - 24px); min-height: 450px; }
        #playground .playground-knowledge-graph-area h3 { font-family: var(--font-header); font-size: 1.3rem; font-weight: 600; margin-bottom: 15px; background: linear-gradient(135deg, var(--color-primary), var(--color-secondary)); -webkit-background-clip: text; -webkit-text-fill-color: transparent; flex-shrink: 0; }
        #knowledgeGraphD3 { width: 100%; flex-grow: 1; min-height: 300px; overflow: visible; /* Para que las etiquetas no se corten */ }
        #knowledgeGraphD3 .d3-link { stroke: var(--kg-link-color); stroke-opacity: 0.6; stroke-width: 1.5px; }
        #knowledgeGraphD3 .d3-link-label { fill: var(--kg-link-label-color); font-family: var(--font-body); font-size: 9px; text-anchor: middle; dominant-baseline: central; pointer-events: none; }
        #knowledgeGraphD3 .d3-node circle { stroke-width: 0px; /* Sin borde en el círculo */ cursor: grab; transition: r 0.2s ease-out, filter 0.2s ease-out; }
        #knowledgeGraphD3 .d3-node circle:hover { filter: brightness(1.15); }
        #knowledgeGraphD3 .d3-node text { font-family: var(--font-body); fill: var(--color-text-primary); /* Color de texto del tema */ pointer-events: none; text-anchor: middle; dominant-baseline: middle; user-select: none; -webkit-user-select: none; font-weight: 500; }


        @media (min-width: 993px) { #playground .playground-layout-grid { grid-template-columns: minmax(450px, 1.2fr) 1fr; } }
        @media (max-width: 992px) { #playground .playground-layout-grid { grid-template-columns: 1fr; height: auto; } #playground .playground-knowledge-graph-area { height: 450px; min-height: 400px; } }
        @media (max-width: 768px) { #playground .playground-knowledge-graph-area { height: 400px; min-height: 350px; } #playground .glass-toolbar { flex-direction: row; gap: 10px; padding: 10px 15px; align-items: center; } #playground .glass-model-selector-container { width: auto; } #playground .glass-search-actions { justify-content: flex-end; width: auto; } }
        @media (max-width: 480px) { #playground .playground-knowledge-graph-area { height: 350px; min-height: 300px; } #playground .glass-action-btn { width: 32px; height: 32px; font-size: 0.8rem;} #playground .glass-search-actions { gap: 5px;} #playground .glass-toolbar { gap: 8px; padding: 8px 12px; } #playground .glass-model-btn { padding: 7px 12px; font-size: 0.7rem; } }
        
    </style>
</head>
<body>
    <div class="app-container">
        <!-- Sidebar -->
        <nav class="sidebar" id="sidebar">
            <div class="sidebar-logo">allhub</div>
            <div class="sidebar-nav">
                <a href="#" data-view="dashboard" class="nav-link active"><i class="fas fa-chart-line"></i> Dashboard</a>
                <a href="#" data-view="prompts" class="nav-link"><i class="fas fa-lightbulb"></i> My Prompts</a>
                <a href="#" data-view="history" class="nav-link"><i class="fas fa-history"></i> History</a>
                <a href="#" data-view="playground" class="nav-link"><i class="fas fa-code"></i> Playground</a>
                <a href="#" data-view="settings" class="nav-link"><i class="fas fa-cog"></i> Settings</a>
            </div>
            <div class="sidebar-footer">
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <div class="user-avatar">JD</div>
                        <span>John Doe</span>
                        <i class="fas fa-chevron-up"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user"></i> Profile</a>
                        <a href="#"><i class="fas fa-bell"></i> Notifications</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </nav>

        <!-- Main Content -->
        <main class="main-content">
            <!-- Header -->
            <header class="header">
                <div class="header-left">
                    <button class="menu-toggle" id="menuToggle"><i class="fas fa-bars"></i></button>
                    <h1 class="header-title" id="headerTitle">Dashboard</h1>
                </div>
                <div class="header-actions">
                    <button class="new-prompt-btn" id="newPromptGlobalBtn"><i class="fas fa-plus"></i> New Prompt</button>
                </div>
            </header>

            <!-- Dashboard View -->
            <div id="dashboard" class="content-view active">
                <div class="card welcome-card">
                    <h1>Welcome back, John!</h1>
                    <p>Ready to create something amazing today?</p>
                </div>

                <div class="dashboard-content">
                    <div class="activity-chart">
                        <div class="chart-item">
                            <div id="savedPromptsChart"></div>
                            <p>Saved Prompts: 27</p>
                        </div>
                        <div class="chart-item">
                            <div id="promptsUsedChart"></div>
                            <p>Prompts Used (Month): 152</p>
                        </div>
                        <div class="chart-item">
                            <div id="activeFavoritesChart"></div>
                            <p>Active Favorites: 8</p>
                        </div>
                        <div class="chart-item">
                            <div id="responseTimeChart"></div>
                            <p>Avg. Response Time: 1.6s</p>
                        </div>
                        <div class="chart-item">
                            <div id="userEngagementChart"></div>
                            <p>User Engagement: 85%</p>
                        </div>
                        <div class="chart-item">
                            <div id="promptQualityChart"></div>
                            <p>Prompt Quality Score: 92%</p>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Prompts View -->
            <div id="prompts" class="content-view">
                <h2 class="section-title"><i class="fas fa-lightbulb"></i> My Prompts</h2>
                <div class="prompts-toolbar">
                    <div class="prompts-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search prompts..." id="promptSearchAllhub">
                    </div>
                    <div class="prompts-filter">
                        <button class="filter-btn active" data-filter="all">All</button>
                        <button class="filter-btn" data-filter="favorites">Favorites</button>
                        <button class="filter-btn" data-filter="recent">Recent</button>
                    </div>
                </div>
                <div class="prompts-list" id="promptsListAllhub">
                    <div class="prompt-card">
                        <div class="prompt-card-header">
                            <h3>Creative Writing Assistant</h3>
                        </div>
                        <div class="prompt-card-meta">
                            <span><i class="fas fa-star"></i> 4.8</span>
                            <span><i class="fas fa-clock"></i> 2 days ago</span>
                            <span><i class="fas fa-tag"></i> Writing</span>
                        </div>
                        <p>A comprehensive prompt for generating creative stories, helping with character development, plot structure, and narrative flow.</p>
                        <div class="prompt-card-actions">
                            <button class="action-btn primary"><i class="fas fa-play"></i> Use Prompt</button>
                            <button class="action-btn"><i class="fas fa-edit"></i> Edit</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                            <button class="action-btn"><i class="fas fa-trash"></i> Delete</button>
                        </div>
                    </div>
                     <div class="prompt-card">
                        <div class="prompt-card-header">
                            <h3>Code Review Helper</h3>
                        </div>
                        <div class="prompt-card-meta">
                            <span><i class="fas fa-star"></i> 4.9</span>
                            <span><i class="fas fa-clock"></i> 1 week ago</span>
                            <span><i class="fas fa-tag"></i> Development</span>
                        </div>
                        <p>Analyze code quality, suggest improvements, and provide detailed feedback on programming best practices.</p>
                        <div class="prompt-card-actions">
                            <button class="action-btn primary"><i class="fas fa-play"></i> Use Prompt</button>
                            <button class="action-btn"><i class="fas fa-edit"></i> Edit</button>
                            <button class="action-btn"><i class="fas fa-share"></i> Share</button>
                            <button class="action-btn"><i class="fas fa-trash"></i> Delete</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- History View -->
            <div id="history" class="content-view">
                <h2 class="section-title"><i class="fas fa-history"></i> Conversation History</h2>
                 <div class="history-toolbar">
                    <div class="history-search">
                        <i class="fas fa-search"></i>
                        <input type="text" placeholder="Search history..." id="historySearchInput">
                    </div>
                    <div class="history-filter">
                        <button class="filter-btn active" data-filter="all-history">All</button>
                        <button class="filter-btn" data-filter="last-7-days">Last 7 Days</button>
                        <button class="filter-btn" data-filter="last-30-days">Last 30 Days</button>
                    </div>
                </div>
                <div class="history-list" id="historyListContainer">
                    <div class="history-card">
                        <div class="history-card-header">
                            <h3>Product Launch Strategy</h3>
                            <span class="history-date">Today</span>
                        </div>
                        <div class="history-card-meta">
                            <span><i class="fas fa-robot"></i> Model: GPT-4</span>
                            <span><i class="fas fa-clock"></i> 45 min</span>
                            <span><i class="fas fa-comment-dots"></i> 23 messages</span>
                        </div>
                        <p>Developed a comprehensive go-to-market strategy for a new SaaS product, including pricing models and customer acquisition channels. Focused on digital marketing and content strategy.</p>
                        <div class="history-card-actions">
                            <button class="action-btn primary"><i class="fas fa-play-circle"></i> Continue</button>
                            <button class="action-btn"><i class="fas fa-download"></i> Export</button>
                            <button class="action-btn"><i class="fas fa-share-alt"></i> Share</button>
                            <button class="action-btn"><i class="fas fa-trash-alt"></i> Delete</button>
                        </div>
                    </div>
                     <div class="history-card">
                        <div class="history-card-header">
                            <h3>Code Optimization Review</h3>
                            <span class="history-date">Yesterday</span>
                        </div>
                        <div class="history-card-meta">
                            <span><i class="fas fa-robot"></i> Model: Claude 3</span>
                            <span><i class="fas fa-clock"></i> 30 min</span>
                            <span><i class="fas fa-comment-dots"></i> 15 messages</span>
                        </div>
                        <p>Analyzed React component performance issues and implemented optimization strategies resulting in 40% faster load times. Discussed memoization and virtual DOM diffing.</p>
                        <div class="history-card-actions">
                            <button class="action-btn primary"><i class="fas fa-play-circle"></i> Continue</button>
                            <button class="action-btn"><i class="fas fa-download"></i> Export</button>
                            <button class="action-btn"><i class="fas fa-share-alt"></i> Share</button>
                            <button class="action-btn"><i class="fas fa-trash-alt"></i> Delete</button>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Playground View - NUEVA ESTRUCTURA -->
            <div id="playground" class="content-view">
                <div class="playground-layout-grid">
                    <div class="playground-prompt-editor-area">
                        <main class="main-container"> 
                            <section class="header-section">
                                <h1 class="glass-title">Prompt like a pro</h1>
                                <p class="glass-subtitle">Craft the perfect prompts with intelligent suggestions</p>
                            </section>
                            <div class="glass-search-container" id="pgSearchAreaContainer">
                                <div class="glass-search-card" id="pgSearchBarCard">
                                    <textarea class="glass-search-input" id="pgSearchInput" placeholder="Type your prompt here..."></textarea>
                                    <div class="glass-toolbar">
                                        <div class="glass-model-selector-container">
                                            <button class="glass-model-btn" id="pgModelSelectorBtn">
                                                <i class="fas fa-cogs"></i><span id="pgCurrentModelName">Model</span><i class="fas fa-chevron-down"></i>
                                            </button>
                                        </div>
                                        <div class="glass-search-actions" id="pgSearchActionsContainer">
                                            <button class="glass-action-btn" id="pgMicBtn" title="Voice Input"><i class="fas fa-microphone"></i></button>
                                            <button class="glass-action-btn" id="pgGuidedCreateBtn" title="Guided Creation"><i class="fas fa-wand-magic-sparkles"></i></button>
                                            <button class="glass-action-btn" id="pgCopyBtn" title="Copy Prompt"><i class="fas fa-copy"></i></button>
                                            <button class="glass-action-btn pulse-effect" id="pgSendBtn" title="Send Prompt"><i class="fas fa-paper-plane"></i></button>
                                        </div>
                                    </div>
                                    <div class="glass-content-area hidden" id="pgDynamicContentArea">
                                        <div class="glass-loading-indicator hidden" id="pgAnalyzingIndicator"><div class="glass-spinner"></div>Analyzing...</div>
                                        <div class="suggestions-panel hidden" id="pgSimplePromptSuggestionsContainer"><div class="simple-suggestions-list"></div></div>
                                        <div class="suggestions-panel hidden" id="pgEnhancedPromptSuggestionsContainer"><div class="enhanced-prompt-list"></div></div>
                                        <div class="suggestions-panel hidden" id="pgFavoritePromptsSection">
                                            <h3 class="popular-prompts-section-title"><i class="fas fa-star"></i>Popular Prompts</h3>
                                            <div class="enhanced-prompt-list" id="pgFavoritePromptsList"></div>
                                        </div>
                                    </div>
                                </div>
                                <div class="glass-model-dropdown hidden" id="pgModelDropdownList">
                                    <div class="model-search-input-container">
                                        <i class="fas fa-search"></i><input type="text" id="pgModelSearchInput" placeholder="Search models">
                                    </div>
                                    <ul class="model-list-ul"></ul>
                                </div>
                            </div>
                            <footer class="glass-footer">Copyright © <span id="pgCopyrightYear">2024</span> All Hub. Crafted with <i class="fas fa-heart"></i> & AI.</footer>
                        </main>
                    </div>
                    <div class="playground-knowledge-graph-area">
                        <h3>Prompting Knowledge Graph</h3>
                        <div id="knowledgeGraphD3"></div> 
                    </div>
                </div>
            </div>

            <!-- Settings View -->
            <div id="settings" class="content-view">
                <h2 class="section-title"><i class="fas fa-cog"></i> Settings</h2>
                <div class="settings-section">
                    <h3>Profile Settings</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label for="settingsFullName">Full Name</label>
                            <input type="text" id="settingsFullName" value="John Doe">
                        </div>
                        <div class="form-group">
                            <label for="settingsEmail">Email Address</label>
                            <input type="email" id="settingsEmail" value="<EMAIL>">
                        </div>
                         <div class="form-group">
                            <label for="settingsPassword">New Password (leave blank to keep current)</label>
                            <input type="password" id="settingsPassword" placeholder="Enter new password">
                        </div>
                        <div class="form-group">
                            <label for="settingsCompany">Company</label>
                            <input type="text" id="settingsCompany" value="Acme Corp">
                        </div>
                        <div class="form-group">
                            <label for="settingsLanguage">Language</label>
                            <select id="settingsLanguage">
                                <option value="en" selected>English</option>
                                <option value="es">Español</option>
                                <option value="fr">Français</option>
                            </select>
                        </div>
                        <div class="settings-actions">
                            <button type="button" class="settings-btn save"><i class="fas fa-save"></i> Save Changes</button>
                            <button type="reset" class="settings-btn cancel"><i class="fas fa-times"></i> Cancel</button>
                        </div>
                    </form>
                </div>

                <div class="settings-section">
                    <h3>API Configuration</h3>
                     <form class="settings-form">
                        <div class="form-group">
                            <label for="settingsApiKey">API Key</label>
                            <input type="password" id="settingsApiKey" value="sk-...">
                        </div>
                        <div class="form-group">
                            <label for="settingsDefaultModel">Default Model</label>
                             <select id="settingsDefaultModel">
                                <option value="openai-gpt-4" selected>OpenAI: GPT-4</option>
                                <option value="anthropic-claude-3">Anthropic: Claude 3</option>
                                <option value="google-gemini-1.5">Google: Gemini 1.5</option>
                            </select>
                        </div>
                        <div class="settings-actions">
                            <button type="button" class="settings-btn save"><i class="fas fa-save"></i> Save API Settings</button>
                        </div>
                    </form>
                </div>
                 <div class="settings-section">
                    <h3>Notification Preferences</h3>
                    <form class="settings-form">
                        <div class="form-group">
                            <label for="emailNotifications">
                                <input type="checkbox" id="emailNotifications" checked> Email Notifications
                            </label>
                        </div>
                        <div class="form-group">
                            <label for="appNotifications">
                                <input type="checkbox" id="appNotifications"> In-App Notifications
                            </label>
                        </div>
                         <div class="settings-actions">
                            <button type="button" class="settings-btn save"><i class="fas fa-save"></i> Save Preferences</button>
                        </div>
                    </form>
                </div>
            </div>
        </main>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/ag-charts-community@latest/dist/ag-charts-community.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/echarts@5.5.0/dist/echarts.min.js"></script>
    <script src="https://d3js.org/d3.v7.min.js"></script>
    <script>
        const navLinks = document.querySelectorAll('.nav-link');
        const contentViews = document.querySelectorAll('.content-view');
        const headerTitle = document.getElementById('headerTitle');
        const menuToggle = document.getElementById('menuToggle');
        const sidebar = document.getElementById('sidebar');
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');

        const viewTitles = {
            dashboard: 'Dashboard',
            prompts: 'My Prompts',
            history: 'Conversation History',
            playground: 'Playground', 
            settings: 'Settings'
        };

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const targetViewId = link.getAttribute('data-view');
                
                navLinks.forEach(nav => nav.classList.remove('active'));
                link.classList.add('active');
                
                contentViews.forEach(view => view.classList.remove('active'));
                const targetElement = document.getElementById(targetViewId);

                if (targetElement) {
                    targetElement.classList.add('active');
                    headerTitle.textContent = viewTitles[targetViewId];

                    if (targetViewId === 'playground') {
                        const graphContainer = document.getElementById('knowledgeGraphD3');
                        if (graphContainer && !graphContainer.classList.contains('d3-initialized')) {
                            initKnowledgeGraphD3(); 
                        }
                    }
                } else {
                    console.error(`View element with ID "${targetViewId}" not found.`);
                }
                
                if (window.innerWidth <= 768 && sidebar) {
                    sidebar.classList.remove('active');
                }
            });
        });

        if(menuToggle) menuToggle.addEventListener('click', () => sidebar && sidebar.classList.toggle('active'));
        if(userMenuBtn) userMenuBtn.addEventListener('click', (e) => { e.stopPropagation(); userDropdown && userDropdown.classList.toggle('visible'); });
        
        document.addEventListener('click', (event) => { 
            if (userDropdown && userMenuBtn && !userMenuBtn.contains(event.target) && !userDropdown.contains(event.target)) {
                userDropdown.classList.remove('visible');
            }
        });
        if(userDropdown) userDropdown.addEventListener('click', (e) => e.stopPropagation());


        const filterBtns = document.querySelectorAll('.prompts-filter .filter-btn, .history-filter .filter-btn');
        filterBtns.forEach(btn => {
            btn.addEventListener('click', () => {
                const filterGroup = btn.closest('.prompts-filter, .history-filter');
                if(filterGroup) {
                    filterGroup.querySelectorAll('.filter-btn').forEach(b => b.classList.remove('active'));
                }
                btn.classList.add('active');
                showNotification(`Filtered by: ${btn.textContent.trim()}`, 'info');
            });
        });

        const promptSearchAllhub = document.getElementById('promptSearchAllhub');
        if (promptSearchAllhub) promptSearchAllhub.addEventListener('input', (e) => console.log('Searching My Prompts:', e.target.value.toLowerCase()));
        
        const historySearchInput = document.getElementById('historySearchInput');
        if (historySearchInput) historySearchInput.addEventListener('input', (e) => console.log('Searching History:', e.target.value.toLowerCase()));


        const settingsForms = document.querySelectorAll('#settings .settings-form');
        settingsForms.forEach(form => {
            const saveBtn = form.querySelector('.settings-btn.save');
            if (saveBtn) {
                saveBtn.addEventListener('click', () => {
                    const originalText = saveBtn.innerHTML;
                    saveBtn.innerHTML = '<div class="loading"></div> Saving...';
                    saveBtn.disabled = true;
                    setTimeout(() => {
                        saveBtn.innerHTML = originalText;
                        saveBtn.disabled = false;
                        showNotification('Settings saved successfully!', 'success');
                    }, 1500);
                });
            }
        });


        const newPromptGlobalBtn = document.getElementById('newPromptGlobalBtn');
        if (newPromptGlobalBtn) {
            newPromptGlobalBtn.addEventListener('click', () => {
                const playgroundLink = document.querySelector('[data-view="playground"]');
                if (playgroundLink) playgroundLink.click(); 
                showNotification('Switched to Playground!', 'info');
            });
        }

        document.addEventListener('click', (e) => {
            const actionButton = e.target.closest('.action-btn');
            if (actionButton && !actionButton.closest('#playground')) { 
                const card = actionButton.closest('.prompt-card, .history-card');
                const title = card ? card.querySelector('h3').textContent : 'Item';
                let actionText = actionButton.textContent.trim();
                if (actionButton.querySelector('i')) { 
                    actionText = actionButton.title || actionButton.querySelector('i').classList[1].replace('fa-','');
                }
                showNotification(`${actionText} on "${title}"`, 'info');
                if (actionText.toLowerCase().includes('use') || actionText.toLowerCase().includes('continue')) {
                     setTimeout(() => {
                        const playgroundLink = document.querySelector('[data-view="playground"]');
                        if (playgroundLink) playgroundLink.click();
                    }, 300);
                }
            }
        });


        function showNotification(message, type = 'info') { 
            const existingNotifications = document.querySelectorAll('.notification');
            existingNotifications.forEach(notif => notif.remove());
            const notification = document.createElement('div');
            notification.className = `notification ${type}`;
            notification.textContent = message;
            document.body.appendChild(notification);
            setTimeout(() => { notification.classList.add('show');}, 100);
            setTimeout(() => { notification.classList.remove('show'); setTimeout(() => { notification.remove(); }, 300); }, 3000);
        }
        window.addEventListener('resize', () => { if (window.innerWidth > 768 && sidebar && sidebar.classList.contains('active')) { sidebar.classList.remove('active'); }});


        function initializePlaygroundScript() {
            const playgroundViewElement = document.getElementById('playground');
            if (!playgroundViewElement) return; 

            const searchAreaContainer = playgroundViewElement.querySelector('#pgSearchAreaContainer');
            const searchBarCard = playgroundViewElement.querySelector('#pgSearchBarCard');
            const searchInput = playgroundViewElement.querySelector('#pgSearchInput');
            
            const guidedCreateBtn = playgroundViewElement.querySelector('#pgGuidedCreateBtn');
            const micBtn = playgroundViewElement.querySelector('#pgMicBtn');
            const copyBtn = playgroundViewElement.querySelector('#pgCopyBtn');
            const sendBtn = playgroundViewElement.querySelector('#pgSendBtn');

            const dynamicContentArea = playgroundViewElement.querySelector('#pgDynamicContentArea');
            const analyzingIndicator = playgroundViewElement.querySelector('#pgAnalyzingIndicator');
            const simplePromptSuggestionsContainer = playgroundViewElement.querySelector('#pgSimplePromptSuggestionsContainer');
            const enhancedPromptSuggestionsContainer = playgroundViewElement.querySelector('#pgEnhancedPromptSuggestionsContainer');
            const favoritePromptsSection = playgroundViewElement.querySelector('#pgFavoritePromptsSection');
            const favoritePromptsList = playgroundViewElement.querySelector('#pgFavoritePromptsList');

            const modelSelectorBtn = playgroundViewElement.querySelector('#pgModelSelectorBtn');
            const currentModelNameSpan = playgroundViewElement.querySelector('#pgCurrentModelName');
            const modelDropdownList = playgroundViewElement.querySelector('#pgModelDropdownList');
            const modelSearchInput = playgroundViewElement.querySelector('#pgModelSearchInput'); 
            const modelListUl = modelDropdownList ? modelDropdownList.querySelector('.model-list-ul') : null;

            if (!searchInput || !modelSelectorBtn ) { 
                return;
            }
            
            let isGuidedModeActive = false;
            let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHubPlayground')) || []; 
            let blockSuggestionsOnNextFocus = false;

            const modelsData = [
                { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
                { id: "anthropic-claude-3", displayName: "Anthropic: Claude 3", shortName: "Claude 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-orange" },
                { id: "google-gemini-1.5", displayName: "Google: Gemini 1.5", shortName: "Gemini 1.5", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-blue-gemini" },
            ];
            
            function debouncePlayground(func, delay) { 
                let timeout; const debounced = function(...args) { const context = this; clearTimeout(timeout); timeout = setTimeout(() => func.apply(context, args), delay); }; debounced.cancel = function() { clearTimeout(timeout); }; return debounced;
            }
            function autoResizeTextarea(textarea) { 
                if (!textarea) return; textarea.style.height = 'auto'; let scrollHeight = textarea.scrollHeight; const minHeightStyle = window.getComputedStyle(textarea).minHeight; const minHeight = minHeightStyle ? parseInt(minHeightStyle, 10) : 60; textarea.style.height = Math.max(minHeight, scrollHeight) + 'px'; textarea.style.overflowY = (scrollHeight > minHeight && scrollHeight > textarea.clientHeight) ? 'auto' : 'hidden';
            }
            function formatFullPromptForDisplay(fullPromptText) {
                 if (!fullPromptText) return ""; let html = fullPromptText.replace(/&/g, "&amp;").replace(/</g, "&lt;").replace(/>/g, "&gt;"); html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim, (match) => `<span class="prompt-heading">${match.trim()}</span>`); return html.replace(/\n/g, '<br>');
            }
            function updateDynamicContentAreaVisibility() { 
                if (!dynamicContentArea || !simplePromptSuggestionsContainer || !enhancedPromptSuggestionsContainer || !favoritePromptsSection || !analyzingIndicator || !favoritePromptsList) return; const simpleList = simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list'); const enhancedList = enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list'); const isSimpleVisible = !simplePromptSuggestionsContainer.classList.contains('hidden') && simpleList && simpleList.children.length > 0; const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedList && enhancedList.children.length > 0; const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && favoritePromptsList.children.length > 0; const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden'); if (isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible) { dynamicContentArea.classList.remove('hidden'); } else { dynamicContentArea.classList.add('hidden'); }
            }
            function hideAllDynamicContentExcept(exceptContainer = null) { 
                [simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => { if (container && container !== exceptContainer) { container.classList.add('hidden'); const simpleList = container.id === 'pgSimplePromptSuggestionsContainer' ? container.querySelector('.simple-suggestions-list') : null; const enhancedList = container.id === 'pgEnhancedPromptSuggestionsContainer' ? container.querySelector('.enhanced-prompt-list') : null; const favList = container.id === 'pgFavoritePromptsSection' ? playgroundViewElement.querySelector('#pgFavoritePromptsList') : null; if (simpleList) simpleList.innerHTML = ''; if (enhancedList) enhancedList.innerHTML = ''; if (favList) favList.innerHTML = ''; } }); if (exceptContainer) exceptContainer.classList.remove('hidden');
            }
            function setDefaultModel() { 
                if (!currentModelNameSpan) return; if (modelsData.length > 0) { const defaultModel = modelsData[0]; currentModelNameSpan.textContent = defaultModel.shortName; currentModelNameSpan.dataset.selectedModelId = defaultModel.id; } else { currentModelNameSpan.textContent = "Model"; delete currentModelNameSpan.dataset.selectedModelId; }
            }
            function populateModelDropdown() {
                if (!modelListUl || !modelSearchInput || !currentModelNameSpan) return; modelListUl.innerHTML = ''; const searchTerm = modelSearchInput.value.toLowerCase(); const currentSelectedModelId = currentModelNameSpan.dataset.selectedModelId; modelsData.filter(model => model.displayName.toLowerCase().includes(searchTerm)).forEach(model => { const li = document.createElement('li'); li.dataset.modelId = model.id; li.dataset.modelShortName = model.shortName; li.dataset.modelDisplayName = model.displayName; let iconElement; if (model.iconType === "fa") { iconElement = document.createElement('i'); iconElement.className = `model-icon ${model.iconClass} ${model.iconColorClass || ''}`; } else { iconElement = document.createElement('span'); iconElement.className = 'model-icon icon-placeholder'; iconElement.textContent = '●'; } li.appendChild(iconElement); const span = document.createElement('span'); span.textContent = model.displayName; li.appendChild(span); if (currentSelectedModelId && currentSelectedModelId === model.id) { li.classList.add('selected-model-item'); } li.addEventListener('click', (e) => { e.stopPropagation(); currentModelNameSpan.textContent = model.shortName; currentModelNameSpan.dataset.selectedModelId = model.id; modelListUl.querySelectorAll('li').forEach(item => item.classList.remove('selected-model-item')); li.classList.add('selected-model-item'); if(modelDropdownList) modelDropdownList.classList.add('hidden'); if(modelSelectorBtn) modelSelectorBtn.classList.remove('open'); blockSuggestionsOnNextFocus = true; if(searchInput) searchInput.focus(); hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); }); modelListUl.appendChild(li); });
            }
            function renderFavoritePrompts() { 
                if (!favoritePromptsList || !searchInput || !favoritePromptsSection || (modelDropdownList && !modelDropdownList.classList.contains('hidden'))) return; if (isGuidedModeActive) return; hideAllDynamicContentExcept(favoritePromptsSection); favoritePromptsList.innerHTML = ''; if (favoritePrompts.length > 0 && searchInput.value.length === 0) { favoritePrompts.forEach(promptData => { const item = createPromptCard(promptData, false, true); favoritePromptsList.appendChild(item); }); favoritePromptsSection.classList.remove('hidden'); } else { favoritePromptsSection.classList.add('hidden'); } updateDynamicContentAreaVisibility();
            }
            function toggleFavorite(promptData, starIconElement) { 
                const promptIdToFind = promptData.id || promptData.title; let index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptIdToFind); let wasFavorited = index > -1; if (wasFavorited) { favoritePrompts.splice(index, 1); } else { const favData = { id: promptData.id, title: promptData.title, fullPrompt: promptData.fullPrompt, metaMinimal: promptData.metaMinimal, description: promptData.description || "N/A" }; favoritePrompts.push(favData); } localStorage.setItem('favoritePromptsAllHubPlayground', JSON.stringify(favoritePrompts)); playgroundViewElement.querySelectorAll(`.glass-star-btn[data-prompt-id="${promptIdToFind}"]`).forEach(starBtn => { const iElement = starBtn.querySelector('i'); if (iElement) { if (wasFavorited) { starBtn.classList.remove('favorited'); iElement.classList.remove('fas'); iElement.classList.add('far'); } else { starBtn.classList.add('favorited'); iElement.classList.remove('far'); iElement.classList.add('fas'); } } }); if (!isGuidedModeActive && searchInput && searchInput.value === '') { renderFavoritePrompts(); }
            }
            function createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) {
                const card = document.createElement('div'); card.classList.add('enhanced-prompt-card'); if (isFavoriteListItem) card.classList.add('favorite-list-item-card'); const uniqueId = promptData.id || (promptData.title ? promptData.title.replace(/\s+/g, '-').toLowerCase() : Date.now().toString()); card.dataset.promptId = uniqueId; const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === (promptData.id || promptData.title)); const starIconClass = isFavorited ? 'fas fa-star' : 'far fa-star'; let metaMinimalHTML = ''; if (promptData.metaMinimal) { metaMinimalHTML = '<div class="enhanced-prompt-meta-minimal">'; if (promptData.metaMinimal.inputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-in-alt"></i> ${promptData.metaMinimal.inputTokens}</span>`; if (promptData.metaMinimal.outputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-out-alt"></i> ${promptData.metaMinimal.outputTokens}</span>`; if (promptData.metaMinimal.time) metaMinimalHTML += `<span class="meta-item"><i class="far fa-clock"></i> ${promptData.metaMinimal.time}</span>`; metaMinimalHTML += '</div>'; } const buttonText = isFavoriteListItem ? 'Use Favorite' : 'Select Prompt'; card.innerHTML = ` <div class="enhanced-card-header"> <h5 class="enhanced-card-title">${promptData.title || "Prompt"}</h5> <button class="glass-star-btn" title="Mark as favorite" data-prompt-id="${uniqueId}"> <i class="${starIconClass}"></i> </button> </div> ${metaMinimalHTML} <div class="full-prompt-display">${formatFullPromptForDisplay(promptData.fullPrompt || promptData.title)}</div> <button class="select-prompt-button"> <i class="fas fa-check-circle"></i> ${buttonText} </button> `; const starButton = card.querySelector('.glass-star-btn'); if (starButton) { starButton.addEventListener('click', (e) => { e.stopPropagation(); toggleFavorite(promptData, e.currentTarget.querySelector('i')); }); } const selectButton = card.querySelector('.select-prompt-button'); if (selectButton) { selectButton.addEventListener('click', () => { if(!searchInput) return; let promptTextToSet = promptData.fullPrompt || promptData.title; searchInput.value = promptTextToSet; autoResizeTextarea(searchInput); exitGuidedModeIfNeeded(); hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); blockSuggestionsOnNextFocus = true; searchInput.focus(); }); } return card;
            }
            function displayGuidedPromptList(suggestions) { 
                if (!enhancedPromptSuggestionsContainer || !searchBarCard) return; isGuidedModeActive = true; hideAllDynamicContentExcept(enhancedPromptSuggestionsContainer); const listContainer = enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list'); if (!listContainer) return; listContainer.innerHTML = ''; if (suggestions && suggestions.length > 0) { suggestions.forEach(suggData => { const card = createPromptCard(suggData); listContainer.appendChild(card); }); enhancedPromptSuggestionsContainer.classList.remove('hidden'); } searchBarCard.classList.add('focused'); updateDynamicContentAreaVisibility();
            }
            function generateGuidedPrompts() {
                let suggestions = []; suggestions.push({ title: "Few-Shot Examples", id: "pg-few-shot", fullPrompt: `Write a blog article about X. Follow style of these examples:\n\nExample 1:\nTitle: "How to A"\nIntro: ...\nStep 1: ...\nConclusion: ...\n\nExample 2:\nTitle: "How to B"\nIntro: ...\nStep 1: ...\nConclusion: ...\n\nWrite a 300-word article about [YOUR TOPIC HERE] with intro, 3 steps, conclusion. Tone: [YOUR TONE].`, metaMinimal: { inputTokens: "150", outputTokens: "~400", time: "~1s" }}); suggestions.push({ title: "Chain of Thought", id: "pg-cot", fullPrompt: `Write a 300-word blog article about Y. Before writing, break down process: Identify key components (intro, steps, conclusion). List 3 methods for Y. For each, explain benefit/challenge. Structure with intro, section per method, conclusion. Tone: [YOUR TONE]. Title: "[YOUR TITLE]"`, metaMinimal: { inputTokens: "180", outputTokens: "~500", time: "~2s" } }); return suggestions;
             }
            async function handleGuidedCreateClick() { 
                if (modelDropdownList && !modelDropdownList.classList.contains('hidden')) { modelDropdownList.classList.add('hidden'); if(modelSelectorBtn) modelSelectorBtn.classList.remove('open'); } if (!analyzingIndicator || !searchBarCard) return; hideAllDynamicContentExcept(analyzingIndicator); analyzingIndicator.classList.remove('hidden'); searchBarCard.classList.add('focused'); updateDynamicContentAreaVisibility(); await new Promise(resolve => setTimeout(resolve, 1000)); const guidedPrompts = generateGuidedPrompts(); displayGuidedPromptList(guidedPrompts);
            }
            function exitGuidedModeIfNeeded() { 
                 if (isGuidedModeActive) { isGuidedModeActive = false; hideAllDynamicContentExcept(); if (document.activeElement !== searchInput && searchBarCard) { searchBarCard.classList.remove('focused'); } if (searchInput && searchInput.value === '') renderFavoritePrompts(); updateDynamicContentAreaVisibility(); }
            }
            function generateSimpleSuggestions(inputText) { 
                const baseSuggestions = [ "Summarize this text for a 5-year-old", "Explain quantum physics in simple terms", "Generate 5 creative blog post ideas about AI", "Write a python script to sort a list of numbers" ]; if (!inputText) return []; const lowerInput = inputText.toLowerCase(); return baseSuggestions.filter(sugg => sugg.toLowerCase().includes(lowerInput)).slice(0, 3);
            }
            const handleSimpleInput = debouncePlayground(function() { 
                if (isGuidedModeActive || (modelDropdownList && !modelDropdownList.classList.contains('hidden'))) { hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); return; } if (!searchInput || !simplePromptSuggestionsContainer) return; const inputText = searchInput.value; hideAllDynamicContentExcept(simplePromptSuggestionsContainer); const listContainer = simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list'); if (!listContainer) return; listContainer.innerHTML = ''; if (inputText.length < 1) { simplePromptSuggestionsContainer.classList.add('hidden'); if (inputText.length === 0) renderFavoritePrompts(); updateDynamicContentAreaVisibility(); return; } const suggestions = generateSimpleSuggestions(inputText); if (suggestions.length > 0) { simplePromptSuggestionsContainer.classList.remove('hidden'); suggestions.forEach(suggText => { const item = document.createElement('div'); item.classList.add('simple-suggestion-item'); item.textContent = suggText; item.addEventListener('click', () => { searchInput.value = suggText; autoResizeTextarea(searchInput); hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); blockSuggestionsOnNextFocus = true; searchInput.focus(); }); listContainer.appendChild(item); }); } else { simplePromptSuggestionsContainer.classList.add('hidden'); } updateDynamicContentAreaVisibility();
            }, 300);

            if(searchInput) {
                searchInput.addEventListener('input', () => { 
                     autoResizeTextarea(searchInput); if (isGuidedModeActive && searchInput.value === '') { exitGuidedModeIfNeeded(); } else if (!isGuidedModeActive && (!modelDropdownList || modelDropdownList.classList.contains('hidden'))) { blockSuggestionsOnNextFocus = false; handleSimpleInput(); } else if (modelDropdownList && !modelDropdownList.classList.contains('hidden')) { hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); }
                });
                searchInput.addEventListener('focus', () => { 
                    if (!searchBarCard) return; searchBarCard.classList.add('focused'); autoResizeTextarea(searchInput); if ((modelDropdownList && !modelDropdownList.classList.contains('hidden')) || isGuidedModeActive) { return; } if (searchInput.value === '') { renderFavoritePrompts(); } else { if (blockSuggestionsOnNextFocus) { blockSuggestionsOnNextFocus = false; } else { handleSimpleInput.cancel(); handleSimpleInput(); } }
                });
                let blurTimeoutPlayground; 
                searchInput.addEventListener('blur', () => { 
                     blurTimeoutPlayground = setTimeout(() => { if (!searchBarCard || !playgroundViewElement) return; const activeEl = document.activeElement; const isFocusWithinRelevantArea = searchBarCard.contains(activeEl) || (modelDropdownList && modelDropdownList.contains(activeEl)) || (dynamicContentArea && dynamicContentArea.contains(activeEl)); if (!isFocusWithinRelevantArea) { searchBarCard.classList.remove('focused'); if (modelDropdownList && modelDropdownList.classList.contains('hidden') && !isGuidedModeActive) { hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); } } }, 150);
                });
                [modelDropdownList, dynamicContentArea].forEach(el => { if(el) { el.addEventListener('focusin', () => clearTimeout(blurTimeoutPlayground)); el.addEventListener('mousedown', () => clearTimeout(blurTimeoutPlayground)); }});
            }

            if(modelSelectorBtn) {
                modelSelectorBtn.addEventListener('click', (e) => {
                    e.stopPropagation(); if (!modelDropdownList) return; const isCurrentlyHidden = modelDropdownList.classList.contains('hidden'); exitGuidedModeIfNeeded(); hideAllDynamicContentExcept(); if (isCurrentlyHidden) { modelDropdownList.classList.remove('hidden'); modelSelectorBtn.classList.add('open'); if(modelSearchInput) modelSearchInput.value = ''; populateModelDropdown(); if(modelSearchInput) modelSearchInput.focus(); } else { modelDropdownList.classList.add('hidden'); modelSelectorBtn.classList.remove('open'); } updateDynamicContentAreaVisibility();
                 });
            }
            if(modelSearchInput) {
                modelSearchInput.addEventListener('input', populateModelDropdown);
                modelSearchInput.addEventListener('click', (e) => e.stopPropagation());
            }

            if(guidedCreateBtn) guidedCreateBtn.addEventListener('click', handleGuidedCreateClick);
            if(micBtn) micBtn.addEventListener('click', () => showNotification("Voice input coming soon!", "info"));
            if(copyBtn) copyBtn.addEventListener('click', (e) => { 
                e.stopPropagation(); if(searchInput && searchInput.value) navigator.clipboard.writeText(searchInput.value) .then(() => showNotification('Prompt Copied!', 'success')) .catch(err => showNotification('Failed to copy.', 'error'));
            });
            if(sendBtn) sendBtn.addEventListener('click', (e) => { 
                e.stopPropagation(); if(searchInput && searchInput.value && currentModelNameSpan) { showNotification(`Sending with ${currentModelNameSpan.textContent}...`, 'info'); } else if (searchInput && !searchInput.value) { showNotification('Please enter a prompt.', 'error'); }
            });

            const playgroundDocumentClickListener = (event) => {
                if (modelDropdownList && modelSelectorBtn && !modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target) && !modelDropdownList.classList.contains('hidden')) {
                    modelDropdownList.classList.add('hidden');
                    modelSelectorBtn.classList.remove('open');
                }
                if (searchAreaContainer) { 
                    const isClickInsideSearchArea = searchAreaContainer.contains(event.target) || (modelDropdownList && modelDropdownList.contains(event.target));
                    if (!isClickInsideSearchArea && searchBarCard) {
                        searchBarCard.classList.remove('focused');
                         if (!isGuidedModeActive && (!modelDropdownList || modelDropdownList.classList.contains('hidden'))) {
                            hideAllDynamicContentExcept();
                            if(searchInput && searchInput.value === '') renderFavoritePrompts();
                            updateDynamicContentAreaVisibility();
                        }
                    }
                }
            };
            document.addEventListener('click', playgroundDocumentClickListener);
            
            if(searchInput) autoResizeTextarea(searchInput);
            setDefaultModel();
            const copyrightYearEl = playgroundViewElement.querySelector('#pgCopyrightYear');
            if(copyrightYearEl) copyrightYearEl.textContent = new Date().getFullYear();
            if (searchInput && searchInput.value === '') renderFavoritePrompts();
            if (document.activeElement === searchInput && searchBarCard) searchBarCard.classList.add('focused');
            if (simplePromptSuggestionsContainer && !simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')) { const list = document.createElement('div'); list.className = 'simple-suggestions-list'; simplePromptSuggestionsContainer.appendChild(list); }
            if (enhancedPromptSuggestionsContainer && !enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')) { const list = document.createElement('div'); list.className = 'enhanced-prompt-list'; enhancedPromptSuggestionsContainer.appendChild(list); }
        
        } 

        function initKnowledgeGraphD3() {
            const playgroundViewElement = document.getElementById('playground');
            if (!playgroundViewElement) return;
            const graphContainer = playgroundViewElement.querySelector("#knowledgeGraphD3");

            if (graphContainer && typeof d3 !== 'undefined' && !graphContainer.classList.contains('d3-initialized')) {
                graphContainer.innerHTML = ''; 
                graphContainer.getBoundingClientRect(); 

                let graphWidth = graphContainer.clientWidth;
                let graphHeight = graphContainer.clientHeight;

                if (graphWidth <= 10 || graphHeight <= 10) { 
                    const parentArea = playgroundViewElement.querySelector(".playground-knowledge-graph-area");
                    if (parentArea) {
                        graphWidth = parentArea.clientWidth > 40 ? parentArea.clientWidth - 40 : 300;
                        graphHeight = parentArea.clientHeight > 50 ? parentArea.clientHeight - 50 : 400; 
                    } else { 
                        graphWidth = playgroundViewElement.clientWidth * 0.45 || 400; 
                        graphHeight = 450;
                    }
                }
                
                const svg = d3.select(graphContainer)
                    .append("svg")
                    .attr("width", "100%")
                    .attr("height", "100%")
                    .attr("viewBox", `0 0 ${graphWidth} ${graphHeight}`)
                    .attr("preserveAspectRatio", "xMidYMid meet");

                const nodesData = [
                    { id: "Prompt", r: 25, colorVar: '--kg-node-pink', type: 'entity' },
                    { id: "Role", r: 18, colorVar: '--kg-node-blue', type: 'concept' },
                    { id: "Task", r: 18, colorVar: '--kg-node-blue', type: 'concept' },
                    { id: "Context", r: 18, colorVar: '--kg-node-blue', type: 'concept' },
                    { id: "Format", r: 15, colorVar: '--kg-node-pink', type: 'entity' },
                    { id: "Tone", r: 15, colorVar: '--kg-node-pink', type: 'entity' },
                    { id: "Examples", r: 15, colorVar: '--kg-node-blue', type: 'concept' },
                    { id: "Constraints", r: 15, colorVar: '--kg-node-pink', type: 'entity' },
                    { id: "Persona", r: 18, colorVar: '--kg-node-blue', type: 'concept' },
                    { id: "Audience", r: 18, colorVar: '--kg-node-blue', type: 'concept' },
                    { id: "Keywords", r: 15, colorVar: '--kg-node-pink', type: 'entity' },
                    { id: "Output", r: 22, colorVar: '--kg-node-blue', type: 'concept' },
                ];
                const linksData = [
                    { source: "Prompt", target: "Role", label: "HAS_A" }, 
                    { source: "Prompt", target: "Task", label: "DEFINES" },
                    { source: "Prompt", target: "Context", label: "PROVIDES" }, 
                    { source: "Prompt", target: "Output", label: "EXPECTS" },
                    { source: "Prompt", target: "Format", label: "SUGGESTS" }, 
                    { source: "Role", target: "Persona", label: "IS_A" }, 
                    { source: "Context", target: "Audience", label: "FOR_AN" },
                    { source: "Task", target: "Format", label: "REQUIRES" }, 
                    { source: "Task", target: "Tone", label: "SETS_TONE" },
                    { source: "Context", target: "Examples", label: "CAN_INCLUDE" }, 
                    { source: "Task", target: "Constraints", label: "HAS" },
                    { source: "Task", target: "Keywords", label: "USES" }, 
                    { source: "Output", target: "Format", label: "IN_FORMAT" },
                ];
                
                const rootStyles = getComputedStyle(document.documentElement);

                const simulation = d3.forceSimulation(nodesData)
                    .force("link", d3.forceLink(linksData).id(d => d.id).distance(d => (d.source.r + d.target.r) * 2.5 ).strength(0.1))
                    .force("charge", d3.forceManyBody().strength(-150 * (graphWidth / 300))) 
                    .force("center", d3.forceCenter(graphWidth / 2, graphHeight / 2))
                    .force("collide", d3.forceCollide().radius(d => d.r + 15).strength(0.7)); // Aumentar separación

                const linkGroup = svg.append("g").attr("class", "links");

                const link = linkGroup.selectAll(".d3-link-path")
                    .data(linksData)
                    .join("path")
                    .attr("class", "d3-link-path")
                    .attr("fill", "none")
                    .attr("stroke", "var(--kg-link-color)")
                    .attr("stroke-width", 1.5)
                    .attr("id", (d, i) => `linkPath${i}`);

                const linkLabel = linkGroup.selectAll(".d3-link-label")
                    .data(linksData)
                    .join("text")
                    .attr("class", "d3-link-label")
                    .attr("dy", -3) // Desplazar un poco arriba de la línea
                    .append("textPath")
                    .attr("xlink:href", (d, i) => `#linkPath${i}`)
                    .attr("startOffset", "50%")
                    .text(d => d.label);


                const nodeGroup = svg.append("g").attr("class", "nodes").selectAll("g.d3-node").data(nodesData).join("g").attr("class", "d3-node")
                    .attr("opacity", 0) 
                    .call(d3.drag().on("start", dragstarted).on("drag", dragged).on("end", dragended));

                nodeGroup.append("circle")
                    .attr("r", d => d.r)
                    .attr("fill", d => rootStyles.getPropertyValue(d.colorVar).trim() || (d.type === 'entity' ? 'var(--kg-node-pink)' : 'var(--kg-node-blue)'))
                    .on("mouseover", function(event, d) {
                        d3.select(this).transition().duration(150).attr("r", d.r * 1.1).style("filter", "brightness(1.1)");
                    })
                    .on("mouseout", function(event, d) {
                        d3.select(this).transition().duration(150).attr("r", d.r).style("filter", "none");
                    });
                
                nodeGroup.append("text")
                    .text(d => d.id)
                    .style("font-size", d => Math.max(8, d.r / 2.2) + "px") // Ajustar tamaño de fuente
                    .attr("fill", d => (d.type === 'entity' ? '#FFFFFF' : '#FFFFFF')) // Texto blanco para ambos tipos de nodos
                    .attr("dy", ".35em"); 

                nodeGroup.transition().duration(600).delay((d,i) => i * 40).attr("opacity", 1); 

                simulation.on("tick", () => {
                    link.attr("d", d => `M${d.source.x},${d.source.y} L${d.target.x},${d.target.y}`);
                    nodeGroup.attr("transform", d => `translate(${d.x},${d.y})`);
                });

                function dragstarted(event, d) { if (!event.active) simulation.alphaTarget(0.3).restart(); d.fx = d.x; d.fy = d.y; d3.select(this).raise().select("circle").transition().duration(100).attr("r", d.r * 1.2); }
                function dragged(event, d) { d.fx = event.x; d.fy = event.y; }
                function dragended(event, d) { if (!event.active) simulation.alphaTarget(0); d3.select(this).select("circle").transition().duration(100).attr("r", d.r); }
                
                graphContainer.classList.add('d3-initialized');

                let resizeTimer;
                window.addEventListener('resize', () => {
                    clearTimeout(resizeTimer);
                    resizeTimer = setTimeout(() => {
                        const currentWidth = graphContainer.clientWidth;
                        const currentHeight = graphContainer.clientHeight;
                        if (currentWidth > 0 && currentHeight > 0) {
                             svg.attr("viewBox", `0 0 ${currentWidth} ${currentHeight}`);
                             simulation.force("center", d3.forceCenter(currentWidth / 2, currentHeight / 2))
                                       .alpha(0.5).restart(); 
                        }
                    }, 250);
                });
            }
        }


        document.addEventListener('DOMContentLoaded', () => {
            showNotification('Welcome to AllHub! 🚀', 'success');
            initializePlaygroundScript(); 

            const playgroundView = document.getElementById('playground');
            if (playgroundView && playgroundView.classList.contains('active')) {
                initKnowledgeGraphD3();
            }
            // ECHARTS INITIALIZATION
            if (typeof echarts !== 'undefined') {
                const chartsToInit = [
                    { id: 'savedPromptsChart', options: { name: 'Saved Prompts', type: 'pie', radius: '55%', roseType: 'radius', data: [{ value: 27, name: 'Saved' }, { value: 73, name: 'Remaining' }] } },
                    { id: 'promptsUsedChart', options: { name: 'Prompts Used', type: 'pie', radius: ['40%', '55%'], data: [{ value: 152, name: 'Used' }, { value: 48, name: 'Remaining' }] } },
                    { id: 'activeFavoritesChart', options: { name: 'Active Favorites', type: 'pie', radius: '55%', roseType: 'area', data: [{ value: 8, name: 'Active' }, { value: 92, name: 'Remaining' }] } },
                    { id: 'responseTimeChart', options: { name: 'Response Time', type: 'bar', xAxisData: ['<1s', '1-2s', '>2s'], data: [40, 50, 10] } },
                    { id: 'userEngagementChart', options: { name: 'User Engagement', type: 'pie', radius: ['40%', '55%'], data: [{ value: 85, name: 'Engaged' }, { value: 15, name: 'Not Engaged' }] } },
                    { id: 'promptQualityChart', options: { name: 'Prompt Quality', type: 'line', smooth: true, xAxisData: ['High', 'Medium', 'Low'], data: [92, 6, 2] } }
                ];
                chartsToInit.forEach(chartConfig => {
                    const chartDiv = document.getElementById(chartConfig.id);
                    if (chartDiv) {
                        const chartInstance = echarts.init(chartDiv);
                        let seriesOpt = { name: chartConfig.options.name, type: chartConfig.options.type, data: chartConfig.options.data, itemStyle: { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [ { offset: 0, color: '#C645F9' }, { offset: 1, color: '#000d83' } ]), shadowBlur: 20, shadowColor: 'rgba(0, 0, 0, 0.3)' }, animationType: 'scale', animationEasing: 'elasticOut', animationDelay: (idx) => Math.random() * 200 };
                        if (chartConfig.options.type === 'pie') { seriesOpt.radius = chartConfig.options.radius; seriesOpt.center = ['50%', '50%']; if(chartConfig.options.roseType) seriesOpt.roseType = chartConfig.options.roseType; seriesOpt.label = { color: 'rgba(0, 0, 0, 0.5)' }; seriesOpt.labelLine = { lineStyle: { color: 'rgba(0, 0, 0, 0.3)' }, smooth: 0.2, length: 10, length2: 20 }; }
                        if (chartConfig.options.type === 'bar' || chartConfig.options.type === 'line') { seriesOpt.lineStyle = chartConfig.options.type === 'line' ? { color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [ { offset: 0, color: '#C645F9' }, { offset: 1, color: '#000d83' } ]), shadowBlur: 20, shadowColor: 'rgba(0, 0, 0, 0.3)' } : undefined; if(chartConfig.options.smooth) seriesOpt.smooth = chartConfig.options.smooth;}
                        chartInstance.setOption({ backgroundColor: 'rgba(255, 255, 255, 0.06)', tooltip: { trigger: chartConfig.options.type === 'pie' ? 'item' : 'axis' }, xAxis: chartConfig.options.xAxisData ? { type: 'category', data: chartConfig.options.xAxisData, axisLabel: { color: 'rgba(0, 0, 0, 0.5)' } } : undefined, yAxis: chartConfig.options.type !== 'pie' ? { type: 'value', axisLabel: { color: 'rgba(0, 0, 0, 0.5)' } } : undefined, series: [seriesOpt] });
                        window.addEventListener('resize', () => { if (echarts.getInstanceByDom(chartDiv)) { echarts.getInstanceByDom(chartDiv).resize(); }});
                    }
                });
            } else { console.error("ECharts library not loaded."); }
        }); 
    </script>
</body>
</html>