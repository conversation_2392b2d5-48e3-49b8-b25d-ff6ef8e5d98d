<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Mi Área - Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            /* TYPOGRAPHY */
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;

            /* NEW COLOR PALETTE */

            --color-vibrant-magenta: #C645F9;
            --color-indigo-blue: #5E6CE7;
            --color-pure-white: #FFFFFF;
            --color-deep-purple-dark: #0D0425;

            --color-bg: #F3F4F6; /* Un poco más claro para el fondo principal del contenido */
            --color-sidebar-bg: #FFFFFF; /* Sidebar más definido */
            --color-card-bg: #FFFFFF;
            --color-text: #111827;
            --color-text-muted: #6B7280;
            --color-cta: #3B82F6;
            --color-cta-text: #FFFFFF;
            --color-shadow-outset-primary: #E5E7EB;
            --color-shadow-outset-secondary: #D1D5DB;
            --color-accent-start: #b26eff;
            --color-accent-middle: #7873F5;
            --color-accent-end: #4ADEDE;
            --color-icon-gray: #9CA3AF;
            --color-hover-bg: #F9FAFB; /* Usado para hover en listas/navs */
            --color-border-light: #E5E7EB;
            --border-radius-card: 16px;
            --border-radius-pill: 9999px;
            --border-radius-element: 8px;
            --color-star-active: #FFD700;
            --sidebar-width: 260px;
            --header-height: 70px;
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background-color: var(--color-bg);
            color: var(--color-text);
            display: flex;
            min-height: 100vh;
            overflow-x: hidden; /* Prevenir scroll horizontal por el sidebar */
        }

        /* --- SIDEBAR --- */
        .sidebar {
            width: var(--sidebar-width);
            background-color: var(--color-sidebar-bg);
            border-right: 1px solid var(--color-border-light);
            padding: 25px 0;
            display: flex;
            flex-direction: column;
            position: fixed;
            left: 0;
            top: 0;
            bottom: 0;
            z-index: 1000;
            box-shadow: 3px 0 15px rgba(0,0,0,0.03);
        }

        .sidebar-logo {
            font-family: var(--font-header);
            font-size: 1.8rem;
            font-weight: 700;
            color: #023c99;
            padding: 0 25px;
            margin-bottom: 30px;
            text-align: center;
        }

        .sidebar-nav ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }

        .sidebar-nav .nav-item {
            margin-bottom: 5px;
        }

        .sidebar-nav .nav-link {
            display: flex;
            align-items: center;
            padding: 12px 25px;
            color: var(--color-text-muted);
            text-decoration: none;
            font-size: 0.95rem;
            font-weight: 500;
            transition: background-color 0.2s ease, color 0.2s ease, padding-left 0.2s ease;
            border-left: 4px solid transparent; /* Para el indicador activo */
        }

        .sidebar-nav .nav-link i {
            margin-right: 15px;
            width: 20px; /* Alineación de iconos */
            text-align: center;
            font-size: 1.1em;
        }

        .sidebar-nav .nav-link:hover {
            background-color: var(--color-hover-bg);
            color: var(--color-cta);
        }

        .sidebar-nav .nav-link.active {
            color: var(--color-cta);
            background-color: #EFF6FF; /* Azul muy claro */
            border-left-color: var(--color-cta);
            font-weight: 600;
        }
        .sidebar-nav .nav-link.active i {
            color: var(--color-cta);
        }

        .sidebar-user-profile {
            margin-top: auto; /* Empuja al fondo */
            padding: 20px 25px;
            border-top: 1px solid var(--color-border-light);
            display: flex;
            align-items: center;
            gap: 12px;
        }
        .sidebar-user-profile img {
            width: 40px;
            height: 40px;
            border-radius: 50%;
            object-fit: cover;
            border: 2px solid var(--color-border-light);
        }
        .sidebar-user-profile .user-info p { margin: 0; }
        .sidebar-user-profile .user-info .user-name { font-weight: 600; font-size: 0.9rem; }
        .sidebar-user-profile .user-info .user-email { font-size: 0.75rem; color: var(--color-text-muted); }


        /* --- MAIN CONTENT WRAPPER --- */
        .main-content-wrapper {
            margin-left: var(--sidebar-width);
            width: calc(100% - var(--sidebar-width));
            display: flex;
            flex-direction: column;
        }

        /* --- APP HEADER (dentro de main-content) --- */
        .app-header {
            height: var(--header-height);
            background-color: var(--color-card-bg);
            border-bottom: 1px solid var(--color-border-light);
            display: flex;
            align-items: center;
            justify-content: space-between;
            padding: 0 30px;
            position: sticky; /* O fixed si prefieres */
            top: 0;
            z-index: 900;
        }
        
        .header-title {
            font-family: var(--font-header);
            font-size: 1.5rem;
            font-weight: 600;
            color: var(--color-text);
        }

        .header-actions { display: flex; align-items: center; gap: 20px; }
        
        .btn-new-prompt {
            padding: 10px 22px;
            border-radius: var(--border-radius-pill);
            font-size: 0.9rem;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-flex; /* Para alinear ícono y texto */
            align-items: center;
            gap: 8px;
            background: linear-gradient(90deg,
                var(--color-accent-start),
                var(--color-accent-middle),
                var(--color-accent-end),
                var(--color-accent-middle),
                var(--color-accent-start)
            );
            background-size: 300% 100%;
            color: var(--color-cta-text);
            border: none;
            box-shadow: 0 6px 15px -4px rgba(120, 115, 245, 0.4), 0 3px 10px -5px rgba(0,0,0,0.05);
            animation: signUpGradientAnimation 15s linear infinite; /* Reutilizamos la animación */
        }
        @keyframes signUpGradientAnimation { /* Asegúrate que esté definida o cópiala */
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        .btn-new-prompt:hover {
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 0 15px 0px rgba(120, 115, 245, 0.6),
                        0 12px 25px -6px rgba(0,0,0,0.07),
                        0 5px 12px -5px rgba(0,0,0,0.05);
        }
        .btn-new-prompt i { font-size: 1.1em; }

        .user-menu { position: relative; }
        .user-menu-btn {
            background: none; border: none; cursor: pointer; display: flex; align-items: center; gap: 10px;
            padding: 8px; border-radius: var(--border-radius-element);
        }
        .user-menu-btn:hover { background-color: var(--color-hover-bg); }
        .user-menu-btn img { width: 36px; height: 36px; border-radius: 50%; object-fit: cover; }
        .user-menu-btn .user-name-header { font-weight: 500; font-size:0.9rem; }
        .user-menu-btn .fa-chevron-down { color: var(--color-text-muted); font-size: 0.8em; }
        .user-dropdown {
            position: absolute; top: calc(100% + 10px); right: 0;
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-element);
            box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            width: 200px;
            padding: 8px 0;
            z-index: 1010;
            opacity: 0; visibility: hidden;
            transform: translateY(10px);
            transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s ease;
        }
        .user-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0); }
        .user-dropdown a {
            display: block; padding: 10px 15px; color: var(--color-text);
            text-decoration: none; font-size: 0.9rem;
        }
        .user-dropdown a:hover { background-color: var(--color-hover-bg); }
        .user-dropdown a i { margin-right: 10px; color: var(--color-text-muted); }
        .user-dropdown .divider { height: 1px; background-color: var(--color-border-light); margin: 8px 0; }

        /* --- CONTENT AREA (donde se cargan las vistas) --- */
        .content-area {
            flex-grow: 1;
            padding: 30px;
            overflow-y: auto;
        }

        /* Estilos de tarjetas, reutilizando .enhanced-prompt-item de tu código público con adaptaciones */
        .grid-container {
            display: grid;
            grid-template-columns: repeat(auto-fill, minmax(300px, 1fr));
            gap: 25px;
        }

        .dashboard-card, .prompt-card { /* Usamos nombres más genéricos si es necesario */
            background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card);
            padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.06);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .dashboard-card:hover, .prompt-card:hover {
            transform: translateY(-3px);
            box-shadow: 0 12px 30px rgba(0,0,0,0.08);
        }

        .card-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }
        .card-header h3 {
            margin: 0; font-size: 1.2rem; font-weight: 600;
        }
        .card-header .icon {
            font-size: 1.5rem;
            color: var(--color-cta);
            opacity: 0.7;
        }
        
        /* Estilos para "Prompt Card" similares a .enhanced-prompt-item */
        /* Adaptamos .enhanced-prompt-item para la sección "Mis Prompts" o "Favoritos" en Dashboard */
        .enhanced-prompt-item {
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-card); /* Era element, lo subo a card */
            padding: 20px; /* Un poco menos que dashboard-card */
            margin-bottom: 20px; /* Si están en una lista vertical */
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        .enhanced-prompt-item:hover {
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(0,0,0,0.07);
        }
        .enhanced-prompt-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        .enhanced-prompt-item-header h5 { margin: 0; font-size: 1.05rem; font-weight: 600; color: var(--color-text); }
        .enhanced-prompt-item-header .star-icon { font-size: 1rem; color: var(--color-icon-gray); cursor:pointer; transition: color 0.2s ease, transform 0.2s ease; }
        .enhanced-prompt-item-header .star-icon:hover { transform: scale(1.2); }
        .enhanced-prompt-item-header .star-icon.favorited { color: var(--color-star-active); }
        .enhanced-prompt-item-meta-minimal { display: flex; flex-wrap: wrap; gap: 12px; font-size: 0.75rem; color: #4B5563; margin-bottom: 12px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; }
        .meta-item { display: flex; align-items: center; gap: 5px; }
        .meta-item i { color: var(--color-cta); font-size: 0.9em; }
        .full-prompt-display-summary { /* Para mostrar un resumen en el dashboard */
            font-size: 0.85rem; line-height: 1.6; color: #374151;
            max-height: 60px; /* Aprox 3 líneas */
            overflow: hidden;
            text-overflow: ellipsis;
            display: -webkit-box;
            -webkit-line-clamp: 3;
            -webkit-box-orient: vertical;
            margin-bottom: 15px;
        }
        .prompt-actions { display: flex; gap: 10px; margin-top: 15px; }
        .prompt-action-btn {
            background-color: var(--color-hover-bg);
            border: 1px solid var(--color-border-light);
            color: var(--color-text-muted);
            padding: 6px 12px;
            border-radius: var(--border-radius-element);
            font-size: 0.8rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.2s ease;
            text-decoration: none;
        }
        .prompt-action-btn:hover {
            background-color: var(--color-cta);
            color: var(--color-cta-text);
            border-color: var(--color-cta);
        }
        .prompt-action-btn.primary {
            background-color: var(--color-cta);
            color: var(--color-cta-text);
            border-color: var(--color-cta);
        }
        .prompt-action-btn.primary:hover {
            background-color: #2563EB; /* Un poco más oscuro que CTA */
        }


        /* Sección de bienvenida */
        .welcome-section {
            background: linear-gradient(120deg, var(--color-vibrant-magenta), var(--color-indigo-blue), var(--color-deep-purple-dark));
            padding: 30px 40px;
            border-radius: var(--border-radius-card);
            margin-bottom: 30px;
            color: var(--color-cta-text);
            box-shadow: 0 10px 25px rgba(120, 115, 245, 0.3);
        }
        .welcome-section h2 {
            font-family: var(--font-header);
            font-size: 2rem;
            margin-top: 0; margin-bottom: 5px;
            font-weight: 700;
        }
        .welcome-section p {
            font-size: 1rem; opacity: 0.9; margin-bottom: 0;
        }

        .section-title {
            font-size: 1.5rem;
            font-weight: 600;
            margin-bottom: 20px;
            color: var(--color-text);
            padding-bottom: 10px;
            border-bottom: 1px solid var(--color-border-light);
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .section-title i { color: var(--color-cta); }


        /* Responsive */
        @media (max-width: 992px) {
            .sidebar {
                transform: translateX(-100%);
                transition: transform 0.3s ease;
            }
            .sidebar.open {
                transform: translateX(0);
            }
            .main-content-wrapper {
                margin-left: 0;
                width: 100%;
            }
            .app-header { padding: 0 15px; }
            .menu-toggle-btn { display: block; font-size:1.5rem; cursor:pointer; color: var(--color-text); margin-right:15px; }
            .sidebar-logo { font-size: 1.5rem;}
            .header-title { font-size: 1.3rem;}
        }
        @media (max-width: 768px) {
            .grid-container {
                grid-template-columns: 1fr; /* Stack cards on smaller screens */
            }
            .welcome-section h2 { font-size: 1.6rem; }
            .welcome-section p { font-size: 0.9rem; }
            .btn-new-prompt { padding: 8px 18px; font-size: 0.85rem; }
            .content-area { padding: 20px; }
        }

        .menu-toggle-btn { display: none; /* Oculto por defecto, visible en mobile */ }
        .hidden { display: none !important; }
    </style>
</head>
<body>

    <aside class="sidebar" id="sidebar">
        <div class="sidebar-logo">allhub</div>
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item">
                    <a href="#" class="nav-link active" data-view="dashboard">
                        <i class="fas fa-tachometer-alt"></i> Dashboard
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="my-prompts">
                        <i class="fas fa-lightbulb"></i> Mis Prompts
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="history">
                        <i class="fas fa-history"></i> Historial
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="playground">
                        <i class="fas fa-rocket"></i> Playground
                    </a>
                </li>
                <li class="nav-item">
                    <a href="#" class="nav-link" data-view="settings">
                        <i class="fas fa-cog"></i> Configuración
                    </a>
                </li>
            </ul>
        </nav>
        <div class="sidebar-user-profile">
            <img src="https://i.pravatar.cc/80?u=UserExample" alt="User Avatar"> <!-- Placeholder Avatar -->
            <div class="user-info">
                <p class="user-name">Elena V.</p>
                <p class="user-email"><EMAIL></p>
            </div>
        </div>
    </aside>

    <div class="main-content-wrapper">
        <header class="app-header">
            <div style="display: flex; align-items: center;">
                <button class="menu-toggle-btn" id="menuToggleBtn"><i class="fas fa-bars"></i></button>
                <h2 class="header-title" id="headerTitle">Dashboard</h2>
            </div>
            <div class="header-actions">
                <a href="#" class="btn-new-prompt"><i class="fas fa-plus-circle"></i> Nuevo Prompt</a>
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <img src="https://i.pravatar.cc/80?u=UserExample" alt="User Avatar">
                        <span class="user-name-header">Elena V.</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user-circle"></i> Mi Perfil</a>
                        <a href="#"><i class="fas fa-credit-card"></i> Suscripción</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Cerrar Sesión</a>
                    </div>
                </div>
            </div>
        </header>

        <main class="content-area">
            <!-- VISTA DASHBOARD (Visible por defecto) -->
            <div id="dashboardView">
                <div class="welcome-section">
                    <h2>¡Bienvenida de nuevo, Elena!</h2>
                    <p>Lista para crear prompts increíbles hoy?</p>
                </div>

                <div class="section-title"><i class="fas fa-chart-line"></i> Tu Actividad Reciente</div>
                <div class="grid-container" style="margin-bottom: 30px;">
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Prompts Guardados</h3>
                            <i class="fas fa-save icon"></i>
                        </div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">27</p>
                        <p class="text-muted" style="font-size:0.9rem;">Organizados y listos para usar.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Prompts Usados (Mes)</h3>
                            <i class="fas fa-cogs icon"></i>
                        </div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">152</p>
                        <p class="text-muted" style="font-size:0.9rem;">Generando ideas sin parar.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header">
                            <h3>Favoritos Activos</h3>
                            <i class="fas fa-star icon"></i>
                        </div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-star-active);">8</p>
                        <p class="text-muted" style="font-size:0.9rem;">Tus prompts estrella.</p>
                    </div>
                </div>

                <div class="section-title"><i class="fas fa-bolt"></i> Acceso Rápido: Favoritos</div>
                <div id="favoritePromptsDashboard">
                    <!-- Aquí se cargarían dinámicamente los prompts favoritos -->
                    <div class="enhanced-prompt-item">
                        <div class="enhanced-prompt-item-header">
                            <h5>Mejorar Artículo con CoT</h5>
                            <i class="fas fa-star star-icon favorited" title="Quitar de favoritos"></i>
                        </div>
                        <div class="enhanced-prompt-item-meta-minimal">
                            <span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                            <span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                            <span class="meta-item"><i class="far fa-clock"></i> Time: ~2s</span>
                        </div>
                        <p class="full-prompt-display-summary">Escribe un artículo de blog de 400 palabras sobre cómo resumir un PDF. Antes de escribir, desglosa el proceso...</p>
                        <div class="prompt-actions">
                             <a href="#" class="prompt-action-btn primary"><i class="fas fa-play-circle"></i> Usar en Playground</a>
                             <a href="#" class="prompt-action-btn"><i class="fas fa-edit"></i> Editar</a>
                        </div>
                    </div>
                     <div class="enhanced-prompt-item">
                        <div class="enhanced-prompt-item-header">
                            <h5>Resumen PDF con Few-Shot</h5>
                             <i class="fas fa-star star-icon favorited" title="Quitar de favoritos"></i>
                        </div>
                        <div class="enhanced-prompt-item-meta-minimal">
                            <span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input: 170</span>
                            <span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output: ~500</span>
                            <span class="meta-item"><i class="far fa-clock"></i> Time: ~1.5s</span>
                        </div>
                        <p class="full-prompt-display-summary">Escribe un artículo de blog sobre cómo resumir un PDF. Sigue el estilo y estructura de estos ejemplos: Ejemplo 1...</p>
                        <div class="prompt-actions">
                             <a href="#" class="prompt-action-btn primary"><i class="fas fa-play-circle"></i> Usar en Playground</a>
                             <a href="#" class="prompt-action-btn"><i class="fas fa-edit"></i> Editar</a>
                        </div>
                    </div>
                </div>
            </div>

            <!-- VISTA MIS PROMPTS (Oculta por defecto) -->
            <div id="myPromptsView" class="hidden">
                 <div class="section-title"><i class="fas fa-lightbulb"></i> Mis Prompts</div>
                 <p>Aquí gestionarás todos tus prompts guardados. Podrás crear nuevos, editarlos, organizarlos en carpetas (futuro) y más.</p>
                 <!-- Contenido de Mis Prompts iría aquí: lista de prompts, filtros, botón de crear nuevo, etc. -->
            </div>
            
            <!-- VISTA HISTORIAL (Oculta por defecto) -->
            <div id="historyView" class="hidden">
                 <div class="section-title"><i class="fas fa-history"></i> Historial de Prompts</div>
                 <p>Un registro de todos los prompts que has utilizado o generado a través del Playground.</p>
                 <!-- Contenido del Historial iría aquí: tabla o lista de prompts con fecha, modelo usado, etc. -->
            </div>

            <!-- VISTA PLAYGROUND (Oculta por defecto) -->
            <div id="playgroundView" class="hidden">
                 <div class="section-title"><i class="fas fa-rocket"></i> Playground</div>
                 <p>Este será tu espacio para experimentar. Muy similar a la interfaz pública, pero con integración directa para guardar y gestionar tus creaciones.</p>
                 <!-- Aquí se podría incrustar una versión adaptada del search-area-container de la página pública -->
                 <!-- O una nueva interfaz específica para el playground -->
            </div>

            <!-- VISTA CONFIGURACIÓN (Oculta por defecto) -->
            <div id="settingsView" class="hidden">
                 <div class="section-title"><i class="fas fa-cog"></i> Configuración de Cuenta</div>
                 <p>Administra tu perfil, información de suscripción, API keys (si aplica) y otras preferencias.</p>
                 <!-- Contenido de Configuración: formularios para datos personales, etc. -->
            </div>

        </main>
    </div>

    <script>
        document.addEventListener('DOMContentLoaded', () => {
            const userMenuBtn = document.getElementById('userMenuBtn');
            const userDropdown = document.getElementById('userDropdown');
            const menuToggleBtn = document.getElementById('menuToggleBtn');
            const sidebar = document.getElementById('sidebar');
            const headerTitle = document.getElementById('headerTitle');
            const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
            const views = {
                dashboard: document.getElementById('dashboardView'),
                'my-prompts': document.getElementById('myPromptsView'),
                history: document.getElementById('historyView'),
                playground: document.getElementById('playgroundView'),
                settings: document.getElementById('settingsView')
            };

            // User menu dropdown
            if (userMenuBtn && userDropdown) {
                userMenuBtn.addEventListener('click', (e) => {
                    e.stopPropagation();
                    userDropdown.classList.toggle('visible');
                });
            }
            document.addEventListener('click', (e) => {
                if (userDropdown && userDropdown.classList.contains('visible') && !userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                    userDropdown.classList.remove('visible');
                }
            });

            // Sidebar toggle for mobile
            if (menuToggleBtn && sidebar) {
                menuToggleBtn.addEventListener('click', () => {
                    sidebar.classList.toggle('open');
                });
            }
             // Close sidebar if clicked outside on mobile
            document.addEventListener('click', (e) => {
                if (sidebar.classList.contains('open') && !sidebar.contains(e.target) && !menuToggleBtn.contains(e.target)) {
                    sidebar.classList.remove('open');
                }
            });


            // Simple view switching logic
            navLinks.forEach(link => {
                link.addEventListener('click', (e) => {
                    e.preventDefault();
                    const viewName = link.dataset.view;

                    // Update active link
                    navLinks.forEach(l => l.classList.remove('active'));
                    link.classList.add('active');

                    // Update header title
                    headerTitle.textContent = link.textContent.trim();

                    // Switch view
                    for (const key in views) {
                        if (views[key]) { // Check if element exists
                            views[key].classList.add('hidden');
                        }
                    }
                    if (views[viewName]) { // Check if view to show exists
                        views[viewName].classList.remove('hidden');
                    } else {
                        console.warn(`View "${viewName}" not found.`);
                        if(views.dashboard) views.dashboard.classList.remove('hidden'); // Fallback to dashboard
                        headerTitle.textContent = "Dashboard"; // Fallback title
                        document.querySelector('.sidebar-nav .nav-link[data-view="dashboard"]').classList.add('active');
                    }
                    
                    // Close sidebar on mobile after navigation
                    if (window.innerWidth <= 992 && sidebar.classList.contains('open')) {
                        sidebar.classList.remove('open');
                    }
                });
            });

            // Initialize with dashboard view
            if (views.dashboard) {
                views.dashboard.classList.remove('hidden');
            } else {
                 console.error("Dashboard view element not found on init.");
            }

        });
    </script>
</body>
</html>