<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>My Area - Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;
            --color-bg: #F3F4F6;
            --color-sidebar-bg: #FFFFFF;
            --color-card-bg: #FFFFFF;
            --color-text: #111827;
            --color-text-muted: #6B7280;
            --color-cta: #3B82F6;
            --color-cta-text: #FFFFFF;
            --color-shadow-outset-primary: #E5E7EB; /* From public */
            --color-shadow-outset-secondary: #D1D5DB; /* From public */
            --color-accent-start: #b26eff;
            --color-accent-middle: #7873F5;
            --color-accent-end: #4ADEDE;
            --color-icon-gray: #9CA3AF; /* From public */
            --color-placeholder: #9CA3AF; /* From public */
            --color-hover-bg: #F9FAFB;
            --color-border-light: #E5E7EB;
            --color-border-input-focus: var(--color-cta);
            --border-radius-card: 16px; /* From public */
            --border-radius-pill: 9999px; /* From public */
            --border-radius-element: 8px; /* From public */
            --color-star-active: #FFD700; /* From public */
            --sidebar-width: 280px;
            --header-height: 70px;
        }

        @keyframes synchronizedNeonShine { /* From public */
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        @keyframes signUpGradientAnimation { /* From public */
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } } /* From public */
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-15px); } to { opacity: 1; transform: translateY(0); } } /* From public */
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px); } to { opacity: 1; transform: translateY(0); } } /* From public */


        body {
            margin: 0; font-family: var(--font-body); background-color: var(--color-bg);
            color: var(--color-text); display: flex; min-height: 100vh; overflow-x: hidden;
        }

        /* --- SIDEBAR, MAIN CONTENT WRAPPER, APP HEADER (Mostly unchanged from previous private version) --- */
        .sidebar {
            width: var(--sidebar-width); background-color: var(--color-sidebar-bg);
            border-right: 1px solid var(--color-border-light); padding: 25px 0;
            display: flex; flex-direction: column; position: fixed;
            left: 0; top: 0; bottom: 0; z-index: 1000;
            box-shadow: 3px 0 15px rgba(0,0,0,0.03);
            transition: transform 0.3s ease-in-out;
        }
        .sidebar-logo {
            font-family: var(--font-header); font-size: 1.8rem; font-weight: 700;
            color: #023c99; padding: 0 30px; margin-bottom: 35px; text-align: left;
        }
        .sidebar-nav ul { list-style: none; padding: 0; margin: 0; }
        .sidebar-nav .nav-item { margin-bottom: 6px; }
        .sidebar-nav .nav-link {
            display: flex; align-items: center; padding: 13px 30px;
            color: var(--color-text-muted); text-decoration: none;
            font-size: 0.95rem; font-weight: 500;
            transition: background-color 0.2s ease, color 0.2s ease, border-left-color 0.2s ease;
            border-left: 4px solid transparent;
        }
        .sidebar-nav .nav-link i { margin-right: 18px; width: 20px; text-align: center; font-size: 1.1em; }
        .sidebar-nav .nav-link:hover { background-color: var(--color-hover-bg); color: var(--color-cta); }
        .sidebar-nav .nav-link.active {
            color: var(--color-cta); background-color: #EFF6FF;
            border-left-color: var(--color-cta); font-weight: 600;
        }
        .sidebar-nav .nav-link.active i { color: var(--color-cta); }
        .sidebar-user-profile {
            margin-top: auto; padding: 20px 30px; border-top: 1px solid var(--color-border-light);
            display: flex; align-items: center; gap: 15px;
        }
        .sidebar-user-profile img { width: 40px; height: 40px; border-radius: 50%; object-fit: cover; border: 2px solid var(--color-border-light); }
        .sidebar-user-profile .user-info p { margin: 0; }
        .sidebar-user-profile .user-info .user-name { font-weight: 600; font-size: 0.9rem; }
        .sidebar-user-profile .user-info .user-email { font-size: 0.75rem; color: var(--color-text-muted); }
        .main-content-wrapper { margin-left: var(--sidebar-width); width: calc(100% - var(--sidebar-width)); display: flex; flex-direction: column; transition: margin-left 0.3s ease-in-out; }
        .app-header {
            height: var(--header-height); background-color: var(--color-card-bg);
            border-bottom: 1px solid var(--color-border-light); display: flex;
            align-items: center; justify-content: space-between; padding: 0 30px;
            position: sticky; top: 0; z-index: 900;
        }
        .header-title-container { display: flex; align-items: center; }
        .menu-toggle-btn { display: none;
            background: none; border: none; font-size: 1.5rem; cursor: pointer;
            color: var(--color-text); margin-right: 15px; padding: 5px;
        }
        .header-title { font-family: var(--font-header); font-size: 1.5rem; font-weight: 600; color: var(--color-text); }
        .header-actions { display: flex; align-items: center; gap: 20px; }
        .btn-gradient-action { /* Primary CTA */
            position: relative; display: inline-flex; align-items: center; gap: 8px;
            padding: 10px 22px; border-radius: var(--border-radius-pill);
            font-size: 0.9rem; font-weight: 600; cursor: pointer;
            transition: all 0.3s ease; text-decoration: none; line-height: 1.5;
            background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%; color: var(--color-cta-text); border: none;
            box-shadow: 0 6px 15px -4px rgba(120, 115, 245, 0.4), 0 3px 10px -5px rgba(0,0,0,0.05);
            animation: signUpGradientAnimation 20s linear infinite;
            overflow: hidden;
        }
        .btn-gradient-action:hover {
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 0 15px 0px rgba(120, 115, 245, 0.6),
                        0 12px 25px -6px rgba(0,0,0,0.07),
                        0 5px 12px -5px rgba(0,0,0,0.05);
        }
        .btn-gradient-action i { font-size: 1.05em; }
        .stylish-action-button { /* Secondary actions */
            position: relative; display: inline-flex; align-items: center; gap: 8px;
            background-color: var(--color-hover-bg); color: var(--color-text);
            padding: 8px 15px; border-radius: var(--border-radius-element); border: none;
            font-size: 0.85rem; font-weight: 500; cursor: pointer;
            transition: box-shadow 0.2s ease, background-color 0.2s ease, color 0.2s ease;
            text-decoration: none; line-height: 1.5; z-index: 1; overflow: hidden;
        }
        .stylish-action-button::before {
            content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            border-radius: inherit; padding: 1.5px;
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%; animation: synchronizedNeonShine 5s linear infinite;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: subtract; mask-composite: subtract;
            z-index: -1; opacity: 0.8; transition: opacity 0.3s ease;
        }
        .stylish-action-button:hover::before { opacity: 1; }
        .stylish-action-button:hover {
            box-shadow: 0 0 8px rgba(var(--color-cta), 0.3);
            background-color: var(--color-cta); color: var(--color-cta-text);
        }
        .stylish-action-button:hover i { color: var(--color-cta-text); }
        .stylish-action-button.delete { background-color: #FEE2E2; color: #B91C1C; }
        .stylish-action-button.delete:hover { background-color: #DC2626; color: var(--color-cta-text); }
        .stylish-action-button.delete::before { background: linear-gradient(120deg, #fda4af, #f43f5e, #fda4af); }
        .user-menu { position: relative; }
        .user-menu-btn {
            background: none; border: none; cursor: pointer; display: flex; align-items: center; gap: 10px;
            padding: 8px; border-radius: var(--border-radius-element);
        }
        .user-menu-btn:hover { background-color: var(--color-hover-bg); }
        .user-menu-btn img { width: 36px; height: 36px; border-radius: 50%; object-fit: cover; }
        .user-menu-btn .user-name-header { font-weight: 500; font-size:0.9rem; }
        .user-menu-btn .fa-chevron-down { color: var(--color-text-muted); font-size: 0.8em; }
        .user-dropdown {
            position: absolute; top: calc(100% + 10px); right: 0;
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-element); box-shadow: 0 5px 20px rgba(0,0,0,0.08);
            width: 200px; padding: 8px 0; z-index: 1010;
            opacity: 0; visibility: hidden; transform: translateY(10px);
            transition: opacity 0.2s ease, transform 0.2s ease, visibility 0.2s ease;
        }
        .user-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0); }
        .user-dropdown a { display: block; padding: 10px 15px; color: var(--color-text); text-decoration: none; font-size: 0.9rem; }
        .user-dropdown a:hover { background-color: var(--color-hover-bg); }
        .user-dropdown a i { margin-right: 10px; color: var(--color-text-muted); }
        .user-dropdown .divider { height: 1px; background-color: var(--color-border-light); margin: 8px 0; }

        /* --- CONTENT AREA & GENERAL STYLES --- */
        .content-area { flex-grow: 1; padding: 30px; overflow-y: auto; }
        .section-title {
            font-size: 1.6rem; font-weight: 600; margin-bottom: 25px;
            color: var(--color-text); padding-bottom: 12px;
            border-bottom: 1px solid var(--color-border-light);
            display: flex; align-items: center; gap: 12px;
        }
        .section-title i { color: var(--color-cta); font-size: 1.1em; }
        .grid-container { display: grid; grid-template-columns: repeat(auto-fill, minmax(320px, 1fr)); gap: 25px; }
        .text-muted { color: var(--color-text-muted); }
        .hidden { display: none !important; }

        /* --- DASHBOARD Specific Styles (from first private version) --- */
        #dashboardView .welcome-section {
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end));
            padding: 30px 40px; border-radius: var(--border-radius-card); margin-bottom: 30px;
            color: var(--color-cta-text); box-shadow: 0 10px 25px rgba(120, 115, 245, 0.3);
        }
        #dashboardView .welcome-section h2 { font-family: var(--font-header); font-size: 2rem; margin-top: 0; margin-bottom: 5px; font-weight: 700; }
        #dashboardView .welcome-section p { font-size: 1rem; opacity: 0.9; margin-bottom: 0; }
        #dashboardView .dashboard-card {
            background-color: var(--color-card-bg); border-radius: var(--border-radius-card); padding: 25px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.06); transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        #dashboardView .dashboard-card:hover { transform: translateY(-3px); box-shadow: 0 12px 30px rgba(0,0,0,0.08); }
        #dashboardView .card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 15px; }
        #dashboardView .card-header h3 { margin: 0; font-size: 1.2rem; font-weight: 600; }
        #dashboardView .card-header .icon { font-size: 1.5rem; color: var(--color-cta); opacity: 0.7; }
        /* Using enhanced-prompt-item for dashboard favorites for consistency with original public page style */
        #dashboardView .enhanced-prompt-item {
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-card); padding: 20px;
            margin-bottom: 20px; box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }
        #dashboardView .enhanced-prompt-item:hover { transform: translateY(-2px); box-shadow: 0 6px 20px rgba(0,0,0,0.07); }
        #dashboardView .enhanced-prompt-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 10px; }
        #dashboardView .enhanced-prompt-item-header h5 { margin: 0; font-size: 1.05rem; font-weight: 600; color: var(--color-text); }
        #dashboardView .enhanced-prompt-item-header .star-icon { font-size: 1rem; color: var(--color-icon-gray); cursor:pointer; transition: color 0.2s ease, transform 0.2s ease; }
        #dashboardView .enhanced-prompt-item-header .star-icon:hover { transform: scale(1.2); }
        #dashboardView .enhanced-prompt-item-header .star-icon.favorited { color: var(--color-star-active); }
        #dashboardView .enhanced-prompt-item-meta-minimal { display: flex; flex-wrap: wrap; gap: 12px; font-size: 0.75rem; color: #4B5563; margin-bottom: 12px; padding: 8px; background-color: #f8f9fa; border-radius: 6px; }
        #dashboardView .meta-item { display: flex; align-items: center; gap: 5px; }
        #dashboardView .meta-item i { color: var(--color-cta); font-size: 0.9em; }
        #dashboardView .full-prompt-display-summary {
            font-size: 0.85rem; line-height: 1.6; color: #374151;
            max-height: 60px; overflow: hidden; text-overflow: ellipsis;
            display: -webkit-box; -webkit-line-clamp: 3; -webkit-box-orient: vertical; margin-bottom: 15px;
        }
        #dashboardView .prompt-actions { display: flex; gap: 10px; margin-top: 15px; }

        /* --- MY PROMPTS Specific Styles --- */
        .prompts-controls-bar {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 25px; padding: 15px; background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card); box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            flex-wrap: wrap; gap: 15px;
        }
        .prompts-view-toggle { display: flex; gap: 0; }
        .prompts-view-toggle button {
            background-color: transparent; border: 1px solid var(--color-border-light);
            padding: 8px 15px; cursor: pointer; color: var(--color-text-muted);
            transition: background-color 0.2s, color 0.2s;
        }
        .prompts-view-toggle button:first-child { border-radius: var(--border-radius-element) 0 0 var(--border-radius-element); border-right:none;}
        .prompts-view-toggle button:last-child { border-radius: 0 var(--border-radius-element) var(--border-radius-element) 0; }
        .prompts-view-toggle button.active { background-color: var(--color-cta); color: var(--color-cta-text); border-color: var(--color-cta); }
        .prompts-view-toggle button:hover:not(.active) { background-color: var(--color-hover-bg); }

        .prompts-filter-panel {
            display: flex; flex-wrap: wrap; gap: 15px; align-items: center;
            padding: 15px; background-color: #F9FAFB;
            border-radius: var(--border-radius-element); margin-bottom: 25px;
            border: 1px solid var(--color-border-light);
        }
        .prompts-filter-panel .filter-group { display: flex; align-items: center; gap: 8px; }
        .prompts-filter-panel .filter-group label { font-size: 0.85rem; font-weight: 500; color: var(--color-text-muted); }
        .prompts-filter-panel .form-control, .prompts-filter-panel .btn-filter-toggle {
            padding: 8px 12px; border-radius: var(--border-radius-element);
            border: 1px solid var(--color-border-light); font-size: 0.9rem;
            background-color: var(--color-card-bg);
        }
        .prompts-filter-panel .form-control:focus { border-color: var(--color-cta); box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); outline: none; }
        .prompts-filter-panel .btn-filter-toggle {
            cursor: pointer; transition: background-color 0.2s, color 0.2s, border-color 0.2s;
        }
        .prompts-filter-panel .btn-filter-toggle.active {
            background-color: var(--color-cta); color: var(--color-cta-text); border-color: var(--color-cta);
            box-shadow: 0 2px 5px rgba(59, 130, 246, 0.2);
        }
        .prompts-filter-panel .btn-filter-toggle:hover:not(.active) { background-color: #E5E7EB; }

        .prompt-card-item-v2 { /* New card for My Prompts */
            background-color: var(--color-card-bg); border: 1px solid var(--color-border-light);
            border-radius: var(--border-radius-card);
            box-shadow: 0 4px 15px rgba(0,0,0,0.05);
            transition: transform 0.2s ease, box-shadow 0.2s ease;
            display: flex; flex-direction: column; padding: 20px;
        }
        .prompt-card-item-v2:hover { transform: translateY(-3px); box-shadow: 0 8px 25px rgba(0,0,0,0.08); }
        .prompt-card-header-v2 { display: flex; justify-content: space-between; align-items: flex-start; margin-bottom: 12px; }
        .prompt-card-title-v2 { font-size: 1.15rem; font-weight: 600; color: var(--color-text); margin:0; line-height: 1.3; }
        .prompt-card-fav-icon-v2 { font-size: 1.1rem; color: var(--color-icon-gray); cursor:pointer; transition: color 0.2s ease, transform 0.2s ease; padding:5px;}
        .prompt-card-fav-icon-v2:hover { transform: scale(1.2); }
        .prompt-card-fav-icon-v2.favorited { color: var(--color-star-active); }

        .prompt-card-tags-v2 { display: flex; flex-wrap: wrap; gap: 6px; margin-bottom: 12px; }
        .prompt-card-tags-v2 .tag {
            background-color: #E0E7FF; color: #3730A3; padding: 4px 10px;
            border-radius: var(--border-radius-pill); font-size: 0.7rem; font-weight: 500;
        }
        .prompt-card-summary-v2 {
            font-size: 0.9rem; line-height: 1.6; color: var(--color-text-muted); margin-bottom: 15px;
            flex-grow: 1; overflow: hidden; text-overflow: ellipsis;
            display: -webkit-box; -webkit-line-clamp: 2; -webkit-box-orient: vertical;
        }
        .prompt-card-footer-v2 { display: flex; justify-content: space-between; align-items: center; margin-top: auto; border-top: 1px solid #f3f4f6; padding-top: 15px; }
        .prompt-card-stats-v2 { font-size: 0.8rem; color: var(--color-text-muted); }
        .prompt-card-stats-v2 i { margin-right: 4px; }
        .prompt-card-actions-v2 { display: flex; gap: 10px; }

        /* --- HISTORY Specific Styles (Unchanged) --- */
        #historyView .history-list-item { /* ... keep as is */ }

        /* --- PLAYGROUND - STYLES FROM PUBLIC HTML (SCOPED) --- */
        #playgroundView .search-area-container { /* From public, but now under #playgroundView */
            display: flex; flex-direction: column; align-items: center;
            width: 100%; max-width: 700px; margin: 20px auto; /* Added margin */
            position: relative; animation: fadeInUp 1s ease-out 0.2s;
            animation-fill-mode: backwards; z-index: 2;
        }
        #playgroundView .search-bar-card {
            width: 100%; background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 6px 15px rgba(0,0,0,0.07);
            border: none; position: relative; display: flex; flex-direction: column;
            box-sizing: border-box; transition: box-shadow 0.4s ease; overflow: hidden; z-index: 1;
        }
        #playgroundView .search-bar-card::before {
            content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%; z-index: -1; border-radius: inherit; padding: 2.5px;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; mask-composite: exclude;
            opacity: 0; transition: opacity 0.4s ease; animation: synchronizedNeonShine 10s linear infinite paused;
        }
        #playgroundView .search-bar-card.focused-card::before,
        #playgroundView .search-bar-card.always-focused::before { opacity: 1; animation-play-state: running; }
        #playgroundView .search-bar-card.focused-card,
        #playgroundView .search-bar-card.always-focused {
            box-shadow: 0 0 7px rgba(255, 110, 196, 0.5), 0 0 12px rgba(120, 115, 245, 0.4),
                        0 0 18px rgba(74, 222, 222, 0.3), 0 10px 30px rgba(0, 0, 0, 0.1),
                        0 6px 35px rgba(0,0,0,0.07);
        }
        #playgroundView .search-input {
            width: 100%; padding: 20px 20px 10px 20px; background: transparent; border: none; outline: none;
            color: var(--color-text); font-size: 1rem; font-family: var(--font-body); font-weight: 400;
            resize: none; overflow-y: hidden; line-height: 1.6; min-height: 70px; box-sizing: border-box;
        }
        #playgroundView .search-input::placeholder { color: var(--color-placeholder); opacity: 1; font-weight: 400; }
        #playgroundView .toolbar-bottom {
            display: flex; justify-content: space-between; align-items: center;
            padding: 8px 16px; background-color: var(--color-card-bg); position: relative; z-index: 1;
        }
        #playgroundView .model-selector-container { /* Keep simple or copy full from public */ }
        #playgroundView .model-selector-btn {
            position: relative; background-color: var(--color-hover-bg); border: none;
            color: var(--color-text); padding: 8px 15px; border-radius: var(--border-radius-element);
            cursor: pointer; font-size: 0.85rem; font-weight: 500;
            display: flex; align-items: center; gap: 8px;
            transition: box-shadow 0.2s ease; z-index: 3; overflow: hidden;
        }
        #playgroundView .model-selector-btn::before { /* Copied from public for consistency */
            content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            border-radius: var(--border-radius-element); padding: 1.5px;
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%; animation: synchronizedNeonShine 5s linear infinite;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: subtract; mask-composite: subtract;
            z-index: -1; opacity: 0.8; transition: opacity 0.3s ease;
        }
        #playgroundView .model-selector-btn:hover::before { opacity: 1; }
        #playgroundView .model-selector-btn:hover { box-shadow: 0 0 8px rgba(var(--color-cta), 0.3); }
        #playgroundView .model-selector-btn #playgroundCurrentModelName { max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        #playgroundView .model-selector-btn .fa-chevron-down { font-size: 0.75em; transition: transform 0.2s ease; }
        #playgroundView .model-selector-btn.open .fa-chevron-down { transform: rotate(180deg); }
        #playgroundView .model-dropdown { /* This is #modelDropdownList in public */
            position: absolute; background-color: var(--color-card-bg);
            border: 1px solid var(--color-shadow-outset-primary);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1); border-radius: var(--border-radius-element);
            z-index: 110; opacity: 0; visibility: hidden; transform: translateY(-10px);
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
            padding: 8px; box-sizing: border-box; width: max-content; min-width: 260px;
        }
        #playgroundView .model-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0); }
        #playgroundView .model-search-container { position: relative; margin-bottom: 8px; }
        #playgroundView .model-search-container .fa-search { position: absolute; top: 50%; left: 12px; transform: translateY(-50%); color: var(--color-placeholder); font-size: 0.9em; }
        #playgroundView #playgroundModelSearchInput { width: 100%; padding: 10px 12px 10px 35px; border: 1px solid var(--color-shadow-outset-primary); border-radius: 6px; font-size: 0.85rem; box-sizing: border-box; outline: none; }
        #playgroundView #playgroundModelSearchInput:focus { border-color: var(--color-cta); box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); }
        #playgroundView .model-dropdown ul { list-style: none; padding: 0; margin: 0; max-height: 200px; overflow-y: auto;}
        #playgroundView .model-dropdown ul::-webkit-scrollbar { width: 6px; }
        #playgroundView .model-dropdown ul::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 3px; }
        #playgroundView .model-dropdown ul::-webkit-scrollbar-thumb { background: #ccc; border-radius: 3px; }
        #playgroundView .model-dropdown ul::-webkit-scrollbar-thumb:hover { background: #aaa; }
        #playgroundView .model-dropdown li { padding: 10px 12px; font-size: 0.85rem; color: var(--color-text); cursor: pointer; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; display: flex; align-items: center; gap: 10px; }
        #playgroundView .model-dropdown li img, #playgroundView .model-dropdown li .model-icon { width: 18px; height: 18px; object-fit: contain; flex-shrink: 0; border-radius: 3px; text-align: center; line-height: 18px; font-size: 16px; transition: color 0.2s ease, filter 0.2s ease; }
        /* Icon colors from public */
        #playgroundView .model-dropdown li .model-icon.google-blue { color: #4285F4; }
        #playgroundView .model-dropdown li .model-icon.google-green { color: #34A853; }
        #playgroundView .model-dropdown li .model-icon.google-red { color: #EA4335; }
        #playgroundView .model-dropdown li .model-icon.star-black { color: #000000; }
        #playgroundView .model-dropdown li .model-icon.star-orange { color: #FFA500; }
        #playgroundView .model-dropdown li .model-icon.star-blue-gemini { color: #007BFF; }
        #playgroundView .model-dropdown li .model-icon.star-darkgray { color: #696969; }
        #playgroundView .model-dropdown li .model-icon.star-skyblue { color: #87CEEB; }

        #playgroundView .model-dropdown li span { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        #playgroundView .model-dropdown li:hover { background-color: var(--color-hover-bg); }
        #playgroundView .model-dropdown li.selected-model-item { background-color: #3B82F6; color: var(--color-cta-text); font-weight: 500; }
        #playgroundView .model-dropdown li.selected-model-item img { filter: brightness(0) invert(1); }
        #playgroundView .model-dropdown li.selected-model-item .model-icon { color: var(--color-cta-text); }

        #playgroundView .search-actions { display: flex; align-items: center; gap: 12px; z-index: 2; }
        #playgroundView .action-icon {
            font-size: 1.2rem; color: var(--color-icon-gray); cursor: pointer;
            transition: color 0.2s ease, transform 0.2s ease; padding: 8px; border-radius: 50%;
        }
        #playgroundView .action-icon:hover { color: var(--color-cta); background-color: var(--color-hover-bg); transform: scale(1.1); }
        #playgroundView #dynamicContentArea { /* This is #dynamicContentArea in public */
            padding: 0 16px 16px 16px; background-color: var(--color-card-bg);
        }
        #playgroundView #dynamicContentArea > *:first-child:not(.hidden) { margin-top: 10px; }
        #playgroundView .analyzing-indicator { text-align: center; padding: 20px 10px; font-size: 1rem; color: var(--color-icon-gray); }
        #playgroundView .analyzing-indicator .fa-spinner { margin-right: 10px; font-size: 1.2em; animation: spin 6s linear infinite; }
        #playgroundView .simple-suggestions-container { background-color: var(--color-card-bg); max-height: 280px; overflow-y: auto; border-radius: var(--border-radius-element); border: 1px solid var(--color-shadow-outset-primary); }
        #playgroundView .simple-suggestion-item { padding: 10px 15px; font-size: 0.9rem; color: var(--color-text); cursor: pointer; border-bottom: 1px solid var(--color-shadow-outset-primary); transition: background-color 0.2s ease; }
        #playgroundView .simple-suggestion-item:last-child { border-bottom: none; }
        #playgroundView .simple-suggestion-item:hover { background-color: var(--color-hover-bg); }
        #playgroundView .simple-suggestion-item strong { font-weight: 600; }
        #playgroundView .enhanced-prompt-suggestions { } /* Placeholder */
        /* Favorite prompts section (as in public, if used within playground's dynamic area) */
        #playgroundView .favorite-prompts-section { border-top: 1px solid var(--color-shadow-outset-primary); }
        #playgroundView .favorite-prompts-section h4 { font-size: 0.9rem; font-weight: 600; color: var(--color-text); margin: 0 0 10px 0; display: flex; align-items: center; padding-top:15px; }
        #playgroundView .favorite-prompts-section h4 .fa-star { margin-right: 8px; color: var(--color-star-active); }
        #playgroundView .favorite-prompts-list .enhanced-prompt-item { padding: 10px 12px; margin-bottom:12px; }
        /* Add the "select-prompt-button" style from public for consistency if it's used dynamically */
        #playgroundView .select-prompt-button { /* From public */
            position: relative; display: inline-flex; align-items: center; gap: 8px;
            background-color: var(--color-hover-bg); color: var(--color-text); padding: 8px 15px;
            border-radius: var(--border-radius-element); border: none; font-size: 0.85rem;
            font-weight: 500; cursor: pointer;
            transition: box-shadow 0.2s ease, background-color 0.2s ease, color 0.2s ease;
            text-decoration: none; line-height: 1.5; z-index: 1; overflow: hidden; margin-top: 10px;
        }
        #playgroundView .select-prompt-button::before { /* From public */
            content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0; border-radius: inherit; padding: 1.5px;
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%; animation: synchronizedNeonShine 5s linear infinite;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: subtract; mask-composite: subtract;
            z-index: -1; opacity: 0.8; transition: opacity 0.3s ease;
        }
        #playgroundView .select-prompt-button:hover::before { opacity: 1; }
        #playgroundView .select-prompt-button:hover { box-shadow: 0 0 8px rgba(var(--color-cta), 0.3); background-color: var(--color-cta); color: var(--color-cta-text); }
        #playgroundView .select-prompt-button:hover i { color: var(--color-cta-text); }

        /* Playground "Save Prompt" button addition */
        #playgroundView .toolbar-bottom .playground-save-action {
            margin-left: auto; /* Push to the right of model selector */
            padding-right: 15px; /* Space before main action icons */
        }


        /* --- SETTINGS Specific Styles (Unchanged) --- */
        #settingsView .settings-tabs { /* ... keep as is */ }
        #settingsView .form-group { /* ... keep as is */ }


        /* Responsive */
        @media (max-width: 992px) {
            .sidebar { transform: translateX(calc(-1 * var(--sidebar-width))); }
            .sidebar.open { transform: translateX(0); box-shadow: 5px 0 25px rgba(0,0,0,0.1); }
            .main-content-wrapper { margin-left: 0; width: 100%; }
            .menu-toggle-btn { display: block; }
            .header-title { font-size: 1.3rem; }
            .prompts-controls-bar { flex-direction: column; align-items: flex-start; }
            #playgroundView .search-area-container { max-width: 95%; }
        }
        @media (max-width: 768px) {
            .grid-container { grid-template-columns: 1fr; }
            #dashboardView .welcome-section h2 { font-size: 1.6rem; }
            .content-area { padding: 20px; }
            .app-header { padding: 0 20px; }
            .btn-gradient-action { padding: 8px 18px; font-size: 0.85rem; }
            .prompts-filter-panel { flex-direction: column; align-items: stretch; }
            .prompts-filter-panel .filter-group { width:100%; }
            .prompts-filter-panel .form-control, .prompts-filter-panel .btn-filter-toggle { width:100%; box-sizing: border-box; text-align: center;}
            #playgroundView .toolbar-bottom { flex-wrap: wrap; gap: 10px; padding: 8px;}
            #playgroundView .model-selector-btn { flex-grow: 1; justify-content: center;}
            #playgroundView .search-actions { flex-grow: 1; justify-content: flex-end;}
            #playgroundView .toolbar-bottom .playground-save-action { width: 100%; order: -1; padding-right:0; text-align: center; } /* Make save button full width on mobile */
            #playgroundView .toolbar-bottom .playground-save-action .stylish-action-button { width: 100%; justify-content: center;}
        }

    </style>
</head>
<body>

    <aside class="sidebar" id="sidebar">
        <div class="sidebar-logo">allhub</div>
        <nav class="sidebar-nav">
            <ul>
                <li class="nav-item"><a href="#" class="nav-link active" data-view="dashboard"><i class="fas fa-th-large"></i> Dashboard</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="my-prompts"><i class="fas fa-lightbulb"></i> My Prompts</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="history"><i class="fas fa-history"></i> History</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="playground"><i class="fas fa-rocket"></i> Playground</a></li>
                <li class="nav-item"><a href="#" class="nav-link" data-view="settings"><i class="fas fa-cog"></i> Settings</a></li>
            </ul>
        </nav>
        <div class="sidebar-user-profile">
            <img src="https://i.pravatar.cc/80?u=UserElenaV" alt="User Avatar">
            <div class="user-info">
                <p class="user-name">Elena V.</p>
                <p class="user-email"><EMAIL></p>
            </div>
        </div>
    </aside>

    <div class="main-content-wrapper" id="mainContentWrapper">
        <header class="app-header">
            <div class="header-title-container">
                <button class="menu-toggle-btn" id="menuToggleBtn"><i class="fas fa-bars"></i></button>
                <h2 class="header-title" id="headerTitle">Dashboard</h2>
            </div>
            <div class="header-actions">
                <a href="#" class="btn-gradient-action" id="newPromptBtn"><i class="fas fa-plus-circle"></i> New Prompt</a>
                <div class="user-menu">
                    <button class="user-menu-btn" id="userMenuBtn">
                        <img src="https://i.pravatar.cc/80?u=UserElenaV" alt="User Avatar">
                        <span class="user-name-header">Elena V.</span>
                        <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="user-dropdown" id="userDropdown">
                        <a href="#"><i class="fas fa-user-circle"></i> My Profile</a>
                        <a href="#"><i class="fas fa-credit-card"></i> Subscription</a>
                        <div class="divider"></div>
                        <a href="#"><i class="fas fa-sign-out-alt"></i> Logout</a>
                    </div>
                </div>
            </div>
        </header>

        <main class="content-area">
            <!-- DASHBOARD VIEW (Reverted to first private version's structure) -->
            <div id="dashboardView" class="view-content">
                <div class="welcome-section">
                    <h2>Welcome back, Elena!</h2>
                    <p>Ready to craft some amazing prompts today?</p>
                </div>

                <div class="section-title"><i class="fas fa-chart-line"></i> Your Activity Snapshot</div>
                <div class="grid-container" style="margin-bottom: 30px;">
                    <div class="dashboard-card">
                        <div class="card-header"><h3>Saved Prompts</h3><i class="fas fa-save icon"></i></div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">27</p>
                        <p class="text-muted" style="font-size:0.9rem;">Organized and ready to use.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header"><h3>Prompts Used (Month)</h3><i class="fas fa-cogs icon"></i></div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-cta);">152</p>
                        <p class="text-muted" style="font-size:0.9rem;">Generating ideas non-stop.</p>
                    </div>
                    <div class="dashboard-card">
                        <div class="card-header"><h3>Active Favorites</h3><i class="fas fa-star icon" style="color: var(--color-star-active);"></i></div>
                        <p style="font-size: 2.5rem; font-weight: 700; margin: 10px 0; color: var(--color-star-active);">8</p>
                        <p class="text-muted" style="font-size:0.9rem;">Your star prompts at a glance.</p>
                    </div>
                </div>

                <div class="section-title"><i class="fas fa-bolt"></i> Quick Access: Favorite Prompts</div>
                <div id="favoritePromptsDashboard"> <!-- This will list favorites -->
                    <!-- Example Favorite Prompt Card (using .enhanced-prompt-item style) -->
                    <div class="enhanced-prompt-item">
                        <div class="enhanced-prompt-item-header">
                            <h5>Enhance Article with CoT</h5>
                            <i class="fas fa-star star-icon favorited" title="Remove from favorites"></i>
                        </div>
                        <div class="enhanced-prompt-item-meta-minimal">
                            <span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input: 195</span>
                            <span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output: ~700</span>
                            <span class="meta-item"><i class="far fa-clock"></i> Time: ~2s</span>
                        </div>
                        <p class="full-prompt-display-summary">Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process: Identify key components...</p>
                        <div class="prompt-actions">
                             <button class="stylish-action-button"><i class="fas fa-play-circle"></i> Use in Playground</button>
                             <button class="stylish-action-button"><i class="fas fa-edit"></i> Edit</button>
                        </div>
                    </div>
                     <!-- Add more favorite prompt cards here -->
                </div>
            </div>

            <!-- MY PROMPTS VIEW (New "Super Cool" Design) -->
            <div id="myPromptsView" class="view-content hidden">
                <div class="section-title"><i class="fas fa-lightbulb"></i> My Prompts</div>
                <div class="prompts-controls-bar">
                    <button class="btn-gradient-action" id="createNewPromptFromMyPromptsBtn"><i class="fas fa-plus"></i> Create New Prompt</button>
                    <div class="prompts-view-toggle">
                        <button class="active" data-viewmode="grid"><i class="fas fa-th-large"></i> Grid</button>
                        <button data-viewmode="list"><i class="fas fa-list"></i> List</button>
                    </div>
                </div>
                <div class="prompts-filter-panel">
                    <div class="filter-group">
                        <label for="filterTechnique">Technique:</label>
                        <select id="filterTechnique" class="form-control">
                            <option value="">All Techniques</option>
                            <option value="cot">Chain of Thought (CoT)</option>
                            <option value="few-shot">Few-Shot</option>
                            <option value="role-play">Role Playing</option>
                            <option value="zero-shot">Zero-Shot</option>
                            <option value="creative-writing">Creative Writing</option>
                        </select>
                    </div>
                    <div class="filter-group">
                        <button class="btn-filter-toggle" data-filter="most-used">Most Used</button>
                        <button class="btn-filter-toggle" data-filter="recent">Recent</button>
                        <button class="btn-filter-toggle active" data-filter="favorites">Favorites <i class="fas fa-star" style="color: var(--color-star-active); margin-left: 5px;"></i></button>
                    </div>
                     <div class="filter-group" style="margin-left: auto;">
                        <input type="text" class="form-control" placeholder="Search my prompts..." style="min-width: 250px;">
                    </div>
                </div>

                <div id="myPromptsGrid" class="grid-container">
                    <!-- Example Prompt Card v2 -->
                    <div class="prompt-card-item-v2">
                        <div class="prompt-card-header-v2">
                            <h5 class="prompt-card-title-v2">Social Media Campaign Strategy</h5>
                            <i class="fas fa-star prompt-card-fav-icon-v2 favorited" title="Toggle Favorite"></i>
                        </div>
                        <div class="prompt-card-tags-v2">
                            <span class="tag">Marketing</span> <span class="tag">Strategy</span> <span class="tag">SaaS</span>
                        </div>
                        <p class="prompt-card-summary-v2">Outline a comprehensive social media campaign strategy for a new SaaS product launch, targeting small businesses. Include key platforms, content pillars, and KPIs to track success over the first 90 days.</p>
                        <div class="prompt-card-footer-v2">
                            <div class="prompt-card-stats-v2">
                                <span><i class="fas fa-sync-alt"></i> Used: 12 times</span> •
                                <span>Last used: 2d ago</span>
                            </div>
                            <div class="prompt-card-actions-v2">
                                 <button class="stylish-action-button" title="Use in Playground"><i class="fas fa-play"></i></button>
                                 <button class="stylish-action-button" title="Edit"><i class="fas fa-edit"></i></button>
                                 <button class="stylish-action-button delete" title="Delete"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    </div>
                    <!-- Add more prompt cards v2 -->
                     <div class="prompt-card-item-v2">
                        <div class="prompt-card-header-v2">
                            <h5 class="prompt-card-title-v2">Blog Post: AI in Education</h5>
                            <i class="far fa-star prompt-card-fav-icon-v2" title="Toggle Favorite"></i>
                        </div>
                        <div class="prompt-card-tags-v2">
                            <span class="tag">Few-Shot</span> <span class="tag">Blog</span> <span class="tag">Education</span>
                        </div>
                        <p class="prompt-card-summary-v2">Generate a 600-word blog post discussing the pros and cons of AI in modern education, using a balanced and informative tone. Provide 2 examples of AI tools.</p>
                         <div class="prompt-card-footer-v2">
                            <div class="prompt-card-stats-v2">
                                <span><i class="fas fa-sync-alt"></i> Used: 5 times</span> •
                                <span>Last used: 1w ago</span>
                            </div>
                            <div class="prompt-card-actions-v2">
                                 <button class="stylish-action-button" title="Use in Playground"><i class="fas fa-play"></i></button>
                                 <button class="stylish-action-button" title="Edit"><i class="fas fa-edit"></i></button>
                                 <button class="stylish-action-button delete" title="Delete"><i class="fas fa-trash"></i></button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- HISTORY VIEW (Unchanged from previous private version) -->
            <div id="historyView" class="view-content hidden">
                 <div class="section-title"><i class="fas fa-history"></i> Prompt History</div>
                 <!-- ... content from previous response ... -->
            </div>

            <!-- PLAYGROUND VIEW (Exact Public HTML integrated here) -->
            <div id="playgroundView" class="view-content hidden">
                <div class="section-title"><i class="fas fa-rocket"></i> Playground</div>
                <!-- COPIED FROM PUBLIC HTML - START -->
                <div class="search-area-container" id="playgroundSearchAreaContainer"> <!-- Renamed ID -->
                    <div class="search-bar-card" id="playgroundSearchBarCard"> <!-- Renamed ID -->
                        <textarea class="search-input" id="playgroundSearchInput" placeholder="Type your prompt here..." rows="1"></textarea> <!-- Renamed ID -->

                        <div class="toolbar-bottom">
                            <div class="model-selector-container">
                                <button class="model-selector-btn" id="playgroundModelSelectorBtn"> <!-- Renamed ID -->
                                    <span id="playgroundCurrentModelName">Model</span> <i class="fas fa-chevron-down"></i> <!-- Renamed ID -->
                                </button>
                            </div>
                            <!-- ADDED SAVE BUTTON FOR PLAYGROUND -->
                            <div class="playground-save-action">
                                <button class="stylish-action-button" id="playgroundSavePromptBtn"><i class="fas fa-save"></i> Save This Prompt</button>
                            </div>

                            <div class="search-actions">
                                <i class="fas fa-microphone action-icon" id="playgroundMicBtn" title="Voice Input"></i> <!-- Renamed ID -->
                                <i class="fas fa-wand-magic-sparkles action-icon" id="playgroundGuidedCreateBtn" title="Guided Creation"></i> <!-- Renamed ID -->
                                <i class="fas fa-copy action-icon" id="playgroundCopyBtn" title="Copy Prompt"></i> <!-- Renamed ID -->
                                <i class="fas fa-paper-plane action-icon" id="playgroundSendBtn" title="Send Prompt"></i> <!-- Renamed ID -->
                            </div>
                        </div>

                        <div id="playgroundDynamicContentArea" class="hidden"> <!-- Renamed ID -->
                            <div class="analyzing-indicator hidden" id="playgroundAnalyzingIndicator"> <!-- Renamed ID -->
                                <i class="fas fa-spinner fa-spin"></i> Analyzing your prompt...
                            </div>
                            <div class="simple-suggestions-container hidden" id="playgroundSimplePromptSuggestionsContainer"></div> <!-- Renamed ID -->
                            <div class="enhanced-prompt-suggestions hidden" id="playgroundEnhancedPromptSuggestionsContainer"></div> <!-- Renamed ID -->
                             <div class="favorite-prompts-section hidden" id="playgroundFavoritePromptsSection"> <!-- Renamed ID -->
                                <h4><i class="fas fa-star"></i> Popular Prompts</h4>
                                <div class="favorite-prompts-list" id="playgroundFavoritePromptsList"></div> <!-- Renamed ID -->
                            </div>
                        </div>
                    </div>

                    <div class="model-dropdown hidden" id="playgroundModelDropdownList"> <!-- Renamed ID -->
                        <div class="model-search-container">
                            <i class="fas fa-search"></i>
                            <input type="text" id="playgroundModelSearchInput" placeholder="Search models"> <!-- Renamed ID -->
                        </div>
                        <ul></ul>
                    </div>
                </div>
                 <!-- COPIED FROM PUBLIC HTML - END -->
            </div>

            <!-- SETTINGS VIEW (Unchanged from previous private version) -->
            <div id="settingsView" class="view-content hidden">
                 <div class="section-title"><i class="fas fa-cog"></i> Settings</div>
                 <!-- ... content from previous response ... -->
            </div>
        </main>
    </div>

<script>
    // --- MAIN PRIVATE AREA SCRIPT ---
    document.addEventListener('DOMContentLoaded', () => {
        const userMenuBtn = document.getElementById('userMenuBtn');
        const userDropdown = document.getElementById('userDropdown');
        const menuToggleBtn = document.getElementById('menuToggleBtn');
        const sidebar = document.getElementById('sidebar');
        const headerTitle = document.getElementById('headerTitle');
        const navLinks = document.querySelectorAll('.sidebar-nav .nav-link');
        const views = document.querySelectorAll('.view-content');
        const newPromptBtn = document.getElementById('newPromptBtn');
        const createNewPromptMyPromptsBtn = document.getElementById('createNewPromptFromMyPromptsBtn');

        // User menu
        if (userMenuBtn && userDropdown) {
            userMenuBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                userDropdown.classList.toggle('visible');
            });
        }
        document.addEventListener('click', (e) => {
            if (userDropdown && userDropdown.classList.contains('visible') && !userMenuBtn.contains(e.target) && !userDropdown.contains(e.target)) {
                userDropdown.classList.remove('visible');
            }
        });

        // Sidebar toggle
        if (menuToggleBtn && sidebar) {
            menuToggleBtn.addEventListener('click', () => sidebar.classList.toggle('open'));
        }
        document.addEventListener('click', (e) => {
            if (sidebar && sidebar.classList.contains('open') && !sidebar.contains(e.target) && !menuToggleBtn.contains(e.target)) {
                sidebar.classList.remove('open');
            }
        });

        function switchToView(viewId) {
            navLinks.forEach(l => l.classList.remove('active'));
            const activeLink = document.querySelector(`.sidebar-nav .nav-link[data-view="${viewId}"]`);
            if (activeLink) {
                activeLink.classList.add('active');
                headerTitle.textContent = activeLink.textContent.trim();
            } else if (viewId === 'playground') {
                 headerTitle.textContent = 'Playground';
                 const playgroundLink = document.querySelector(`.sidebar-nav .nav-link[data-view="playground"]`);
                 if(playgroundLink) playgroundLink.classList.add('active');
            }

            views.forEach(view => {
                if (view.id === `${viewId}View`) {
                    view.classList.remove('hidden');
                } else {
                    view.classList.add('hidden');
                }
            });

            if (window.innerWidth <= 992 && sidebar.classList.contains('open')) {
                sidebar.classList.remove('open');
            }

            // If switching to Playground, initialize its specific JS
            if (viewId === 'playground' && typeof initializePlaygroundScript === 'function' && !window.playgroundScriptInitialized) {
                initializePlaygroundScript();
                window.playgroundScriptInitialized = true; // Prevent re-initialization
            }
        }

        navLinks.forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                switchToView(link.dataset.view);
            });
        });

        if (newPromptBtn) {
            newPromptBtn.addEventListener('click', (e) => { e.preventDefault(); switchToView('playground'); });
        }
        if (createNewPromptMyPromptsBtn) {
            createNewPromptMyPromptsBtn.addEventListener('click', (e) => { e.preventDefault(); switchToView('playground'); });
        }

        // My Prompts - Filter Toggles
        const filterToggles = document.querySelectorAll('.prompts-filter-panel .btn-filter-toggle');
        filterToggles.forEach(toggle => {
            toggle.addEventListener('click', () => {
                // Example: allow only one "status" like filter active or toggle individually
                // For this demo, just toggling the clicked one
                toggle.classList.toggle('active');
                console.log("Filter toggled:", toggle.dataset.filter, toggle.classList.contains('active'));
                // Add actual filtering logic here
            });
        });


        // Settings Tabs (if not already handled by global script)
        const settingsTabLinks = document.querySelectorAll('#settingsView .settings-tabs .tab-link');
        const settingsTabContents = document.querySelectorAll('#settingsView .settings-tab-content');
        settingsTabLinks.forEach(link => {
            link.addEventListener('click', (e) => { /* ... tab logic ... */ });
        });

        // Initialize
        const initialActiveLink = document.querySelector('.sidebar-nav .nav-link.active');
        if (initialActiveLink) {
            switchToView(initialActiveLink.dataset.view);
        } else if (navLinks.length > 0) {
            switchToView(navLinks[0].dataset.view);
        }
    });

    // --- PLAYGROUND SCRIPT (ADAPTED FROM PUBLIC HTML) ---
    // Encapsulate in a function to call when Playground view is active
    function initializePlaygroundScript() {
        console.log("Initializing Playground Script...");
        // --- CONSTANTES Y VARIABLES GLOBALES (Playground Scoped) ---
        // IMPORTANT: All IDs are prefixed with 'playground'
        const searchAreaContainer = document.getElementById('playgroundSearchAreaContainer');
        const searchBarCard = document.getElementById('playgroundSearchBarCard');
        const searchInput = document.getElementById('playgroundSearchInput');
        const guidedCreateBtn = document.getElementById('playgroundGuidedCreateBtn');
        const micBtn = document.getElementById('playgroundMicBtn');
        const simplePromptSuggestionsContainer = document.getElementById('playgroundSimplePromptSuggestionsContainer');
        const analyzingIndicator = document.getElementById('playgroundAnalyzingIndicator');
        const enhancedPromptSuggestionsContainer = document.getElementById('playgroundEnhancedPromptSuggestionsContainer');
        const favoritePromptsSection = document.getElementById('playgroundFavoritePromptsSection');
        const favoritePromptsList = document.getElementById('playgroundFavoritePromptsList');
        const dynamicContentArea = document.getElementById('playgroundDynamicContentArea');

        const modelSelectorBtn = document.getElementById('playgroundModelSelectorBtn');
        const currentModelNameSpan = document.getElementById('playgroundCurrentModelName');
        const modelDropdownList = document.getElementById('playgroundModelDropdownList');
        const modelSearchInput = document.getElementById('playgroundModelSearchInput');
        const modelListUl = modelDropdownList.querySelector('ul'); // Assumes ul exists

        let isGuidedModeActive = false;
        // Playground might have its own favorites context or use a global one
        let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHubPlayground')) || []; // Or use global 'favoritePromptsAllHub'
        let blockSuggestionsOnNextFocus = false;

        // --- DATOS DE MODELOS ACTUALIZADOS (Playground Scoped) ---
        const modelsData = [ // This should ideally come from a shared source or config
            { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
            { id: "anthropic-claude-3", displayName: "Anthropic: Claude 3", shortName: "Claude 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-orange" },
            { id: "google-gemini-1.5", displayName: "Google: Gemini 1.5", shortName: "Gemini 1.5", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-blue-gemini" },
            { id: "meta-llama-3", displayName: "Meta: LLaMA 3", shortName: "LLaMA 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-skyblue" },
            // ... more models
        ];

        // --- HELPER FUNCTIONS (Copied from public, ensure no global conflicts) ---
        function pg_debounce(func, delay) { let timeout; const debounced = function(...args) { const context = this; clearTimeout(timeout); timeout = setTimeout(() => func.apply(context, args), delay); }; debounced.cancel = function() { clearTimeout(timeout); }; return debounced; }
        function pg_autoResizeTextarea(textarea) { if(!textarea) return; textarea.style.height = 'auto'; let scrollHeight = textarea.scrollHeight; textarea.style.height = scrollHeight + 'px'; textarea.style.overflowY = 'hidden'; }
        function pg_formatFullPromptForDisplay(fullPromptText) { if (!fullPromptText) return ""; let html = fullPromptText; html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim, (match) => `<span class="prompt-heading">${match.trim()}</span>`); return html; }

        function pg_updateDynamicContentAreaVisibility() {
            if (!dynamicContentArea || !simplePromptSuggestionsContainer || !enhancedPromptSuggestionsContainer || !favoritePromptsSection || !analyzingIndicator) return;
            const isSimpleVisible = !simplePromptSuggestionsContainer.classList.contains('hidden') && simplePromptSuggestionsContainer.innerHTML.trim() !== '';
            const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedPromptSuggestionsContainer.innerHTML.trim() !== '';
            const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && favoritePromptsList.innerHTML.trim() !== '';
            const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden');

            if (isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible) {
                dynamicContentArea.classList.remove('hidden');
            } else {
                dynamicContentArea.classList.add('hidden');
            }
        }

        function pg_hideAllDynamicContentExcept(exceptContainer = null) {
            [simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => {
                if (container && container !== exceptContainer) {
                    container.classList.add('hidden');
                    if (container !== analyzingIndicator) container.innerHTML = '';
                }
            });
        }

        function pg_setDefaultModel() {
            if (!currentModelNameSpan) return;
            if (modelsData.length > 0) {
                const defaultModel = modelsData[0];
                currentModelNameSpan.textContent = defaultModel.shortName;
                currentModelNameSpan.dataset.selectedModelId = defaultModel.id;
            } else {
                currentModelNameSpan.textContent = "Model";
                delete currentModelNameSpan.dataset.selectedModelId;
            }
        }

        function pg_populateModelDropdown() {
             if (!modelListUl || !modelSearchInput || !currentModelNameSpan) return;
            modelListUl.innerHTML = '';
            const searchTerm = modelSearchInput.value.toLowerCase();
            const currentSelectedModelId = currentModelNameSpan.dataset.selectedModelId;

            modelsData.filter(model => model.displayName.toLowerCase().includes(searchTerm)).forEach(model => {
                const li = document.createElement('li');
                li.dataset.modelId = model.id;
                li.dataset.modelShortName = model.shortName;
                li.dataset.modelDisplayName = model.displayName;

                let iconElement; // ... (icon creation logic from public)
                if (model.iconType === "fa") {
                    iconElement = document.createElement('i');
                    iconElement.className = `model-icon ${model.iconClass} ${model.iconColorClass || ''}`;
                } else { /* img type ... */ }
                li.appendChild(iconElement);


                const span = document.createElement('span');
                span.textContent = model.displayName;
                li.appendChild(span);

                if (currentSelectedModelId && currentSelectedModelId === model.id) {
                    li.classList.add('selected-model-item');
                }

                li.addEventListener('click', (e) => {
                    e.stopPropagation();
                    currentModelNameSpan.textContent = model.shortName;
                    currentModelNameSpan.dataset.selectedModelId = model.id;
                    modelListUl.querySelectorAll('li').forEach(item => item.classList.remove('selected-model-item'));
                    li.classList.add('selected-model-item');
                    modelDropdownList.classList.remove('visible');
                    modelDropdownList.classList.add('hidden');
                    if(modelSelectorBtn) modelSelectorBtn.classList.remove('open');
                    modelDropdownList.style.transform = 'translateY(-10px)';
                    blockSuggestionsOnNextFocus = true;
                    if(searchInput) searchInput.focus();
                    pg_hideAllDynamicContentExcept();
                    pg_updateDynamicContentAreaVisibility();
                });
                modelListUl.appendChild(li);
            });
        }

        if (modelSelectorBtn) {
            modelSelectorBtn.addEventListener('click', (e) => {
                e.stopPropagation();
                if (!modelDropdownList || !searchBarCard || !searchAreaContainer) return;
                const isCurrentlyHidden = modelDropdownList.classList.contains('hidden');
                pg_exitGuidedModeIfNeeded();
                pg_hideAllDynamicContentExcept();

                if (isCurrentlyHidden) {
                    const cardRect = searchBarCard.getBoundingClientRect();
                    const containerRect = searchAreaContainer.getBoundingClientRect();
                    const buttonRect = modelSelectorBtn.getBoundingClientRect();
                    modelDropdownList.style.top = (cardRect.bottom - containerRect.top + 8) + 'px';
                    modelDropdownList.style.left = (buttonRect.left - containerRect.left) + 'px';
                    modelDropdownList.style.transform = 'translateY(-10px)';
                    modelDropdownList.classList.remove('hidden');
                    void modelDropdownList.offsetHeight;
                    modelDropdownList.classList.add('visible');
                    modelSelectorBtn.classList.add('open');
                    if(modelSearchInput) {
                        modelSearchInput.value = '';
                        pg_populateModelDropdown();
                        modelSearchInput.focus();
                    }
                } else {
                    modelDropdownList.classList.remove('visible');
                    modelDropdownList.classList.add('hidden');
                    modelSelectorBtn.classList.remove('open');
                    modelDropdownList.style.transform = 'translateY(-10px)';
                }
                pg_updateDynamicContentAreaVisibility();
            });
        }

        if (modelSearchInput) {
            modelSearchInput.addEventListener('input', pg_populateModelDropdown);
            modelSearchInput.addEventListener('click', (e) => e.stopPropagation());
        }
        
        // --- FAVORITES, PROMPT CARD CREATION (Simplified for brevity, copy full from public if needed) ---
        function pg_createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) {
            // ... This function would be copied from public script and adapted for playground IDs/styles ...
            // Make sure it uses #playgroundView .select-prompt-button for styling
            const card = document.createElement('div');
            // Example: card.querySelector('.select-prompt-button').addEventListener('click', () => { searchInput.value = ... });
            return card;
        }

        function pg_renderFavoritePrompts() { /* ... copy from public ... */ }
        function pg_toggleFavorite(promptData, starIconElement) { /* ... copy from public ... */ }

        // --- GUIDED MODE (Simplified for brevity) ---
        function pg_displayGuidedPromptList(suggestions) { /* ... copy from public ... */ }
        function pg_generateGuidedPrompts() { /* ... copy from public, this returns example prompts ... */ return []; }
        async function pg_handleGuidedCreateClick() { /* ... copy from public ... */ }
        function pg_exitGuidedModeIfNeeded() { /* ... copy from public ... */ }

        // --- SIMPLE SUGGESTIONS (Simplified for brevity) ---
        function pg_generateSimpleSuggestions(inputText) { /* ... copy from public ... */ return []; }
        const pg_handleSimpleInput = pg_debounce(function() { /* ... copy from public ... */ }, 300);

        // --- EVENT LISTENERS (Playground Scoped) ---
        if (searchInput) {
            searchInput.addEventListener('input', () => {
                pg_autoResizeTextarea(searchInput);
                // ... rest of input logic from public ...
                pg_handleSimpleInput();
            });
            searchInput.addEventListener('focus', () => {
                if (searchBarCard) searchBarCard.classList.add('always-focused', 'focused-card');
                pg_autoResizeTextarea(searchInput);
                // ... rest of focus logic from public ...
                 if (searchInput.value === '') { pg_renderFavoritePrompts(); }
                 else { pg_handleSimpleInput(); }
            });
            searchInput.addEventListener('blur', () => {
                // ... blur logic from public, carefully check activeElement and contains() ...
                setTimeout(() => {
                    const activeEl = document.activeElement;
                    if (!searchBarCard.contains(activeEl) && !modelDropdownList.contains(activeEl)) { // Simplified
                        searchBarCard.classList.remove('always-focused', 'focused-card');
                        pg_hideAllDynamicContentExcept();
                        pg_updateDynamicContentAreaVisibility();
                    }
                }, 150);
            });
        }

        if (guidedCreateBtn) guidedCreateBtn.addEventListener('click', pg_handleGuidedCreateClick);
        if (micBtn) micBtn.addEventListener('click', () => console.log("Playground Mic pressed"));

        // Global click listener for dropdowns etc (careful with multiple global listeners)
        // This might be better handled by the main private area script or a more robust component system
        document.addEventListener('click', (event) => { // This could conflict if not managed well
            if (modelDropdownList && !modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target) && modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                if(modelSelectorBtn) modelSelectorBtn.classList.remove('open');
                modelDropdownList.style.transform = 'translateY(-10px)';
            }
             // Logic for hiding dynamic content area when clicking outside
             if (searchBarCard && !searchBarCard.contains(event.target) &&
                modelDropdownList && !modelDropdownList.contains(event.target) &&
                dynamicContentArea && !dynamicContentArea.contains(event.target)
                ) {
                if (!isGuidedModeActive && (!modelDropdownList || !modelDropdownList.classList.contains('visible'))) {
                    pg_hideAllDynamicContentExcept();
                    pg_updateDynamicContentAreaVisibility();
                }
            }
        });

        const copyBtn = document.getElementById('playgroundCopyBtn');
        if(copyBtn) copyBtn.addEventListener('click', (e) => { e.stopPropagation(); if(searchInput.value) navigator.clipboard.writeText(searchInput.value); });
        
        const sendBtn = document.getElementById('playgroundSendBtn');
        if(sendBtn) sendBtn.addEventListener('click', (e) => { e.stopPropagation(); if(searchInput.value) console.log('Playground Prompt sent: ' + searchInput.value + ' (Model: ' + (currentModelNameSpan ? currentModelNameSpan.textContent : 'N/A') + ')');});

        const savePromptBtnPlayground = document.getElementById('playgroundSavePromptBtn');
        if(savePromptBtnPlayground) {
            savePromptBtnPlayground.addEventListener('click', () => {
                const promptText = searchInput ? searchInput.value : '';
                const modelName = currentModelNameSpan ? currentModelNameSpan.textContent : 'N/A';
                if (promptText) {
                    alert(`Saving prompt from Playground:\nText: ${promptText}\nModel: ${modelName}`);
                    // Add actual save logic here (e.g., add to "My Prompts" data, API call)
                } else {
                    alert("Nothing to save!");
                }
            });
        }

        // --- INITIALIZATION (Playground Scoped) ---
        pg_autoResizeTextarea(searchInput);
        pg_setDefaultModel();
        if (searchInput && searchInput.value === '') {
            pg_renderFavoritePrompts(); // Show favorites if input is empty
        }
        console.log("Playground Script Initialized.");
    } // End of initializePlaygroundScript
</script>

</body>
</html>