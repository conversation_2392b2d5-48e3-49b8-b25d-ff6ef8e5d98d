<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Búsqueda Glassmorphism</title>
    <!-- Font Awesome para los iconos -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

        body {
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            background: linear-gradient(135deg, #e0eafc 0%, #cfdef3 100%); /* Fondo suave similar al ejemplo */
            font-family: 'Inter', sans-serif;
            overflow: hidden; /* Para que el ruido de fondo no cause scrollbars */
        }

        /* Simulación del ruido de fondo del ejemplo */
        body::before {
            content: "";
            position: fixed;
            top: -50%;
            left: -50%;
            width: 200%;
            height: 200%;
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 200 200' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.65' numOctaves='3' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
            opacity: 0.03; /* Muy sutil */
            z-index: -1;
            pointer-events: none;
        }

        .main-title {
            font-size: 28px;
            font-weight: 500;
            color: #333; /* Un color más oscuro para el título, como en la segunda imagen */
            margin-bottom: 30px;
            text-align: center;
        }

        .search-bar-container {
            position: relative;
            width: 90%;
            max-width: 800px; /* Ajusta según necesidad */
            padding: 12px 20px;
            background: rgba(255, 255, 255, 0.25); /* Transparencia clave */
            backdrop-filter: blur(15px); /* Desenfoque del fondo */
            -webkit-backdrop-filter: blur(15px); /* Para Safari */
            border-radius: 50px; /* Bordes muy redondeados */
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.15);
            display: flex;
            align-items: center;
            gap: 10px;
            transition: all 0.3s ease;
        }

        .search-bar-container:focus-within {
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.25), 0 0 0 2px rgba(100, 150, 255, 0.5);
        }

        .glow-line {
            position: absolute;
            height: 2px;
            background: linear-gradient(90deg, transparent, #8cb2ff, transparent);
            filter: blur(3px);
            border-radius: 2px;
            opacity: 0;
            transition: opacity 0.4s ease, width 0.4s ease, left 0.4s ease;
        }

        .search-bar-container:focus-within .glow-line,
        .search-bar-container.focused .glow-line { /* Para forzar con JS si es necesario */
            opacity: 0.9;
        }

        .top-glow {
            top: 4px;
            left: 50%;
            width: 0%;
            transform: translateX(-50%);
        }
        .search-bar-container:focus-within .top-glow,
        .search-bar-container.focused .top-glow {
            width: 30%;
            animation: pulseGlow 2.5s infinite alternate ease-in-out;
        }

        .bottom-glow {
            bottom: 4px;
            left: 50%;
            width: 0%;
            transform: translateX(-50%);
        }
        .search-bar-container:focus-within .bottom-glow,
        .search-bar-container.focused .bottom-glow {
            width: 30%;
            animation: pulseGlow 2.5s infinite alternate-reverse ease-in-out;
        }
        
        @keyframes pulseGlow {
            from { opacity: 0.6; transform: translateX(-50%) scaleX(0.8); }
            to { opacity: 1; transform: translateX(-50%) scaleX(1.1); }
        }


        .initial-prompt {
            display: flex;
            align-items: center;
            color: rgba(50, 50, 70, 0.7);
            font-size: 15px;
            flex-shrink: 0; /* Para que no se encoja demasiado */
            margin-right: 15px;
        }

        .initial-prompt .fa-search {
            margin-right: 10px;
            font-size: 16px;
            color: rgba(50, 50, 70, 0.6);
        }

        .action-buttons {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-wrap: nowrap; /* Evitar que se rompan en varias líneas fácilmente */
            overflow-x: auto; /* Permitir scroll horizontal si no caben */
            padding-bottom: 5px; /* Espacio para la barra de scroll si aparece */
            scrollbar-width: thin;
            scrollbar-color: rgba(0,0,0,0.2) transparent;
        }
        .action-buttons::-webkit-scrollbar {
            height: 4px;
        }
        .action-buttons::-webkit-scrollbar-thumb {
            background-color: rgba(0,0,0,0.2);
            border-radius: 10px;
        }


        .action-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 1px solid rgba(255, 255, 255, 0.3);
            color: rgba(30, 30, 50, 0.8);
            padding: 7px 12px;
            border-radius: 20px;
            cursor: pointer;
            font-size: 13px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 6px;
            transition: all 0.25s ease;
            white-space: nowrap; /* Para que el texto no se rompa */
        }

        .action-btn:hover {
            background: rgba(255, 255, 255, 0.4);
            border-color: rgba(255, 255, 255, 0.5);
            color: rgba(0, 0, 0, 0.9);
            transform: translateY(-1px);
            box-shadow: 0 2px 8px rgba(0,0,0,0.05);
        }

        .action-btn .fas, .action-btn .far { /* FontAwesome 5 solid/regular */
            font-size: 14px;
        }
        
        .action-btn.icon-only {
            padding: 7px 9px; /* Un poco más ajustado para botones solo con icono */
        }
        .action-btn.icon-only .fas, .action-btn.icon-only .far {
            font-size: 15px;
        }

        .mic-buttons {
            margin-left: auto; /* Empuja los micrófonos a la derecha */
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0; /* Evita que se encojan */
        }

        .mic-btn {
            background: rgba(255, 255, 255, 0.15);
            border: 1px solid rgba(255, 255, 255, 0.25);
            color: rgba(30, 30, 50, 0.7);
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.25s ease;
            font-size: 16px;
        }

        .mic-btn:hover {
            background: rgba(255, 255, 255, 0.35);
            color: rgba(0, 0, 0, 0.9);
            transform: scale(1.05);
        }

        .mic-btn.active-mic-style { /* Estilo para el micrófono "activo" */
            background: rgba(100, 150, 255, 0.2); /* Un color para indicar actividad */
            color: #3366cc;
            border-color: rgba(100, 150, 255, 0.4);
        }
        .hidden {
            display: none !important;
        }

        /* Placeholder para un input real si se quisiera usar */
        .search-bar-container input[type="text"] {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            color: rgba(30, 30, 50, 0.9);
            font-size: 15px;
            font-family: 'Inter', sans-serif;
        }
        .search-bar-container input[type="text"]::placeholder {
            color: rgba(50, 50, 70, 0.7);
        }

    </style>
</head>
<body>

    <div class="main-title">¿En qué puedo ayudarte?</div>

    <div class="search-bar-container" id="searchBar">
        <div class="glow-line top-glow"></div>

        <!-- En lugar de un input, usamos un span para el prompt inicial, como en la segunda imagen -->
        <div class="initial-prompt">
            <i class="fas fa-search"></i>
            <span>Pregunta lo que quieras</span>
        </div>
        
        <!-- Si quisieras un input real, descomenta esto y comenta el div .initial-prompt -->
        <!-- <i class="fas fa-search" style="color: rgba(50, 50, 70, 0.6); margin-right: 8px;"></i> -->
        <!-- <input type="text" placeholder="Pregunta lo que quieras"> -->


        <div class="action-buttons">
            <button class="action-btn icon-only" title="Añadir"><i class="fas fa-plus"></i></button>
            <button class="action-btn" data-action="buscar"><i class="fas fa-search"></i><span>Buscar</span></button>
            <button class="action-btn" data-action="razona"><i class="fas fa-lightbulb"></i><span>Razona</span></button>
            <button class="action-btn" data-action="investigacion"><i class="fas fa-book-open"></i><span>Investigación en profundidad</span></button>
            <button class="action-btn" data-action="imagen"><i class="fas fa-image"></i><span>Crea una imagen</span></button>
            <button class="action-btn icon-only" title="Más opciones"><i class="fas fa-ellipsis-h"></i></button>
        </div>

        <div class="mic-buttons">
            <button class="mic-btn" id="micOutlineBtn" title="Activar micrófono"><i class="fas fa-microphone"></i></button>
            <button class="mic-btn active-mic-style hidden" id="micFilledBtn" title="Desactivar micrófono"><i class="fas fa-microphone-alt"></i></button>
        </div>
        <div class="glow-line bottom-glow"></div>
    </div>

    <script>
        const searchBar = document.getElementById('searchBar');
        const micOutlineBtn = document.getElementById('micOutlineBtn');
        const micFilledBtn = document.getElementById('micFilledBtn');
        const actionButtons = document.querySelectorAll('.action-buttons .action-btn');

        // Simular foco para las luces (útil si no hay un input real)
        // Puedes quitar esto si usas un input real y :focus-within es suficiente
        searchBar.addEventListener('click', (event) => {
            // Solo añade 'focused' si el click no es en un botón interactivo dentro de la barra
            if (!event.target.closest('button')) {
                 searchBar.classList.add('focused');
            }
        });
        // Quitar el foco simulado si se hace clic fuera
        document.addEventListener('click', (event) => {
            if (!searchBar.contains(event.target)) {
                searchBar.classList.remove('focused');
            }
        });


        // Interacción de los botones de micrófono
        micOutlineBtn.addEventListener('click', () => {
            micOutlineBtn.classList.add('hidden');
            micFilledBtn.classList.remove('hidden');
            console.log("Micrófono activado (simulado)");
            searchBar.classList.add('focused'); // Mantener el brillo
        });

        micFilledBtn.addEventListener('click', () => {
            micFilledBtn.classList.add('hidden');
            micOutlineBtn.classList.remove('hidden');
            console.log("Micrófono desactivado (simulado)");
            searchBar.classList.remove('focused'); // Quitar el brillo si no hay otra razón
        });

        // Consola para los botones de acción
        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const action = button.dataset.action;
                const title = button.title;
                if (action) {
                    console.log(`Acción: ${action}`);
                } else if (title) {
                    console.log(`Acción: ${title}`);
                }
                 searchBar.classList.add('focused'); // Mantener el brillo al hacer clic en un botón
            });
        });

    </script>

</body>
</html>