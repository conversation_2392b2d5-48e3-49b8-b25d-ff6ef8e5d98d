<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Búsqueda Unificada Glassmorphism</title>
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;500;600&display=swap');

        :root {
                        --bg-gradient-start: #ffffff; 
            --bg-gradient-end: #c2c2eb;   
            --glass-bg: rgba(255, 255, 255, 0.22); /* Un poco menos opaco para el contraste */
            --glass-border: rgba(255, 255, 255, 0.35);
            --blur-intensity: 12px;
            --border-radius-main: 28px;
            --border-radius-buttons: 20px;
            --text-color: rgba(40, 40, 60, 0.9); /* Un poco más oscuro para contraste */
            --icon-color: rgba(70, 70, 90, 0.75);
            --glow-color: #8cb2ff;
            --focus-shadow: 0 0 0 2px rgba(100, 150, 255, 0.4);
            --hover-bg-button: rgba(255, 255, 255, 0.45);
        }

        body {
            margin: 0;
            padding: 20px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            /* Degradado de fondo más contrastado */
            background: linear-gradient(135deg, #eaedf3 35%, #d1eefc 70%, #a2b3c4 100%);
            font-family: 'Inter', sans-serif;
            overflow-x: hidden;
        }

        body::before {
            content: "";
            position: fixed;
            top: -50%; left: -50%;
            width: 200%; height: 200%;
            background-image: url("data:image/svg+xml,%3Csvg viewBox='0 0 250 250' xmlns='http://www.w3.org/2000/svg'%3E%3Cfilter id='noiseFilter'%3E%3CfeTurbulence type='fractalNoise' baseFrequency='0.85' numOctaves='2' stitchTiles='stitch'/%3E%3C/filter%3E%3Crect width='100%25' height='100%25' filter='url(%23noiseFilter)'/%3E%3C/svg%3E");
            opacity: 0.020;
            z-index: -1;
            pointer-events: none;
        }

        .main-title {
            font-size: 50px;
            font-weight: 500;
            color: #303848; /* Color ajustado para el nuevo fondo */
            margin-bottom: 50px;
            text-align: center;
        }

        .glass-container {
            position: relative;
            width: 90%;
            max-width: 700px;
            background: var(--glass-bg);
            backdrop-filter: blur(var(--blur-intensity));
            -webkit-backdrop-filter: blur(var(--blur-intensity));
            border-radius: var(--border-radius-main);
            border: 1px solid var(--glass-border);
            box-shadow: 0 7px 28px rgba(0, 0, 0, 0.1); /* Sombra ajustada */
            transition: box-shadow 0.3s ease;
            padding: 18px 24px; /* Padding general para el contenedor unificado */
        }

        .input-area {
            display: flex;
            align-items: center;
            gap: 12px;
            margin-bottom: 18px; /* Espacio antes de los botones de acción */
        }
        
        .input-area .fa-search {
            font-size: 18px;
            color: var(--icon-color);
            flex-shrink: 0;
        }

        .input-area input[type="text"] {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--text-color);
            font-size: 16px;
            font-weight: 400;
            font-family: 'Inter', sans-serif;
        }
        .input-area input[type="text"]::placeholder {
            color: var(--icon-color);
            font-weight: 400;
        }

        .mic-buttons-wrapper {
            display: flex;
            align-items: center;
            gap: 8px;
            flex-shrink: 0;
        }

        .mic-btn {
            background: transparent;
            border: 1px solid rgba(0,0,0,0.12); /* Borde sutil */
            color: var(--icon-color);
            width: 34px;
            height: 34px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
            transition: all 0.2s ease;
            font-size: 15px;
        }

        .mic-btn:hover {
            background: rgba(0, 0, 0, 0.06);
            color: var(--text-color);
        }
        .mic-btn.active-mic-style {
             background: rgba(0, 0, 0, 0.09);
             color: var(--text-color);
        }

        .action-buttons-grid {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            justify-content: flex-start;
            padding-top: 10px; /* Pequeño padding superior para el grupo de botones */
            border-top: 1px solid rgba(255, 255, 255, 0.15); /* Línea divisora sutil */
            margin-top: 15px; /* Espacio adicional arriba de la línea */
        }

        .action-btn {
            background: rgba(255, 255, 255, 0.18); /* Ligeramente más opaco que el fondo principal */
            border: 1px solid rgba(255, 255, 255, 0.28);
            color: var(--text-color);
            padding: 8px 14px;
            border-radius: var(--border-radius-buttons);
            cursor: pointer;
            font-size: 13.5px;
            font-weight: 500;
            display: flex;
            align-items: center;
            gap: 7px;
            transition: all 0.2s ease;
            white-space: nowrap;
        }
        .action-btn:hover {
            background: var(--hover-bg-button);
            border-color: rgba(255, 255, 255, 0.45);
            color: rgba(0,0,0,0.85);
            transform: translateY(-1px);
            box-shadow: 0 2px 6px rgba(0,0,0,0.05);
        }
        .action-btn .fas { font-size: 14px; }
        .action-btn.icon-only { padding: 8px 10px; }
        .action-btn.icon-only .fas { font-size: 15px; }

        .glow-line {
            position: absolute;
            height: 2.5px;
            background: var(--glow-color);
            filter: blur(4px);
            border-radius: 3px;
            opacity: 0;
            transition: opacity 0.4s ease, width 0.5s cubic-bezier(0.25, 1, 0.5, 1), left 0.5s cubic-bezier(0.25, 1, 0.5, 1);
            pointer-events: none;
        }

        .glass-container.focused .glow-line {
            opacity: 0.75; /* Opacidad del brillo ligeramente aumentada */
        }

        .top-glow {
            top: 2px;
            left: 50%;
            width: 0%;
            transform: translateX(-50%);
        }
        .glass-container.focused .top-glow {
            width: 35%;
            animation: glow-pulse 2.8s infinite alternate ease-in-out;
        }

        .bottom-glow {
            bottom: 2px;
            left: 50%;
            width: 0%;
            transform: translateX(-50%);
        }
        .glass-container.focused .bottom-glow {
            width: 35%;
            animation: glow-pulse 2.8s infinite alternate-reverse ease-in-out;
        }
        
        @keyframes glow-pulse {
            0% { opacity: 0.55; transform: translateX(-50%) scaleX(0.9); }
            100% { opacity: 0.85; transform: translateX(-50%) scaleX(1.1); }
        }
        
        .glass-container.focused {
            box-shadow: 0 8px 28px rgba(0, 0, 0, 0.12), var(--focus-shadow);
        }

        .hidden { display: none !important; }

        @media (max-width: 600px) {
            .main-title { font-size: 22px; margin-bottom: 20px;}
            .glass-container { padding: 16px 20px; }
            .input-area input[type="text"] { font-size: 15px; }
            .input-area { margin-bottom: 15px; }
            .action-buttons-grid { justify-content: center; gap: 8px; margin-top: 12px; padding-top: 8px; }
            .action-btn { font-size: 12.5px; padding: 7px 10px; gap: 5px; }
            .action-btn .fas { font-size: 13px; }
        }

    </style>
</head>
<body>

    <div class="main-title">Prompt Like a Pro</div>

    <div class="glass-container" id="mainContainer">
        <div class="glow-line top-glow"></div>

        <div class="input-area">
            <i class="fas fa-search"></i>
            <input type="text" id="mainInput" placeholder="Pregunta lo que quieras">
            <div class="mic-buttons-wrapper">
                <button class="mic-btn" id="micOutlineBtn" title="Activar micrófono"><i class="fas fa-microphone"></i></button>
                <button class="mic-btn active-mic-style hidden" id="micFilledBtn" title="Desactivar micrófono"><i class="fas fa-microphone-alt"></i></button>
            </div>
        </div>
        
        <div class="action-buttons-grid">
            <button class="action-btn icon-only" title="Añadir"><i class="fas fa-plus"></i></button>
            <button class="action-btn" data-action="buscar"><i class="fas fa-search"></i><span>Buscar</span></button>
            <button class="action-btn" data-action="razona"><i class="fas fa-lightbulb"></i><span>Razona</span></button>
            <button class="action-btn" data-action="investigacion"><i class="fas fa-book-open"></i><span>Investigación en profundidad</span></button>
            <button class="action-btn" data-action="imagen"><i class="fas fa-image"></i><span>Crea una imagen</span></button>
            <button class="action-btn icon-only" title="Más opciones"><i class="fas fa-ellipsis-h"></i></button>
        </div>

        <div class="glow-line bottom-glow"></div>
    </div>

    <script>
        const mainContainer = document.getElementById('mainContainer');
        const mainInput = document.getElementById('mainInput');
        const micOutlineBtn = document.getElementById('micOutlineBtn');
        const micFilledBtn = document.getElementById('micFilledBtn');
        const actionButtons = document.querySelectorAll('.action-buttons-grid .action-btn');

        mainInput.addEventListener('focus', () => {
            mainContainer.classList.add('focused');
        });
        mainInput.addEventListener('blur', (event) => {
            // Pequeño retraso para permitir que los clics en los botones de micrófono se procesen
            // antes de potencialmente eliminar la clase 'focused'.
            setTimeout(() => {
                const activeElement = document.activeElement;
                const isMicButtonClicked = activeElement === micOutlineBtn || activeElement === micFilledBtn;
                const isChildOfMainContainer = mainContainer.contains(activeElement);

                if (!mainInput.matches(':focus') && !isMicButtonClicked && !isChildOfMainContainer) {
                    mainContainer.classList.remove('focused');
                } else if (!mainInput.matches(':focus') && !isMicButtonClicked && activeElement !== mainInput) {
                    // Si el foco está en otro elemento que no sea el input o los mics, y no es hijo, quitar foco
                    // Este caso es más para clics fuera del contenedor
                     if (!mainContainer.contains(document.activeElement)) {
                        mainContainer.classList.remove('focused');
                    }
                }
            }, 100); // 100ms delay
        });
        
        document.addEventListener('click', (event) => {
            if (!mainContainer.contains(event.target) && !mainInput.matches(':focus')) {
                 // Solo remueve 'focused' si el clic es realmente fuera y el input no tiene el foco
                 // y tampoco es un botón de micrófono
                if(event.target !== micOutlineBtn && event.target !== micFilledBtn) {
                    mainContainer.classList.remove('focused');
                }
            }
        });

        micOutlineBtn.addEventListener('click', () => {
            micOutlineBtn.classList.add('hidden');
            micFilledBtn.classList.remove('hidden');
            console.log("Micrófono activado (simulado)");
            mainContainer.classList.add('focused');
            mainInput.focus();
        });

        micFilledBtn.addEventListener('click', () => {
            micFilledBtn.classList.add('hidden');
            micOutlineBtn.classList.remove('hidden');
            console.log("Micrófono desactivado (simulado)");
            if(!mainInput.matches(':focus')){ // Solo quita focused si el input no lo tiene
                // mainContainer.classList.remove('focused'); // Decidimos mantenerlo si el input aún podría tenerlo
            }
            mainInput.focus();
        });

        actionButtons.forEach(button => {
            button.addEventListener('click', () => {
                const action = button.dataset.action;
                const title = button.title;
                if (action) {
                    console.log(`Acción: ${action}`);
                } else if (title) {
                    console.log(`Acción: ${title}`);
                }
                mainInput.focus(); // Devolver foco al input principal
                mainContainer.classList.add('focused'); // Asegurar que el brillo se mantiene
            });
        });
    </script>

</body>
</html>