<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;700&family=Russo+One&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --color-primary-neon: #00bfff; /* Azul celeste neon */
            --color-gradient-start: #ff3b3b; /* Rojo sutil */
            --color-gradient-end: #3b86ff;   /* Azul sutil */
            --color-text-on-dark: #e0e0e5;
            --color-placeholder: rgba(200, 200, 210, 0.7); /* Ligeramente más visible */
            --color-suggestion-icon: #bdc3c7;
            --bg-gradient-page-start: #e0e5ec; /* Gris claro */
            --bg-gradient-page-end: #cad2d9;   /* Gris azulado más oscuro */

            /* Glassmorphism Search Bar */
            --glass-outer-bg: rgba(255, 255, 255, 0.15);
            --glass-outer-border: rgba(255, 255, 255, 0.25);
            --glass-outer-blur: 12px;
            --glass-inner-bg: rgba(240, 243, 248, 0.85); /* Más opaco, color base del input */
            --glass-inner-text: #333842; /* Texto oscuro dentro del input */
            --glass-glow-top: rgba(100, 180, 255, 0.5);
            --glass-glow-bottom: rgba(150, 200, 255, 0.5);

            --font-header: 'Russo One', sans-serif;
            --font-body: 'Exo 2', sans-serif;
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background: linear-gradient(135deg, var(--bg-gradient-page-start), var(--bg-gradient-page-end));
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            /* justify-content: center; /* Esto centrará todo el contenido */
            padding: 20px;
            box-sizing: border-box;
        }

        .main-header {
            font-family: var(--font-header);
            font-size: 4rem; /* Tamaño grande */
            text-align: center;
            margin-top: 3vh;
            margin-bottom: 30px; /* Espacio antes de la barra de búsqueda */
            font-weight: 400;
            letter-spacing: 1px;
            background: linear-gradient(to right, var(--color-gradient-start), var(--color-gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            animation: fadeInDown 1s ease-out;
        }

        .search-interaction-wrapper { /* Nuevo wrapper para centrar search y suggestions */
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 850px; /* Ancho máximo para la barra y sugerencias */
            margin-top: 10vh; /* Empuja la barra de búsqueda hacia el centro */
        }
        
        /* --- Glassmorphism Search Bar Start --- */
        .search-box-container {
            position: relative;
            width: 100%;
            padding: 7px; /* Espacio para el borde difuminado exterior */
            background-color: var(--glass-outer-bg);
            backdrop-filter: blur(var(--glass-outer-blur));
            -webkit-backdrop-filter: blur(var(--glass-outer-blur));
            border: 1px solid var(--glass-outer-border);
            border-radius: 35px; /* Bordes muy redondeados para el contenedor exterior */
            box-shadow: 0 8px 32px 0 rgba(31, 38, 135, 0.1);
            margin-bottom: 20px; /* Espacio antes de las sugerencias */
            animation: fadeInUp 1s ease-out 0.3s;
            animation-fill-mode: backwards;
        }
        /* Sutiles brillos superior e inferior */
        .search-box-container::before,
        .search-box-container::after {
            content: '';
            position: absolute;
            left: 10%;
            right: 10%;
            height: 1.5px;
            border-radius: 2px;
            filter: blur(1.5px);
            opacity: 0.9;
        }
        .search-box-container::before {
            top: 6px;
            background: linear-gradient(to right, transparent, var(--glass-glow-top), transparent);
        }
        .search-box-container::after {
            bottom: 6px;
            background: linear-gradient(to right, transparent, var(--glass-glow-bottom), transparent);
        }

        .search-box-inner {
            display: flex;
            align-items: center;
            background-color: var(--glass-inner-bg);
            border-radius: 28px; /* Bordes redondeados para el input interior */
            padding: 12px 20px; /* Padding interno generoso para un look "grande" */
            height: 60px; /* Altura considerable */
            box-sizing: border-box;
        }
        /* --- Glassmorphism Search Bar End --- */

        .attach-file-btn {
            background: none;
            border: none;
            color: var(--glass-inner-text); /* Color del texto dentro del input */
            font-family: var(--font-body);
            font-size: 0.9rem;
            cursor: pointer;
            padding: 8px 10px;
            margin-right: 10px;
            white-space: nowrap;
            position: relative;
            transition: color 0.2s ease;
            opacity: 0.8;
        }
        .attach-file-btn:hover {
            color: var(--color-primary-neon);
            opacity: 1;
        }
        .attach-file-btn .tooltip {
            /* Estilos de tooltip (del código anterior) */
            visibility: hidden; width: max-content; background-color: #333; color: #fff; text-align: center;
            border-radius: 6px; padding: 5px 10px; position: absolute; z-index: 1; bottom: 125%;
            left: 50%; transform: translateX(-50%); opacity: 0; transition: opacity 0.3s, visibility 0.3s;
            font-size: 0.8rem;
        }
        .attach-file-btn .tooltip::after {
            content: ""; position: absolute; top: 100%; left: 50%; margin-left: -5px;
            border-width: 5px; border-style: solid; border-color: #333 transparent transparent transparent;
        }
        .attach-file-btn:hover .tooltip { visibility: visible; opacity: 1; }

        .search-input {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--glass-inner-text); /* Texto oscuro */
            font-size: 1.2rem; /* Tamaño de fuente generoso */
            font-family: var(--font-body);
            padding: 5px;
        }
        .search-input::placeholder {
            color: var(--color-placeholder);
            opacity: 1;
        }

        .search-box-icons {
            display: flex;
            align-items: center;
            gap: 12px; /* Espacio entre iconos */
            margin-left: 15px;
        }
        .search-box-icons .icon-btn {
            background: none;
            border: none;
            color: var(--glass-inner-text); /* Iconos oscuros */
            font-size: 1.25rem; /* Iconos un poco más grandes */
            cursor: pointer;
            padding: 5px;
            transition: color 0.2s ease, transform 0.2s ease;
            opacity: 0.7;
        }
        .search-box-icons .icon-btn:hover {
            color: var(--color-primary-neon);
            transform: scale(1.1);
            opacity: 1;
        }
        .send-btn {
            background-color: var(--color-primary-neon);
            color: #fff; /* Texto blanco para contraste */
            border: none;
            border-radius: 50%; /* Círculo */
            width: 42px; /* Ligeramente más grande */
            height: 42px; /* Ligeramente más grande */
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.4rem;
            cursor: pointer;
            box-shadow: 0 0 8px var(--color-primary-neon), 0 0 12px rgba(0, 191, 255, 0.7);
            animation: pulse 2s infinite;
            transition: background-color 0.3s ease, transform 0.2s ease;
        }
        .send-btn:hover {
            background-color: #00a0dd;
            transform: scale(1.05);
        }

        /* --- Sugerencias --- */
        .suggestions-list {
            width: 100%; /* Ocupará el ancho del search-interaction-wrapper */
            margin-top: 10px;
            background-color: rgba(40, 45, 60, 0.85); /* Fondo oscuro semitransparente para las sugerencias */
            backdrop-filter: blur(5px);
            -webkit-backdrop-filter: blur(5px);
            border-radius: 15px;
            padding: 10px 0;
            box-shadow: 0 4px 15px rgba(0,0,0,0.2);
            max-height: 0; /* Inicialmente oculto */
            opacity: 0;
            overflow: hidden;
            transition: max-height 0.3s ease-out, opacity 0.3s ease-out, padding 0.3s ease-out;
        }
        .suggestions-list.visible {
            max-height: 300px; /* Altura máxima para las sugerencias */
            opacity: 1;
            padding: 10px; /* Padding cuando visible */
            overflow-y: auto;
        }
        .suggestion-item {
            display: flex;
            align-items: center;
            padding: 12px 20px; /* Más padding para items */
            color: var(--color-text-on-dark);
            font-size: 1rem;
            cursor: pointer;
            border-radius: 10px;
            transition: background-color 0.2s ease;
            margin: 0 5px 5px 5px; /* Pequeño margen interno */
        }
        .suggestion-item:last-child {
            margin-bottom: 0;
        }
        .suggestion-item:hover {
            background-color: rgba(255, 255, 255, 0.1);
        }
        .suggestion-item .fa-magnifying-glass {
            margin-right: 15px;
            font-size: 0.9rem;
            color: var(--color-suggestion-icon);
        }

        /* Animaciones */
        @keyframes pulse {
            0% { box-shadow: 0 0 6px var(--color-primary-neon), 0 0 10px rgba(0, 191, 255, 0.6); }
            50% { box-shadow: 0 0 10px var(--color-primary-neon), 0 0 18px rgba(0, 191, 255, 0.8); }
            100% { box-shadow: 0 0 6px var(--color-primary-neon), 0 0 10px rgba(0, 191, 255, 0.6); }
        }
        @keyframes fadeInDown { /* (del código anterior) */
            from { opacity: 0; transform: translateY(-20px); } to { opacity: 1; transform: translateY(0); }
        }
        @keyframes fadeInUp { /* (del código anterior) */
            from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); }
        }

        /* Responsive */
        @media (max-width: 768px) {
            .main-header { font-size: 3rem; margin-top: 2vh; margin-bottom: 20px;}
            .search-interaction-wrapper { margin-top: 5vh; max-width: 95%;}
            .search-box-inner { height: 55px; padding: 10px 15px;}
            .search-input { font-size: 1rem; }
            .attach-file-btn { font-size: 0.8rem; margin-right: 5px; }
            .search-box-icons { gap: 8px; }
            .search-box-icons .icon-btn { font-size: 1.1rem; }
            .send-btn { width: 38px; height: 38px; font-size: 1.2rem; }
            .suggestion-item { padding: 10px 15px; font-size: 0.9rem; }
        }
        @media (max-width: 480px) {
            .main-header { font-size: 2.2rem; }
            .search-interaction-wrapper { margin-top: 3vh; }
            .search-box-inner { height: 50px; padding: 8px 12px; border-radius: 22px;}
            .search-box-container { border-radius: 28px; }
            .attach-file-btn span:not(.tooltip) { display: none; }
            .attach-file-btn { padding: 8px 5px; }
             .search-box-icons .icon-btn { font-size: 1rem; }
             .send-btn { width: 35px; height: 35px; font-size: 1.1rem; }
        }
    </style>
</head>
<body>

    <h1 class="main-header">prompt like a pro</h1>

    <div class="search-interaction-wrapper">
        <div class="search-box-container">
            <div class="search-box-inner">
                <button class="attach-file-btn">
                    Adjuntar archivo
                    <span class="tooltip">Inicia sesión para adjuntar archivos</span>
                </button>
                <input type="text" class="search-input" placeholder="Escribe tu prompt" id="promptInput">
                <div class="search-box-icons">
                    <button class="icon-btn" title="Configuración"><i class="fas fa-cog"></i></button>
                    <button class="icon-btn" title="Idioma/Región"><i class="fas fa-globe"></i></button>
                    <button class="icon-btn" title="Entrada por voz"><i class="fas fa-microphone"></i></button>
                    <button class="send-btn" title="Enviar"><i class="fas fa-arrow-right"></i></button>
                </div>
            </div>
        </div>

        <div class="suggestions-list" id="suggestionsList">
            {/* Las sugerencias se llenarán con JS */}
        </div>
    </div>

    <script>
        const promptInput = document.getElementById('promptInput');
        const suggestionsList = document.getElementById('suggestionsList');

        const allSuggestions = [
            { text: "redacta un correo", term: "redacta" },
            { text: "redacta un artículo", term: "redacta" },
            { text: "redacta una noticia", term: "redacta" },
            { text: "redacta un párrafo que describe la etapa actual de mi vida y me dibujo", term: "redacta" },
            { text: "redacta un plan de marketing", term: "redacta"},
            { text: "escribe un poema sobre la naturaleza", term: "escribe"},
            { text: "genera ideas para nombres de startups", term: "genera"},
            { text: "traduce 'hola mundo' al francés", term: "traduce"},
        ];

        promptInput.addEventListener('input', function() {
            const inputText = this.value.toLowerCase().trim();
            suggestionsList.innerHTML = ''; // Limpiar sugerencias previas

            if (inputText.length > 0) {
                // Filtrar sugerencias que comiencen con la primera palabra del input
                // o específicamente con "redacta" si el input es "redacta"
                const firstWord = inputText.split(' ')[0];
                const filteredSuggestions = allSuggestions.filter(suggestion => {
                    if (inputText === "redacta" && suggestion.term === "redacta") {
                        return true;
                    }
                    return suggestion.text.toLowerCase().startsWith(inputText) || 
                           (suggestion.term === firstWord && suggestion.text.toLowerCase().includes(firstWord));
                }).slice(0, 5); // Limitar a 5 sugerencias

                if (filteredSuggestions.length > 0) {
                    filteredSuggestions.forEach(sugg => {
                        const item = document.createElement('div');
                        item.classList.add('suggestion-item');
                        item.innerHTML = `<i class="fas fa-magnifying-glass"></i><span>${sugg.text}</span>`;
                        item.addEventListener('click', () => {
                            promptInput.value = sugg.text;
                            suggestionsList.classList.remove('visible');
                            promptInput.focus();
                        });
                        suggestionsList.appendChild(item);
                    });
                    suggestionsList.classList.add('visible');
                } else {
                    suggestionsList.classList.remove('visible');
                }
            } else {
                suggestionsList.classList.remove('visible');
            }
        });

        // Ocultar sugerencias si se hace clic fuera
        document.addEventListener('click', function(event) {
            if (!promptInput.contains(event.target) && !suggestionsList.contains(event.target)) {
                suggestionsList.classList.remove('visible');
            }
        });

        promptInput.addEventListener('focus', function() {
            if (this.value.length > 0 && suggestionsList.children.length > 0) {
                 suggestionsList.classList.add('visible');
            }
        });

    </script>

</body>
</html>