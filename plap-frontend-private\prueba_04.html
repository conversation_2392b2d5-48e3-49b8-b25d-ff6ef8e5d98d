<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Exo+2:wght@300;400;500;700&family=Russo+One&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --color-primary-neon: #00bfff; /* Azul celeste neon */
            
            /* Encabezado */
            --header-gradient-start: #4a4e52; /* Gris oscuro */
            --header-gradient-end: #2c3e50;   /* Azul oscuro */

            /* Caja de búsqueda */
            --search-box-bg: rgba(50, 60, 80, 0.25); /* Un poco más oscuro para contraste con fondo claro */
            --search-box-border: rgba(0, 191, 255, 0.3);
            --search-box-neon-glow: 0 0 7px rgba(0, 191, 255, 0.6), 0 0 14px rgba(0, 191, 255, 0.4);
            --search-box-text-color: #e0e0e5; /* Texto claro dentro de la caja de búsqueda */
            --placeholder-color: rgba(200, 205, 215, 0.6);

            /* Sugerencias */
            --suggestions-glass-bg: rgba(225, 230, 240, 0.3); /* Muy transparente, base clara */
            --suggestions-glass-blur: 10px;
            --suggestions-border: rgba(255, 255, 255, 0.18);
            --suggestions-text-color: #3d4752; /* Gris oscuro para el texto de sugerencias */
            --suggestions-icon-color: #606c76;
            --suggestions-hover-bg: rgba(255, 255, 255, 0.2);


            /* Fondo General */
            --bg-gradient-page-start: #e8edf0; /* Gris claro */
            --bg-gradient-page-end: #c8d0d8;   /* Gris azulado más oscuro */

            --font-header: 'Russo One', sans-serif;
            --font-body: 'Exo 2', sans-serif;
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background: linear-gradient(135deg, var(--bg-gradient-page-start), var(--bg-gradient-page-end));
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            color: #333; /* Color de texto por defecto para elementos fuera de contenedores especiales */
        }

        .main-header {
            font-family: var(--font-header);
            font-size: 4.5rem; 
            text-align: center;
            margin-top: 3vh;
            margin-bottom: 30px; 
            font-weight: 400;
            letter-spacing: 1px;
            /* Texto: Primera letra mayúscula, resto minúsculas - se maneja en HTML */
            background: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            text-fill-color: transparent;
            animation: fadeInDown 1s ease-out;
        }

        .search-interaction-wrapper {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 800px; /* Ancho de la caja de búsqueda y sugerencias */
            margin-top: 10vh; /* Para centrar verticalmente la caja de búsqueda */
            animation: fadeInUp 1s ease-out 0.3s;
            animation-fill-mode: backwards;
        }
        
        .search-box {
            display: flex;
            align-items: center;
            width: 100%;
            background-color: var(--search-box-bg);
            border: 1px solid var(--search-box-border);
            border-radius: 30px; 
            padding: 15px 20px; /* Padding generoso para altura */
            box-shadow: var(--search-box-neon-glow);
            margin-bottom: 20px;
            transition: box-shadow 0.3s ease;
            min-height: 50px; /* Altura mínima */
            box-sizing: border-box;
        }
        .search-box:focus-within {
            box-shadow: 0 0 10px rgba(0, 191, 255, 0.8), 0 0 20px rgba(0, 191, 255, 0.6);
        }
        
        .action-button-left { /* Contenedor para el botón de adjuntar */
            margin-right: 15px;
        }

        .icon-btn-input { /* Estilo común para iconos dentro del input */
            background: none;
            border: none;
            color: var(--search-box-text-color);
            font-size: 1.2rem;
            cursor: pointer;
            padding: 5px;
            transition: color 0.2s ease, transform 0.2s ease;
            opacity: 0.8;
            position: relative; /* Para el tooltip */
        }
        .icon-btn-input:hover {
            color: var(--color-primary-neon);
            opacity: 1;
            transform: scale(1.1);
        }
        .icon-btn-input .tooltip {
            visibility: hidden; width: max-content; background-color: #333; color: #fff; text-align: center;
            border-radius: 6px; padding: 5px 10px; position: absolute; z-index: 10; bottom: 125%;
            left: 50%; transform: translateX(-50%); opacity: 0; transition: opacity 0.3s, visibility 0.3s;
            font-size: 0.8rem; white-space: nowrap;
        }
        .icon-btn-input .tooltip::after {
            content: ""; position: absolute; top: 100%; left: 50%; margin-left: -5px;
            border-width: 5px; border-style: solid; border-color: #333 transparent transparent transparent;
        }
        .icon-btn-input:hover .tooltip { visibility: visible; opacity: 1; }

        .search-input {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--search-box-text-color);
            font-size: 1.15rem;
            font-family: var(--font-body);
            padding: 5px;
        }
        .search-input::placeholder {
            color: var(--placeholder-color);
            opacity: 1;
        }

        .search-box-icons-right {
            display: flex;
            align-items: center;
            gap: 15px; 
            margin-left: 15px;
        }
        
        .send-btn { /* Hereda de .icon-btn-input pero con override */
            background-color: var(--color-primary-neon);
            color: #fff !important; /* Texto blanco, !important para asegurar */
            border-radius: 50%;
            width: 40px;
            height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 1.3rem !important; /* Tamaño de flecha */
            box-shadow: 0 0 8px var(--color-primary-neon), 0 0 12px rgba(0, 191, 255, 0.7);
            animation: pulse 2s infinite;
            opacity: 1 !important; /* Siempre opaco */
        }
        .send-btn:hover {
            background-color: #00a0dd;
            transform: scale(1.05) !important; /* Asegurar hover */
        }

        /* --- Sugerencias "Cristalinas" --- */
        .suggestions-list {
            width: 100%;
            margin-top: 0; /* Estaba en 10px, ahora es 0 y el wrapper da espacio */
            background-color: var(--suggestions-glass-bg);
            backdrop-filter: blur(var(--suggestions-glass-blur));
            -webkit-backdrop-filter: blur(var(--suggestions-glass-blur));
            border: 1px solid var(--suggestions-border);
            border-radius: 20px; /* Bordes redondeados consistentes */
            padding: 0; /* Se manejará en .visible */
            box-shadow: 0 5px 25px rgba(0,0,0,0.1); /* Sombra sutil */
            max-height: 0; 
            opacity: 0;
            overflow: hidden;
            transition: max-height 0.35s ease-out, opacity 0.35s ease-out, padding 0.35s ease-out;
        }
        .suggestions-list.visible {
            max-height: 280px; 
            opacity: 1;
            padding: 15px; 
            overflow-y: auto;
        }
        .suggestion-item {
            display: flex;
            align-items: center;
            padding: 10px 15px; 
            color: var(--suggestions-text-color); /* Gris oscuro */
            font-size: 0.95rem;
            cursor: pointer;
            border-radius: 10px;
            transition: background-color 0.2s ease, color 0.2s ease;
            margin-bottom: 6px;
        }
        .suggestion-item:last-child { margin-bottom: 0; }
        .suggestion-item:hover {
            background-color: var(--suggestions-hover-bg);
            /* color: #f0f0f0; /* Opcional: texto más claro en hover */
        }
        .suggestion-item .fa-magnifying-glass {
            margin-right: 12px;
            font-size: 0.9rem;
            color: var(--suggestions-icon-color);
        }

        /* Animaciones (del código anterior) */
        @keyframes pulse {
            0% { box-shadow: 0 0 6px var(--color-primary-neon), 0 0 10px rgba(0, 191, 255, 0.6); }
            50% { box-shadow: 0 0 10px var(--color-primary-neon), 0 0 18px rgba(0, 191, 255, 0.8); }
            100% { box-shadow: 0 0 6px var(--color-primary-neon), 0 0 10px rgba(0, 191, 255, 0.6); }
        }
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-20px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

        /* Responsive */
        @media (max-width: 768px) {
            .main-header { font-size: 3rem; margin-top: 2vh; margin-bottom: 25px;}
            .search-interaction-wrapper { margin-top: 5vh; max-width: 95%;}
            .search-box { padding: 12px 15px; min-height: 45px; }
            .search-input { font-size: 1rem; }
            .action-button-left { margin-right: 10px; }
            .icon-btn-input { font-size: 1.1rem; }
            .search-box-icons-right { gap: 10px; }
            .send-btn { width: 38px; height: 38px; font-size: 1.2rem !important; }
            .suggestion-item { padding: 9px 12px; font-size: 0.9rem; }
            .suggestions-list.visible { padding: 10px; }
        }
        @media (max-width: 480px) {
            .main-header { font-size: 2.3rem; margin-bottom: 20px; }
            .search-interaction-wrapper { margin-top: 3vh; }
             .search-box { border-radius: 25px; }
            .action-button-left { margin-right: 8px; }
            .icon-btn-input { font-size: 1rem; padding: 3px; }
            .search-box-icons-right { gap: 8px; margin-left: 10px; }
             .send-btn { width: 35px; height: 35px; font-size: 1.1rem !important; }
        }
    </style>
</head>
<body>

    <h1 class="main-header">Prompt like a pro</h1>

    <div class="search-interaction-wrapper">
        <div class="search-box">
            <div class="action-button-left">
                <button class="icon-btn-input" title="Adjuntar archivo">
                    <i class="fas fa-paperclip"></i>
                    <span class="tooltip">Inicia sesión para adjuntar archivos</span>
                </button>
            </div>
            <input type="text" class="search-input" placeholder="Escribe tu prompt" id="promptInput">
            <div class="search-box-icons-right">
                <button class="icon-btn-input" title="Configuración"><i class="fas fa-cog"></i></button>
                <button class="icon-btn-input" title="Entrada por voz"><i class="fas fa-microphone"></i></button>
                <button class="icon-btn-input send-btn" title="Enviar"><i class="fas fa-arrow-right"></i></button>
            </div>
        </div>

        <div class="suggestions-list" id="suggestionsList">
            {/* Las sugerencias se llenarán con JS */}
        </div>
    </div>

    <script>
        const promptInput = document.getElementById('promptInput');
        const suggestionsList = document.getElementById('suggestionsList');

        // Lista actualizada de sugerencias
        const baseSuggestions = {
            "redacta": [
                "redacta un correo",
                "redacta un artículo",
                "redacta una noticia",
                "redacta un párrafo que describe la etapa actual de mi vida y me dibujo",
                "redacta un plan de marketing"
            ],
            "escribe": [
                "escribe un poema sobre el amanecer",
                "escribe un cuento corto de misterio",
                "escribe el guion para un video de YouTube"
            ],
            "genera": [
                "genera ideas para un logo",
                "genera nombres para una mascota",
                "genera una paleta de colores"
            ]
            // Puedes añadir más términos clave y sus sugerencias
        };

        promptInput.addEventListener('input', function() {
            const inputText = this.value.toLowerCase().trim();
            const firstWord = inputText.split(' ')[0];
            suggestionsList.innerHTML = ''; 

            if (inputText.length > 0) {
                let filteredSuggestions = [];

                if (baseSuggestions[firstWord]) {
                    // Si la primera palabra coincide con una clave, mostrar esas sugerencias
                    // y filtrarlas si el usuario sigue escribiendo
                    filteredSuggestions = baseSuggestions[firstWord]
                        .filter(sugg => sugg.toLowerCase().startsWith(inputText))
                        .slice(0, 5);
                } else {
                    // Búsqueda general si no hay coincidencia de palabra clave
                    // (Podrías tener una lista más grande para esto o buscar en todas)
                    // Por ahora, lo dejamos vacío si no hay coincidencia de palabra clave inicial.
                }


                if (filteredSuggestions.length > 0) {
                    filteredSuggestions.forEach(suggText => {
                        const item = document.createElement('div');
                        item.classList.add('suggestion-item');
                        item.innerHTML = `<i class="fas fa-magnifying-glass"></i><span>${suggText}</span>`;
                        item.addEventListener('click', () => {
                            promptInput.value = suggText;
                            suggestionsList.classList.remove('visible');
                            promptInput.focus();
                        });
                        suggestionsList.appendChild(item);
                    });
                    suggestionsList.classList.add('visible');
                } else {
                    suggestionsList.classList.remove('visible');
                }
            } else {
                suggestionsList.classList.remove('visible');
            }
        });

        document.addEventListener('click', function(event) {
            if (!promptInput.contains(event.target) && !suggestionsList.contains(event.target) &&
                !event.target.closest('.icon-btn-input')) { // No ocultar si se hace clic en un botón dentro de la barra
                suggestionsList.classList.remove('visible');
            }
        });

        promptInput.addEventListener('focus', function() {
            // Re-evaluar si mostrar sugerencias al enfocar, solo si ya hay texto y sugerencias
            if (this.value.length > 0 && suggestionsList.children.length > 0) {
                 suggestionsList.classList.add('visible');
            }
        });
    </script>

</body>
</html>