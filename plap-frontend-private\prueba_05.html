<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --header-gradient-start: #dad6d6;
            --header-gradient-end: #24035a;
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;
            --page-bg-lilac: #e9e4f0;
            --glass-outer-bg: rgba(255, 255, 255, 0.1);
            --glass-outer-border-color: rgba(255, 255, 255, 0.25);
            --glass-outer-blur: 12px;
            --glass-outer-border-color: rgba(138, 43, 226, 0.8); /* Neon violet border color */
            --glass-outer-shadow: 0 15px 40px rgba(0, 0, 0, 0.15); /* Increased shadow */
            --glass-inner-bg: rgba(248, 249, 252, 0.85);
            --glass-inner-shadow-focus: 0 0 0 2px rgba(100, 150, 255, 0.3);
            --searchbar-placeholder-text: #8a8a93;
            --searchbar-input-text: #454550;
            --searchbar-icon-gray: #90909d;
            --searchbar-magnifying-glass: #70707a;
            --searchbar-bottom-glow-color: rgba(120, 170, 255, 0.6);
            --popup-bg: #fdfdff;
            --popup-shadow: 0 6px 20px rgba(0,0,0,0.12);
            --popup-text: #484852;
            --popup-hover-bg: #f1f3f9;
            --tooltip-bg: #282c34;
            --tooltip-bg: #282c34; /* El fondo del tooltip original, lo mantenemos o ajustamos si es necesario */
            --tooltip-text: #858688;
            --suggestions-list-bg: rgba(235, 230, 245, 0.35);
            --suggestions-list-blur: 10px;
            --suggestions-list-border: rgba(255, 255, 255, 0.2);
            --suggestions-list-shadow: 0 6px 20px rgba(0, 0, 0, 0.1);
            --suggestions-item-text-color: #303540;
            --suggestions-item-hover-bg: rgba(255, 255, 255, 0.25);
            --suggestions-item-icon-color: #606570;
            --auth-btn-text-color: #f0f0f5;
            --auth-btn-pulse-color: rgba(51, 3, 128, 0.7);
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background: linear-gradient(to bottom right, #ECEBE4, #EEF0F2, #FAFAFF);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
        }

        .all-hub-logo {
            position: absolute;
            top: 25px;
            left: 30px; /* Positioned in the top left corner */
            font-family: var(--font-header);
            font-size: 1.5rem; /* Adjust size as needed */
            font-weight: 700; /* Match header weight or similar */
            color: #1C1C1C;
            z-index: 10;
        }

        .auth-buttons {
            position: absolute;
            top: 25px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }

        .auth-btn {
            padding: 6px 14px; /* Reduced padding to make buttons smaller */
            border-radius: 18px; /* Slightly reduced border radius */
font-size: 0.9rem; /* Slightly reduced font size */
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
        }

        .btn-signin {
            background-color: transparent;
            border: 0.25px solid #1C1C1C;
            color: #1C1C1C;
        }
        .btn-signin:hover {
            background-color: rgba(28, 28, 28, 0.1);
        }

        .btn-signup {
            background: linear-gradient(135deg, #746ab0 0%, #1e1e2d 100%); /* Strong gradient - keeping original for now */
            border: 2px solid #1C1C1C;
            color: var(--auth-btn-text-color);
            box-shadow: 0 8px 12px rgba(0, 0, 0, 0.2); /* Levitating effect */
            animation: pulse-signup 2s infinite;
        }
        .btn-signup:hover {
            background-color: var(--header-gradient-start); /* Keeping original for now */
            border-color: var(--header-gradient-start); /* Keeping original for now */
            animation-play-state: paused;
            transform: translateY(-2px);
            box-shadow: 0 4px 10px rgba(0,0,0,0.2);
        }


        @keyframes pulse-signup {
            0% { box-shadow: 0 0 0 0 var(--auth-btn-pulse-color); transform: scale(1); }
            70% { box-shadow: 0 0 10px 2px rgba(138, 43, 226, 0.8); transform: scale(1.05); } /* Neon-like glow with scale */
            100% { box-shadow: 0 0 0 0 rgba(138, 43, 226, 0); transform: scale(1); }
        }

        .header-container {
            text-align: center;
            margin-top: 20vh; /* Keep margin-top the same */
            margin-bottom: 30px; /* Reduced margin-bottom to decrease space below */
            position: relative;
            z-index: 5;
            width: 100%;
        }

        .main-header {
            font-family: var(--font-header);
            font-size: 3rem; /* Slightly increased size for main header */
            margin-bottom: 5px;
            font-weight: 900;
            letter-spacing: 1px;
            background: linear-gradient(to right, var(--header-gradient-start), var(--header-gradient-end));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            color: transparent;
            animation: fadeInDown 1s ease-out;
        }

        .copilot-subtitle {
            font-family: var(--font-body);
            font-size: 1rem;
            font-weight: 300;
            color: #5a5e63;
            margin-top: 10px;
            margin-bottom: 10px;
            animation: fadeInDown 1s ease-out 0.2s;
            animation-fill-mode: backwards;
        }

        .search-area-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 800px;
            margin-top: 10vh;
            margin-bottom: auto;
            position: relative;
            animation: fadeInUp 1s ease-out 0.3s;
            animation-fill-mode: backwards;
        }

        .search-bar-glass-outer {
            width: 100%;
            background-color: var(--glass-outer-bg);
            backdrop-filter: blur(var(--glass-outer-blur));
            -webkit-backdrop-filter: blur(var(--glass-outer-blur));
            border: 0.25px solid #1C1C1C;
            border-radius: 28px;
            padding: 8px;
            box-shadow: var(--glass-outer-shadow);
            position: relative;
            transition: transform 0.3s ease, box-shadow 0.3s ease;
        }
        .search-bar-glass-outer:hover {
            transform: scale(1.015);
            box-shadow: 0 12px 35px rgba(0, 0, 0, 0.09);
        }

        /* Removed the blueish glow effect from the bottom of the search bar */

        .search-bar-glass-inner {
            display: flex;
            align-items: center;
            background-color: var(--glass-inner-bg);
            border-radius: 20px;
            padding: 10px 15px 10px 10px;
            min-height: 48px;
            box-sizing: border-box;
            box-shadow: none;
            transition: box-shadow 0.3s ease;
        }
        .search-bar-glass-outer.focused-outer .search-bar-glass-inner {
            box-shadow: var(--glass-inner-shadow-focus);
        }

        .search-input {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--searchbar-input-text);
            margin-left: 15px; /* Shift input to the right */
font-size: 0.9rem; /* Made search input text even smaller */
            font-family: var(--font-body);
            font-weight: 500;
        }
        .search-input::placeholder {
            color: var(--searchbar-placeholder-text);
            opacity: 1;
            font-weight: 500;
        }

        .search-actions {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: 10px;
        }
        .action-icon {
            font-size: 1.05rem;
            color: var(--searchbar-icon-gray);
            cursor: pointer;
            transition: color 0.2s ease, transform 0.2s ease;
            padding: 4px;
            position: relative;
        }
        .action-icon:hover {
            color: #555;
            transform: scale(1.1);
        }
        .action-icon:hover .icon-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
        }

        .icon-tooltip {
            position: absolute;
            bottom: calc(100% + 25px);
            left: 50%;
            transform: translateX(-50%) translateY(4px);
            background-color: var(--glass-outer-bg);
            backdrop-filter: blur(var(--glass-outer-blur));
            -webkit-backdrop-filter: blur(var(--glass-outer-blur));
            border: 1px solid #1C1C1C;
            box-shadow: var(--glass-outer-shadow);
            color: var(--tooltip-text);
            font-size: 0.85rem;
            font-family: var(--font-body);
            padding: 5px 10px;
            border-radius: 6px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
            z-index: 100;
        }
        .icon-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: #1C1C1C transparent transparent transparent;
        }

        .tags-dropdown {
            position: absolute;
            top: calc(100% + 12px);
            left: 0;
            min-width: 120px;
            background-color: var(--suggestions-list-bg);
            backdrop-filter: blur(var(--suggestions-list-blur));
            -webkit-backdrop-filter: blur(var(--suggestions-list-blur));
            border: 1px solid #1C1C1C;
            border-radius: 10px;
            box-shadow: var(--suggestions-list-shadow);
            padding: 10px;
            opacity: 0;
            visibility: hidden;
            transform: translateY(8px);
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
            z-index: 100;
        }
        .tags-dropdown.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }
        .tags-dropdown ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .tags-dropdown li {
            padding: 7px 10px;
            font-size: 0.9rem;
            color: var(--suggestions-item-text-color);
            cursor: pointer;
            border-radius: 5px;
            transition: background-color 0.2s ease;
        }
        .tags-dropdown li:hover {
            background-color: var(--suggestions-item-hover-bg);
        }

        .prompt-suggestions-list {
            position: absolute;
            top: calc(100% + 8px);
            left: 0;
            right: 0;
            background-color: var(--suggestions-list-bg);
            backdrop-filter: blur(var(--suggestions-list-blur));
            -webkit-backdrop-filter: blur(var(--suggestions-list-blur));
            border: 1px solid #1C1C1C;
            border-radius: 18px;
            box-shadow: var(--suggestions-list-shadow);
            padding: 0;
            opacity: 0;
            visibility: hidden;
            transform: translateY(8px);
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease, padding 0.25s ease;
            z-index: 95;
            max-height: 280px;
            overflow-y: auto;
        }
        .prompt-suggestions-list.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
            padding: 10px;
        }
        .prompt-suggestion-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
font-size: 1.05rem;
            color: var(--suggestions-item-text-color);
            cursor: pointer;
            border-radius: 10px;
            transition: background-color 0.2s ease, color 0.2s ease;
            margin-bottom: 5px;
        }
        .prompt-suggestion-item:last-child {
            margin-bottom: 0;
        }
        .prompt-suggestion-item:hover {
            background-color: var(--suggestions-item-hover-bg);
        }
        .prompt-suggestion-item .fa-check-circle { /* Updated selector for the new icon */
            margin-right: 10px; /* Added space between icon and text */
            color: #a0a0a0; /* Set icon color to a lighter gray */
            font-size: 0.9rem;
        }

        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-20px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

        @media (max-width: 768px) {
            .auth-buttons {
                top: 15px;
                right: 15px;
                gap: 10px;
            }
            .auth-btn {
                padding: 6px 12px;
                font-size: 0.8rem;
            }
            .header-container { margin-top: 15vh;}
            .main-header { font-size: 2.5rem; }
            .copilot-subtitle { font-size: 0.9rem; margin-bottom: 20px; }
            .search-area-container { max-width: 90%; margin-top: 3vh; }
            .search-bar-glass-outer { border-radius: 24px; padding: 7px;}
            .search-bar-glass-inner { border-radius: 17px; padding: 8px 12px; min-height: 44px;}
            .search-input { font-size: 0.95rem; }
            .search-actions { gap: 7px; margin-left: 7px; }
            .action-icon { font-size: 1rem; }
            .tags-dropdown { left: 0; }
            .prompt-suggestions-list { border-radius: 16px;}
            .icon-tooltip { font-size: 0.8rem; padding: 4px 8px; }
        }
        @media (max-width: 480px) {
            .auth-buttons {
                position: static;
                width: 100%;
                justify-content: center;
                margin-top: 15px;
                margin-bottom: 20px;
            }
            .header-container { margin-top: 5vh;}
            .main-header { font-size: 2rem; }
            .copilot-subtitle { font-size: 0.85rem; }
            .search-area-container { margin-top: 0; }
            .search-bar-glass-outer { border-radius: 20px; padding: 6px;}
            .search-bar-glass-inner { border-radius: 14px; padding: 7px 10px; min-height: 40px;}
            .search-input { font-size: 0.9rem; }
            .search-actions { gap: 7px; margin-left: 7px; }
            .action-icon { font-size: 0.9rem; }
            .prompt-suggestions-list { border-radius: 14px;}
            .icon-tooltip { font-size: 0.75rem; padding: 3px 6px; }
        }
    </style>
</head>
<body>
    <div class="all-hub-logo">allhub</div>
    <div class="auth-buttons">
        <a href="#" class="auth-btn btn-signin">Sign In</a>
        <a href="#" class="auth-btn btn-signup">Sign Up Free</a>
    </div>

    <div class="header-container">
        <h1 class="main-header">Prompt like a pro</h1>
        <p class="copilot-subtitle">Craft the perfect prompts with intelligent suggestions</p>
    </div>

    <div class="search-area-container">
        <div class="search-bar-glass-outer" id="searchBarOuter">
            <div class="search-bar-glass-inner" id="searchBarInner">
                <i class="fas fa-brain action-icon" id="hashtagBtn">
                    <span class="icon-tooltip">Select Model</span>
                </i>
                <input type="text" class="search-input" id="searchInput" placeholder="Describe your prompt clearly for the best response..." autocomplete="on">
                <div class="search-actions">
                    <i class="fas fa-copy action-icon" id="copyBtn">
                        <span class="icon-tooltip">Copy</span>
                    </i>
                    <i class="fas fa-paper-plane action-icon" id="sendBtn">
                        <span class="icon-tooltip">Send</span>
                    </i>
                </div>
            </div>
        </div>

        <div class="tags-dropdown" id="tagsDropdown">
            <ul>
                <li>Gemini 2.5 Pro</li>
                <li>Grok 3</li>
                <li>Claude 3.5 Sonnet</li>
                <li>GPT-4o</li>
                <li>DeepSeek R1</li>
            </ul>
        </div>

        <div class="prompt-suggestions-list" id="promptSuggestionsList">
            <!-- JS will populate this -->
        </div>
    </div>

    <script>
        const searchBarOuter = document.getElementById('searchBarOuter');
        const searchInput = document.getElementById('searchInput');
        const hashtagBtn = document.getElementById('hashtagBtn');
        const copyBtn = document.getElementById('copyBtn');
        const sendBtn = document.getElementById('sendBtn');
        const tagsDropdown = document.getElementById('tagsDropdown');
        const promptSuggestionsList = document.getElementById('promptSuggestionsList');

        const originalPlaceholder = searchInput.placeholder;

        searchInput.addEventListener('focus', () => {
            searchBarOuter.classList.add('focused-outer');
            searchInput.placeholder = '';
            if (searchInput.value.length > 0 && promptSuggestionsList.children.length > 0 && !tagsDropdown.classList.contains('visible')) {
                promptSuggestionsList.classList.add('visible');
            }
        });

        searchInput.addEventListener('blur', () => {
            searchBarOuter.classList.remove('focused-outer');
            if (searchInput.value === '') {
                searchInput.placeholder = originalPlaceholder;
            }
            setTimeout(() => {
                const activePopup = document.querySelector('.tags-dropdown.visible, .prompt-suggestions-list.visible');
                if (!searchBarOuter.contains(document.activeElement) &&
                    (!activePopup || !activePopup.contains(document.activeElement)) &&
                    document.activeElement !== searchInput) {
                    if (!tagsDropdown.classList.contains('visible')) {
                        if (promptSuggestionsList.classList.contains('visible') && !promptSuggestionsList.dataset.clicked) {
                            promptSuggestionsList.classList.remove('visible');
                        }
                        delete promptSuggestionsList.dataset.clicked;
                    }
                }
            }, 150);
        });

        function togglePopup(popupElement) {
            const isOpening = !popupElement.classList.contains('visible');
            tagsDropdown.classList.remove('visible');
            promptSuggestionsList.classList.remove('visible');
            if (isOpening) {
                popupElement.classList.add('visible');
            }
        }

        hashtagBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            togglePopup(tagsDropdown);
        });

        copyBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            navigator.clipboard.writeText(searchInput.value).then(() => {
                alert('Prompt copied to clipboard!');
            });
        });

        sendBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            alert('Prompt sent: ' + searchInput.value);
        });

        const basePromptSuggestions = {
            "redacta": [
                "redacta un correo", "redacta un artículo", "redacta una noticia",
                "redacta un párrafo",
            ],
        };

        searchInput.addEventListener('input', function() {
            const inputText = this.value.toLowerCase().trim();
            const firstWord = inputText.split(' ')[0];
            promptSuggestionsList.innerHTML = '';

            if (inputText.length > 0) {
                let filteredSuggestions = [];
                if (basePromptSuggestions[firstWord]) {
                    filteredSuggestions = basePromptSuggestions[firstWord]
                        .filter(sugg => sugg.toLowerCase().startsWith(inputText))
                        .slice(0, 4);
                }

                if (filteredSuggestions.length > 0 && !tagsDropdown.classList.contains('visible')) {
                    tagsDropdown.classList.remove('visible');

                    filteredSuggestions.forEach(suggText => {
                        const item = document.createElement('div');
                        item.classList.add('prompt-suggestion-item');
                        item.innerHTML = `<i class="fas fa-check-circle"></i><span>${suggText}</span>`; /* Changed icon to check circle */
                        item.addEventListener('mousedown', (e) => {
                            e.preventDefault();
                            searchInput.value = suggText;
                            promptSuggestionsList.classList.remove('visible');
                            promptSuggestionsList.dataset.clicked = "true";
                        });
                        promptSuggestionsList.appendChild(item);
                    });
                    promptSuggestionsList.classList.add('visible');
                } else {
                    promptSuggestionsList.classList.remove('visible');
                }
            } else {
                promptSuggestionsList.classList.remove('visible');
            }
        });

        document.addEventListener('click', (event) => {
            const isClickInsideSearchArea = searchBarOuter.contains(event.target) ||
                                        tagsDropdown.contains(event.target) ||
                                        promptSuggestionsList.contains(event.target);
            const isClickInsideAuth = event.target.closest('.auth-buttons');

            if (!isClickInsideSearchArea && !isClickInsideAuth) {
                tagsDropdown.classList.remove('visible');
                promptSuggestionsList.classList.remove('visible');
            }
        });
    </script>
    <p style="text-align: center; margin-top: 40px; color: #555; font-size: 0.9rem;">Copyright © 2023 - 2025 All Hub. All rights reserved.</p>
</body>
</html>
```