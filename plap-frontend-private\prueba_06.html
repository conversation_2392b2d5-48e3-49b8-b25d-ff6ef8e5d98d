<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        /* Remove most old glassmorphism variables */
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;

            /* New Aesthetic Colors */
            --color-bg: #F3F4F6;
            --color-card-bg: #FFFFFF;
            --color-text: #111827;
            --color-cta: #3B82F6;
            --color-shadow-light: #E5E7EB; /* Specified shadow colors - using for soft shadows */
            --color-shadow-dark: #D1D5DB;
            --color-icon-gray: #9ca3af; /* Light gray for icons */
            --color-accent-start: #FF8A00; /* Accent gradient start */
            --color-accent-end: #DA00FF;   /* Accent gradient end */
            --color-placeholder: #9ca3af; /* Light gray for placeholder */
            --color-hover-bg: #F9FAFB; /* Very subtle hover background */
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            /* Apply new background color */
            background-color: var(--color-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
            color: var(--color-text); /* Set default text color */
        }

        .all-hub-logo {
            position: absolute;
            top: 25px;
            left: 30px;
            font-family: var(--font-header);
            font-size: 1.5rem;
            font-weight: 700;
            /* Apply new text color */
            color: var(--color-text);
            z-index: 10;
        }

        .auth-buttons {
            position: absolute;
            top: 25px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }

        .auth-btn {
            padding: 8px 16px; /* Slightly adjusted padding */
            border-radius: 9999px; /* Pill shape */
            font-size: 0.95rem; /* Adjusted font size */
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block; /* Ensure padding works */
        }

        .btn-signin {
            background-color: transparent;
            /* Apply new text color for border and text */
            border: 1px solid var(--color-text);
            color: var(--color-text);
        }
        .btn-signin:hover {
            background-color: rgba(17, 24, 39, 0.05); /* Subtle hover */
        }

        .btn-signup {
            /* Apply CTA color */
            background-color: var(--color-cta);
            color: var(--color-card-bg); /* White text */
            border: none; /* Remove border */
            box-shadow: 0 4px 8px rgba(0,0,0,0.1); /* Subtle initial shadow */
            /* Remove old animation */
            /* animation: pulse-signup 2s infinite; */
        }
        /* Glowing hover effect for CTA */
        .btn-signup:hover {
            transform: translateY(-1px);
            box-shadow: 0 0 15px rgba(59, 130, 246, 0.6); /* Blue glow */
            /* animation-play-state: paused; Remove old animation specific styles */
        }

        /* Remove old pulse animation */
        /* @keyframes pulse-signup { ... } */


        .header-container {
            text-align: center;
            margin-top: 18vh; /* Keep margin-top */
            margin-bottom: 30px;
            position: relative;
            z-index: 5;
            width: 100%;
        }

        .main-header {
            font-family: var(--font-header);
            font-size: 3rem;
            margin-bottom: 5px;
            font-weight: 900;
            letter-spacing: 1px;
            /* Apply new text color, remove gradient */
            color: var(--color-text);
            /* Remove old gradient text styles */
            -webkit-background-clip: text;
            -webkit-text-fill-color: initial; /* Reset text fill color */
            background-clip: text;
            color: initial; /* Reset color */
             color: var(--color-text); /* Re-apply new text color */

            animation: fadeInDown 1s ease-out;
        }

        .copilot-subtitle {
            font-family: var(--font-body);
            font-size: 1.05rem; /* Slightly larger subtitle */
            font-weight: 400; /* Adjusted weight */
             /* Apply new text color */
            color: var(--color-text);
            margin-top: 10px;
            margin-bottom: 10px;
            animation: fadeInDown 1s ease-out 0.2s;
            animation-fill-mode: backwards;
        }

        .search-area-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 700px; /* Adjusted max-width for search bar */
            margin-top: 8vh; /* Adjusted margin-top */
            margin-bottom: auto;
            position: relative;
            animation: fadeInUp 1s ease-out 0.3s;
            animation-fill-mode: backwards;
        }

        .search-bar-glass-outer {
            width: 100%;
            /* Apply new card background */
            background-color: var(--color-card-bg);
            /* Remove glassmorphism styles */
            backdrop-filter: none;
            -webkit-backdrop-filter: none;
            border: none; /* Remove old border */
            border-radius: 30px; /* Slightly larger border radius for pill shape */
            padding: 10px; /* Adjusted padding */
            /* Apply soft outset shadow */
            box-shadow: 0 5px 15px rgba(0,0,0,0.1); /* Standard soft shadow */
            position: relative;
            /* Ensure padding and min-height are applied correctly */
            display: flex;
            align-items: center;
            min-height: 56px; /* Minimum height for the bar */
            box-sizing: border-box;

            transition: box-shadow 0.3s ease, transform 0.3s ease; /* Keep transitions */

            /* --- Accent Gradient Border Effect --- */
            /* Use a pseudo-element for the gradient border on focus */
        }

        .search-bar-glass-outer::before {
            content: '';
            position: absolute;
            top: -2px; /* Extend slightly beyond the border */
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(to right, var(--color-accent-start), var(--color-accent-end));
            z-index: -1; /* Place behind the content */
            border-radius: inherit; /* Match the parent's border radius */
            opacity: 0;
            transition: opacity 0.3s ease;
        }

        .search-bar-glass-outer.focused-outer::before {
             opacity: 1; /* Show the gradient on focus */
        }

        .search-bar-glass-outer:hover {
            transform: scale(1.005); /* Subtle scale */
             box-shadow: 0 8px 20px rgba(0,0,0,0.12); /* Slightly increased shadow on hover */
        }

        /* Remove the inner glass container - combine styles into outer */
        /* .search-bar-glass-inner { ... } */

        /* Style the inner flex container that was .search-bar-glass-inner */
         .search-bar-glass-outer > div { /* Target the direct child div */
            display: flex;
            align-items: center;
            /* background-color: var(--glass-inner-bg); Removed old bg */
            border-radius: 20px; /* Inner radius if needed, but outer handles shape */
            padding: 0; /* Remove inner padding, use outer padding */
            min-height: auto; /* Auto height, outer sets min-height */
            box-sizing: border-box;
            box-shadow: none;
            flex-grow: 1; /* Make this inner div take available space */
         }


        .search-input {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            /* Apply new text color */
            color: var(--color-text);
            margin-left: 15px;
            font-size: 1rem; /* Slightly larger input font */
            font-family: var(--font-body);
            font-weight: 500;
        }
        .search-input::placeholder {
            /* Apply new placeholder color */
            color: var(--color-placeholder);
            opacity: 1;
            font-weight: 400; /* Adjusted weight */
        }

        .search-actions {
            display: flex;
            align-items: center;
            gap: 12px; /* Adjusted gap */
            margin-left: 15px; /* Adjusted margin */
        }
        .action-icon {
            font-size: 1.1rem; /* Slightly larger icons */
            /* Apply new icon color */
            color: var(--color-icon-gray);
            cursor: pointer;
            transition: color 0.2s ease, transform 0.2s ease;
            padding: 4px;
            position: relative;
        }
        .action-icon:hover {
            color: var(--color-text); /* Hover to dark text color */
            transform: scale(1.1);
        }
        .action-icon:hover .icon-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(0);
        }

        .icon-tooltip {
            position: absolute;
            bottom: calc(100% + 10px); /* Adjusted position */
            left: 50%;
            transform: translateX(-50%) translateY(4px);
            /* Apply new tooltip styles */
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-shadow-light); /* Light border */
            box-shadow: 0 2px 5px rgba(0,0,0,0.1); /* Subtle shadow */
            color: var(--color-text); /* Dark text */
            font-size: 0.85rem;
            font-family: var(--font-body);
            padding: 6px 10px; /* Adjusted padding */
            border-radius: 6px;
            white-space: nowrap;
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
            z-index: 100;
        }
        .icon-tooltip::after {
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            /* Match border color */
            border-color: var(--color-shadow-light) transparent transparent transparent;
        }

        .tags-dropdown,
        .prompt-suggestions-list {
            position: absolute;
            top: calc(100% + 12px);
            left: 0;
            right: 0;
            /* Apply new card styles */
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-shadow-light);
            box-shadow: 0 5px 15px rgba(0,0,0,0.1);
            border-radius: 12px; /* Rounded corners */
            padding: 10px; /* Add padding */
            opacity: 0;
            visibility: hidden;
            transform: translateY(8px);
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
            z-index: 95;
            max-height: 280px;
            overflow-y: auto;
        }
         .tags-dropdown.visible,
         .prompt-suggestions-list.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
         }
        .tags-dropdown ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .tags-dropdown li {
            padding: 8px 12px; /* Adjusted padding */
            font-size: 0.95rem; /* Adjusted font size */
            /* Apply new text color */
            color: var(--color-text);
            cursor: pointer;
            border-radius: 8px; /* Rounded corners for items */
            transition: background-color 0.2s ease;
        }
        .tags-dropdown li:hover {
            /* Apply new hover background */
            background-color: var(--color-hover-bg);
        }

        .prompt-suggestions-list {
            padding: 0; /* Remove padding here, added to the main container style */
             border-radius: 12px; /* Ensure consistency */
        }
        /* Add back padding when visible, if removed above */
         .prompt-suggestions-list.visible {
             padding: 10px;
         }


        .prompt-suggestion-item {
            display: flex;
            align-items: center;
            padding: 10px 15px;
            font-size: 1rem; /* Adjusted font size */
            /* Apply new text color */
            color: var(--color-text);
            cursor: pointer;
            border-radius: 8px; /* Rounded corners */
            transition: background-color 0.2s ease, color 0.2s ease;
            margin-bottom: 5px;
        }
        .prompt-suggestion-item:last-child {
            margin-bottom: 0;
        }
        .prompt-suggestion-item:hover {
            /* Apply new hover background */
            background-color: var(--color-hover-bg);
        }
        .prompt-suggestion-item .fa-check-circle {
            margin-right: 10px;
            /* Apply new light icon color */
            color: var(--color-icon-gray);
            font-size: 0.95rem; /* Adjusted icon size */
        }

        /* Animations */
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-20px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(20px); } to { opacity: 1; transform: translateY(0); } }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .auth-buttons {
                top: 15px;
                right: 15px;
                gap: 10px;
            }
            .auth-btn {
                padding: 6px 12px;
                font-size: 0.85rem;
            }
            .header-container { margin-top: 12vh;}
            .main-header { font-size: 2.5rem; }
            .copilot-subtitle { font-size: 0.95rem; margin-bottom: 20px; }
            .search-area-container { max-width: 95%; margin-top: 5vh; }
            .search-bar-glass-outer { border-radius: 26px; padding: 8px; min-height: 50px;}
            .search-input { font-size: 0.95rem; }
            .search-actions { gap: 10px; margin-left: 10px; }
            .action-icon { font-size: 1.05rem; }
            .tags-dropdown, .prompt-suggestions-list { border-radius: 10px; }
            .tags-dropdown li, .prompt-suggestion-item { font-size: 0.9rem; padding: 8px 10px; }
            .icon-tooltip { font-size: 0.8rem; padding: 5px 8px; bottom: calc(100% + 8px);}
             .icon-tooltip::after { bottom: calc(100% + 2px); } /* Adjust pointer */
        }
        @media (max-width: 480px) {
             .all-hub-logo {
                top: 15px;
                left: 15px;
                font-size: 1.2rem;
            }
            .auth-buttons {
                position: static;
                width: 100%;
                justify-content: center;
                margin-top: 15px;
                margin-bottom: 20px;
            }
            .header-container { margin-top: 5vh;}
            .main-header { font-size: 2rem; }
            .copilot-subtitle { font-size: 0.85rem; }
            .search-area-container { margin-top: 2vh; }
            .search-bar-glass-outer { border-radius: 24px; padding: 6px; min-height: 48px;}
            .search-input { font-size: 0.9rem; margin-left: 10px;}
            .search-actions { gap: 8px; margin-left: 8px; }
            .action-icon { font-size: 1rem; }
             .tags-dropdown, .prompt-suggestions-list { border-radius: 8px; }
            .tags-dropdown li, .prompt-suggestion-item { font-size: 0.85rem; padding: 6px 8px; }
             .prompt-suggestion-item .fa-check-circle { margin-right: 8px; font-size: 0.9rem;}
            .icon-tooltip { font-size: 0.75rem; padding: 3px 6px; bottom: calc(100% + 6px);}
             .icon-tooltip::after { bottom: calc(100% + 1px); } /* Adjust pointer */
        }
         /* Adjusting the direct child div flex container in search bar */
         @media (max-width: 480px) {
             .search-bar-glass-outer > div {
                  padding: 0; /* Ensure no extra padding on small screens */
             }
         }
    </style>
</head>
<body>
    <div class="all-hub-logo">allhub</div>
    <div class="auth-buttons">
        <a href="#" class="auth-btn btn-signin">Sign In</a>
        <a href="#" class="auth-btn btn-signup">Sign Up Free</a>
    </div>

    <div class="header-container">
        <h1 class="main-header">Prompt like a pro</h1>
        <p class="copilot-subtitle">Craft the perfect prompts with intelligent suggestions</p>
    </div>

    <div class="search-area-container">
        <!-- The outer div is now the "card" -->
        <div class="search-bar-glass-outer" id="searchBarOuter">
             <!-- This inner div is just for layout -->
            <div style="display: flex; align-items: center; flex-grow: 1;">
                <i class="fas fa-brain action-icon" id="hashtagBtn">
                    <span class="icon-tooltip">Select Model</span>
                </i>
                <input type="text" class="search-input" id="searchInput" placeholder="Describe your prompt clearly for the best response..." autocomplete="off">
                <div class="search-actions">
                    <i class="fas fa-copy action-icon" id="copyBtn">
                        <span class="icon-tooltip">Copy</span>
                    </i>
                    <i class="fas fa-paper-plane action-icon" id="sendBtn">
                        <span class="icon-tooltip">Send</span>
                    </i>
                </div>
            </div>
        </div>

        <div class="tags-dropdown" id="tagsDropdown">
            <ul>
                <li>Gemini 2.5 Pro</li>
                <li>Grok 3</li>
                <li>Claude 3.5 Sonnet</li>
                <li>GPT-4o</li>
                <li>DeepSeek R1</li>
            </ul>
        </div>

        <div class="prompt-suggestions-list" id="promptSuggestionsList">
            <!-- JS will populate this -->
        </div>
    </div>

    <p style="text-align: center; margin-top: 40px; color: var(--color-text); font-size: 0.9rem; width: 100%;">Copyright © 2023 - 2025 All Hub. All rights reserved.</p>

    <script>
        const searchBarOuter = document.getElementById('searchBarOuter');
        // const searchBarInner = document.getElementById('searchBarInner'); // Removed as inner div is just for layout
        const searchInput = document.getElementById('searchInput');
        const hashtagBtn = document.getElementById('hashtagBtn');
        const copyBtn = document.getElementById('copyBtn');
        const sendBtn = document.getElementById('sendBtn');
        const tagsDropdown = document.getElementById('tagsDropdown');
        const promptSuggestionsList = document.getElementById('promptSuggestionsList');

        const originalPlaceholder = searchInput.placeholder;

        searchInput.addEventListener('focus', () => {
            searchBarOuter.classList.add('focused-outer');
            searchInput.placeholder = '';
             // Only show suggestions if input has value AND suggestions are available AND dropdown is not open
            if (searchInput.value.length > 0 && promptSuggestionsList.children.length > 0 && !tagsDropdown.classList.contains('visible')) {
                 // Trigger input event manually to populate suggestions if needed
                 const event = new Event('input', { bubbles: true });
                 searchInput.dispatchEvent(event);
            }
        });

        searchInput.addEventListener('blur', () => {
             // Add a small delay before removing focused-outer to allow click on suggestions/dropdown
             setTimeout(() => {
                 const activeElement = document.activeElement;
                 // Check if focus moved inside the search bar container, tags dropdown, or suggestions list
                 const isFocusWithinSearchArea = searchBarOuter.contains(activeElement) ||
                                                 tagsDropdown.contains(activeElement) ||
                                                 promptSuggestionsList.contains(activeElement);

                if (!isFocusWithinSearchArea) {
                     searchBarOuter.classList.remove('focused-outer');
                     if (searchInput.value === '') {
                        searchInput.placeholder = originalPlaceholder;
                    }
                     // Hide suggestions and dropdown only if focus moved outside
                    tagsDropdown.classList.remove('visible');
                    promptSuggestionsList.classList.remove('visible');
                 }
             }, 50); // Small delay
        });


        function togglePopup(popupElement) {
            const isOpening = !popupElement.classList.contains('visible');
            // Close any currently open popups first
            tagsDropdown.classList.remove('visible');
            promptSuggestionsList.classList.remove('visible');
            if (isOpening) {
                popupElement.classList.add('visible');
                // If opening a popup, keep the search bar focused state
                 searchBarOuter.classList.add('focused-outer');
            } else {
                 // If closing, check if focus is still in the input, if not, remove focused state
                 if (document.activeElement !== searchInput) {
                    searchBarOuter.classList.remove('focused-outer');
                 }
            }
        }

        hashtagBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            togglePopup(tagsDropdown);
        });

        copyBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            const textToCopy = searchInput.value;
            if (textToCopy) {
                 navigator.clipboard.writeText(textToCopy).then(() => {
                    // Provide user feedback
                    const originalTooltipText = event.target.querySelector('.icon-tooltip').textContent;
                    event.target.querySelector('.icon-tooltip').textContent = 'Copied!';
                    setTimeout(() => {
                        event.target.querySelector('.icon-tooltip').textContent = originalTooltipText;
                    }, 1500);
                 }).catch(err => {
                     console.error('Failed to copy text: ', err);
                     alert('Failed to copy prompt.');
                 });
             } else {
                 alert('Nothing to copy.');
             }
        });

        sendBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            const textToSend = searchInput.value;
             if (textToSend) {
                alert('Prompt sent: ' + textToSend);
                // Here you would typically handle sending the prompt
             } else {
                alert('Prompt is empty.');
             }
        });

         // Make dropdown items clickable
         tagsDropdown.querySelectorAll('li').forEach(item => {
             item.addEventListener('click', (event) => {
                 event.stopPropagation(); // Prevent document click from closing immediately
                 const selectedModel = item.textContent;
                 // You might want to update a visual indicator or store the selected model
                 console.log("Selected model:", selectedModel); // For demo
                 // Close the dropdown
                 tagsDropdown.classList.remove('visible');
                  // Keep search input focused
                 searchInput.focus();
             });
         });


        const basePromptSuggestions = {
            "redacta": [
                "redacta un correo", "redacta un artículo", "redacta una noticia",
                "redacta un párrafo",
            ],
             "escribe": [
                 "escribe un poema", "escribe un guion", "escribe una historia corta"
             ],
              "genera": [
                 "genera código python", "genera ideas de negocio", "genera un plan de marketing"
              ]
             // Add more base prompts as needed
        };

        searchInput.addEventListener('input', function() {
            const inputText = this.value.toLowerCase().trim();
            const firstWord = inputText.split(' ')[0];
            promptSuggestionsList.innerHTML = ''; // Clear previous suggestions

            if (inputText.length > 0) {
                let filteredSuggestions = [];
                // Check for suggestions starting with the first word
                if (basePromptSuggestions[firstWord]) {
                    filteredSuggestions = basePromptSuggestions[firstWord]
                        .filter(sugg => sugg.toLowerCase().startsWith(inputText));
                }
                // Also include suggestions from other words if they match later parts (optional)
                // Example: If input is "correo", suggest "redacta un correo"
                 Object.values(basePromptSuggestions).flat().forEach(sugg => {
                     if (sugg.toLowerCase().includes(inputText) && !filteredSuggestions.includes(sugg) && sugg.toLowerCase().startsWith(inputText)) {
                         // Make sure suggestions starting with the input come first
                         // The filter above handles this, but this ensures broader matches aren't missed
                         // Re-filtering the initial list and then adding others might be better
                     }
                 });

                // Re-filter and limit the combined list if you added more suggestions
                filteredSuggestions = Object.values(basePromptSuggestions).flat()
                    .filter(sugg => sugg.toLowerCase().startsWith(inputText)) // Only show suggestions that START with the input
                    .slice(0, 6); // Limit the number of suggestions


                if (filteredSuggestions.length > 0 && !tagsDropdown.classList.contains('visible')) {
                    tagsDropdown.classList.remove('visible'); // Close tags if suggestions appear

                    filteredSuggestions.forEach(suggText => {
                        const item = document.createElement('div');
                        item.classList.add('prompt-suggestion-item');
                        // Highlight the matching part
                        const matchIndex = suggText.toLowerCase().indexOf(inputText);
                        const beforeMatch = suggText.substring(0, matchIndex);
                        const matchText = suggText.substring(matchIndex, matchIndex + inputText.length);
                        const afterMatch = suggText.substring(matchIndex + inputText.length);

                        item.innerHTML = `<i class="fas fa-check-circle"></i><span>${beforeMatch}<strong>${matchText}</strong>${afterMatch}</span>`;

                        item.addEventListener('mousedown', (e) => {
                            e.preventDefault(); // Prevent blur on search input
                            searchInput.value = suggText;
                            promptSuggestionsList.classList.remove('visible');
                            // Keep search input focused after selecting
                             searchInput.focus();
                             // Manually trigger input event if needed to show suggestions based on the *new* value
                             // const inputEvent = new Event('input', { bubbles: true });
                             // searchInput.dispatchEvent(inputEvent);
                        });
                        promptSuggestionsList.appendChild(item);
                    });
                    promptSuggestionsList.classList.add('visible');
                } else {
                    promptSuggestionsList.classList.remove('visible'); // Hide if no suggestions match
                }
            } else {
                promptSuggestionsList.classList.remove('visible'); // Hide if input is empty
            }
        });


        // Hide popups when clicking outside
        document.addEventListener('click', (event) => {
            const isClickInsideSearchArea = searchBarOuter.contains(event.target) ||
                                        tagsDropdown.contains(event.target) ||
                                        promptSuggestionsList.contains(event.target);
            const isClickInsideAuth = event.target.closest('.auth-buttons');

            if (!isClickInsideSearchArea && !isClickInsideAuth) {
                tagsDropdown.classList.remove('visible');
                promptSuggestionsList.classList.remove('visible');
                 // Only remove focused-outer class if not focused on the input
                 if(document.activeElement !== searchInput){
                     searchBarOuter.classList.remove('focused-outer');
                 }
            }
        });

         // Ensure suggestions/dropdowns stay open if clicking on them
         tagsDropdown.addEventListener('mousedown', (e) => { e.preventDefault(); });
         promptSuggestionsList.addEventListener('mousedown', (e) => { e.preventDefault(); });


    </script>
</body>
</html>