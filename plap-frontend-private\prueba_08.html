<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;

            /* Aesthetic Color Guidelines */
            --color-bg: #F3F4F6;                            /* Background: Very light gray */
            --color-card-bg: #FFFFFF;                       /* Cards and Containers: White */
            --color-text: #111827;                          /* Text color: Dark gray */
            --color-cta: #3B82F6;                           /* Call-to-action button: Bright blue */
            --color-cta-text: #FFFFFF;                      /* CTA text: White */
            
            /* Shadows and Borders for Cards/Containers */
            --color-shadow-outset-primary: #E5E7EB;         /* Soft outset shadow color 1 (for borders or very light elements) */
            --color-shadow-outset-secondary: #D1D5DB;      /* Soft outset shadow color 2 (for borders or slightly darker elements) */
            /* For actual shadow projection, using rgba for softness/depth */
            --shadow-soft-outset: 0 6px 15px -4px rgba(0, 0, 0, 0.07), 0 3px 10px -5px rgba(0, 0, 0, 0.05);
            --shadow-soft-outset-hover: 0 12px 25px -6px rgba(0, 0, 0, 0.07), 0 5px 12px -5px rgba(0, 0, 0, 0.05);


            /* Accent Gradient */
            --color-accent-start: #FF8A00;
            --color-accent-end: #DA00FF;

            /* Icons and UI Indicators */
            --color-icon-gray: #9CA3AF;                     /* Light gray for icons */
            --color-placeholder: #9CA3AF;                   /* Placeholder text, similar to icons */
            
            /* Misc */
            --color-hover-bg: #F9FAFB;                      /* Subtle background hover for list items */
            --border-radius-card: 16px;                     /* Consistent border radius for cards */
            --border-radius-pill: 9999px;                   /* For pill shapes */
            --border-radius-element: 8px;                   /* For smaller elements within cards */
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background-color: var(--color-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
            color: var(--color-text);
        }

        .all-hub-logo {
            position: absolute;
            top: 25px;
            left: 30px;
            font-family: var(--font-header);
            font-size: 1.5rem;
            font-weight: 700;
            color: #0750c5;
            z-index: 10;
        }

        .auth-buttons {
            position: absolute;
            top: 25px;
            right: 30px;
            display: flex;
            gap: 15px;
            z-index: 10;
        }

        .auth-btn {
            padding: 8px 18px;
            border-radius: var(--border-radius-pill);
            font-size: 0.9rem;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
            line-height: 1.5; /* Ensure consistent height */
        }

        .btn-signin {
            background-color: transparent;
            border: 1px solid var(--color-shadow-outset-secondary); /* Using specified shadow color for border */
            color: var(--color-text);
        }
        .btn-signin:hover {
            background-color: rgba(17, 24, 39, 0.03); /* Very subtle dark hover */
            border-color: var(--color-text);
        }

        .btn-signup { /* Call-to-action button */
            background: linear-gradient(to right, #60A5FA, #01378d); /* Light to dark blue gradient */
            color: var(--color-cta-text);
            border: none;
            box-shadow: var(--shadow-soft-outset);
        }
        .btn-signup:hover { /* Glowing hover effect */
            transform: translateY(-2px);
            box-shadow: 0 0 15px 0px rgba(59, 130, 246, 0.6), var(--shadow-soft-outset-hover);
            animation-play-state: var(--color-accent-start); /* Stop pulse animation on hover */
        }

        .header-container {
            text-align: center;
            margin-top: 18vh;
            margin-bottom: 30px;
            position: relative;
            z-index: 5;
            width: 100%;
        }

        .main-header {
            font-family: var(--font-header);
            font-size: 3rem;
            margin-bottom: 5px;
            font-weight: 600;
            letter-spacing: 1px;
            /* Apply gradient to text */
            background: linear-gradient(90deg, #FF6EC4, #7873F5, #4ADEDE, #FFCB57);
            background-size: 800% 800%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            animation: gradientShift 15s ease infinite;
            display: inline-block;
}

@keyframes gradientShift {
  0% {
    background-position: 0% 50%;
  }
  50% {
    background-position: 100% 50%;
  }
  100% {
    background-position: 0% 50%;
  }
}

        .copilot-subtitle {

            font-size: 1.1rem; /* 18px, un poco más grande que el default */
            font-weight: 400;
            color: #6B7280; /* gris neutro elegante */
            line-height: 1.6;
            margin-top: 0.5rem;
            margin-bottom: 1.5rem;
            }

        .search-area-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%;
            max-width: 700px;
            margin-top: 8vh;
            margin-bottom: auto;
            position: relative;
            animation: fadeInUp 2s ease-out 0.3s;
            animation-fill-mode: backwards;
        }

        /* Main Interactive Card: Search Bar */
        .search-bar-card {
            width: 100%;
            background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card); /* Rounded corners */
            padding: 8px; /* Padding for the "inset" feel of the inner elements */
            box-shadow: var(--shadow-soft-outset); /* Soft outset shadow for depth */
            border: 1px solid var(--color-shadow-outset-primary); /* Subtle border using one of the specified shadow colors */
            position: relative;
            display: flex; /* To contain the inner flex div */
            align-items: center;
            min-height: 58px; 
            box-sizing: border-box;
            transition: box-shadow 0.3s ease, border-color 0.3s ease;
        }
        
        .search-bar-card:hover {
            box-shadow: var(--shadow-soft-outset-hover);
        }

        /* Accent Gradient for Search Bar (main interactive card) on focus */
        .search-bar-card::before {
            content: '';
            position: absolute;
            top: -2px; 
            left: -2px;
            right: -2px;
            bottom: -2px;
            background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-end));
            z-index: -1; 
            border-radius: calc(var(--border-radius-card) + 2px); /* Match parent's radius + border thickness */
            opacity: 0;
            transition: opacity 0.3s ease;
            pointer-events: none; /* Ensure it doesn't interfere with clicks */
        }

        .search-bar-card.focused-card::before {
            opacity: 1; /* Show gradient outline on focus */
        }
        .search-bar-card.focused-card {
             border-color: transparent; /* Hide the base border when gradient is active */
        }


        /* Inner container for search elements, mimicking an inset feel */
        .search-bar-inner-content {
            display: flex;
            align-items: center;
            flex-grow: 1;
            background-color: var(--color-card-bg); /* Match card background */
            border-radius: calc(var(--border-radius-card) - 10px); /* Slightly smaller radius than parent for inset look */
            padding: 6px 8px; /* Padding for elements inside */
            /* box-shadow: inset 0 1px 2px rgba(0,0,0,0.03); Optional subtle inset shadow */
        }

        .search-input {
            flex-grow: 1;
            background: transparent;
            border: none;
            outline: none;
            color: var(--color-text);
            margin-left: 12px;
            font-size: 1rem;
            font-family: var(--font-body);
            font-weight: 400; /* Normal weight for input text */
        }
        .search-input::placeholder {
            color: var(--color-placeholder);
            opacity: 1;
            font-weight: 400;
        }

        .search-actions {
            display: flex;
            align-items: center;
            gap: 10px;
            margin-left: 12px;
        }
        .action-icon {
            font-size: 1.1rem;
            color: var(--color-icon-gray);
            cursor: pointer;
            transition: color 0.2s ease, transform 0.2s ease;
            padding: 6px; /* Clickable area */
            border-radius: var(--border-radius-element);
            position: relative;
        }
        .action-icon:hover {
            color: var(--color-text);
            background-color: var(--color-hover-bg); /* Subtle bg on icon hover */
            transform: scale(1.05);
        }
        .action-icon:hover .icon-tooltip {
            opacity: 1;
            visibility: visible;
            transform: translateX(-50%) translateY(-4px); /* Adjusted for better positioning */
        }

        /* Tooltips and Dropdowns (Containers) */
        .icon-tooltip,
        .tags-dropdown,
        .prompt-suggestions-list {
            background-color: var(--color-card-bg); /* White */
            border: 1px solid var(--color-shadow-outset-primary); /* Soft border */
            box-shadow: var(--shadow-soft-outset); /* Soft outset shadow */
            border-radius: var(--border-radius-element); /* Rounded corners */
            color: var(--color-text); /* Dark gray text */
            font-family: var(--font-body);
            z-index: 100; /* Ensure on top */
            opacity: 0;
            visibility: hidden;
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
        }

            #hashtagBtn .icon-tooltip {
            bottom: calc(100% + 16px); /* Adjust 'Select Model' tooltip only */
            transform: translateX(-50%) translateY(-4px);
            }

            #copyBtn .icon-tooltip {
            bottom: calc(100% + 16px); /* Example: Adjust copy tooltip only */
            }

            #sendBtn .icon-tooltip {
            bottom: calc(100% + 16px); /* Example: Adjust send tooltip only */
            }

        .icon-tooltip {
            position: absolute;
            bottom: calc(100% + 8px);
            left: 50%;
            transform: translateX(-50%) translateY(4px);
            font-size: 0.85rem;
            padding: 6px 10px;
            white-space: nowrap;
        }
        .icon-tooltip::after { /* Arrow for tooltip */
            content: '';
            position: absolute;
            top: 100%;
            left: 50%;
            margin-left: -5px;
            border-width: 5px;
            border-style: solid;
            border-color: var(--color-shadow-outset-primary) transparent transparent transparent;
            transition: border-color 0.25s ease; /* Smooth transition for arrow */
        }
        .action-icon:hover .icon-tooltip::after {
             border-top-color: var(--color-shadow-outset-primary); /* Keep consistent or adjust */
        }


        .tags-dropdown,
        .prompt-suggestions-list {
            position: absolute;
            top: calc(100% + 8px); /* Spacing from search bar */
            left: 0;
            right: 0;
            padding: 8px;
            transform: translateY(8px);
            max-height: 280px;
            overflow-y: auto;
        }
         .tags-dropdown.visible,
         .prompt-suggestions-list.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
         }

        .tags-dropdown ul {
            list-style: none;
            padding: 0;
            margin: 0;
        }
        .tags-dropdown li,
        .prompt-suggestion-item {
            padding: 8px 12px;
            font-size: 0.95rem;
            color: var(--color-text);
            cursor: pointer;
            border-radius: calc(var(--border-radius-element) - 2px); /* Slightly smaller radius for list items */
            transition: background-color 0.2s ease, color 0.2s ease;
            display: flex;
            align-items: center;
        }
        .tags-dropdown li:hover,
        .prompt-suggestion-item:hover {
            background-color: var(--color-hover-bg);
        }
        
        .prompt-suggestion-item {
            margin-bottom: 4px; /* Spacing between suggestion items */
        }
        .prompt-suggestion-item:last-child {
            margin-bottom: 0;
        }
        .prompt-suggestion-item .fa-check-circle {
            margin-right: 10px;
            color: var(--color-icon-gray);
            font-size: 0.95rem;
        }
        .prompt-suggestion-item strong { /* For highlighting matched text */
            font-weight: 600; /* Or use accent color if desired */
        }


        /* Animations */
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-15px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px); } to { opacity: 1; transform: translateY(0); } }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .auth-buttons { top: 20px; right: 20px; gap: 10px; }
            .auth-btn { padding: 7px 15px; font-size: 0.85rem; }
            .header-container { margin-top: 15vh;}
            .main-header { font-size: 2.5rem; }
            .copilot-subtitle { font-size: 1rem; }
            .search-area-container { max-width: 90%; margin-top: 6vh; }
            .search-bar-card { min-height: 54px; border-radius: 14px;}
            .search-bar-inner-content { border-radius: 10px; padding: 5px 6px;}
            .search-input { font-size: 0.95rem; margin-left: 10px; }
            .search-actions { gap: 8px; margin-left: 10px; }
            .action-icon { font-size: 1.05rem; padding: 5px; }
            .tags-dropdown, .prompt-suggestions-list { border-radius: 10px; padding: 6px; }
            .tags-dropdown li, .prompt-suggestion-item { font-size: 0.9rem; padding: 7px 10px; }
            .icon-tooltip { font-size: 0.8rem; padding: 5px 8px; }
        }
        @media (max-width: 480px) {
             .all-hub-logo { top: 15px; left: 15px; font-size: 1.3rem; }
            .auth-buttons {
                position: static; /* Stack on small screens */
                width: 100%;
                justify-content: center;
                margin-top: 20px;
                margin-bottom: 25px;
            }
            .header-container { margin-top: 8vh;}
            .main-header { font-size: 2rem; }
            .copilot-subtitle { font-size: 0.9rem; }
            .search-area-container { margin-top: 4vh; }
            .search-bar-card { min-height: 50px; border-radius: 12px; padding: 6px;}
            .search-bar-inner-content { border-radius: 8px; padding: 4px 5px;}
            .search-input { font-size: 0.9rem; margin-left: 8px;}
            .search-actions { gap: 6px; margin-left: 8px; }
            .action-icon { font-size: 1rem; padding: 4px; }
            .tags-dropdown, .prompt-suggestions-list { border-radius: 8px; padding: 5px;}
            .tags-dropdown li, .prompt-suggestion-item { font-size: 0.85rem; padding: 6px 8px; }
            .icon-tooltip { font-size: 0.75rem; padding: 4px 7px; }
        }
        .copyright-footer {
            text-align: center;
            margin-top: 40px;
            color: var(--color-text);
            opacity: 0.7;
            font-size: 0.85rem;
            width: 100%;
            padding-bottom: 20px; /* Ensure some space at the very bottom */
        }
    @keyframes pulse {
        0% {
            box-shadow: 0 0 3px 3px rgba(59, 130, 246, 0.5);
        }
        70% {
            box-shadow: 0 0 8px 15px rgba(59, 130, 246, 0);
        }
        100% {
            box-shadow: 0 0 0 0px rgba(59, 130, 246, 0);
        }
    }
    </style>
  <style>
    body {
      background-image: linear-gradient(to bottom left, #ffffff, lch(91.25% 13.31 276.59));
    }
  </style>
</head>
<body>
    <div class="all-hub-logo">allhub</div>
    <div class="auth-buttons">
        <a href="#" class="auth-btn btn-signin">Sign In</a>
        <a href="#" class="auth-btn btn-signup" style="animation: pulse 3s infinite;">Sign Up Free</a>
    </div>

    <div class="header-container">
        <h1 class="main-header">Prompt like a pro</h1>
        <p class="copilot-subtitle">Craft the perfect prompts with intelligent suggestions</p>
    </div>

    <div class="search-area-container">
        <div class="search-bar-card" id="searchBarCard">
            <div class="search-bar-inner-content">
                <i class="fas fa-brain action-icon" id="hashtagBtn" title="Select Model"> <!-- Added title for basic tooltip -->
                    <span class="icon-tooltip">Select Model</span>
                </i>
                <input type="text" class="search-input" id="searchInput" placeholder="Describe your prompt clearly..." autocomplete="off">
                <div class="search-actions">
                    <i class="fas fa-copy action-icon" id="copyBtn" title="Copy Prompt">
                        <span class="icon-tooltip">Copy</span>
                    </i>
                    <i class="fas fa-paper-plane action-icon" id="sendBtn" title="Send Prompt">
                        <span class="icon-tooltip">Send</span>
                    </i>
                </div>
            </div>
        </div>

        <div class="tags-dropdown" id="tagsDropdownContainer">
            <ul>
                <li>Gemini 2.5 Pro</li>
                <li>Grok 3</li>
                <li>Claude 3.5 Sonnet</li>
                <li>GPT-4o</li>
                <li>DeepSeek R1</li>
            </ul>
        </div>

        <div class="prompt-suggestions-list" id="promptSuggestionsContainer">
            <!-- JS will populate this -->
        </div>
    </div>

    <p class="copyright-footer">Copyright © 2023 - 2025 All Hub. All rights reserved.</p>

    <script>
        const searchBarCard = document.getElementById('searchBarCard');
        const searchInput = document.getElementById('searchInput');
        const hashtagBtn = document.getElementById('hashtagBtn');
        // const copyBtn = document.getElementById('copyBtn'); // Defined below
        // const sendBtn = document.getElementById('sendBtn'); // Defined below
        const tagsDropdownContainer = document.getElementById('tagsDropdownContainer');
        const promptSuggestionsContainer = document.getElementById('promptSuggestionsContainer');

        const originalPlaceholder = searchInput.placeholder;

        searchInput.addEventListener('focus', () => {
            searchBarCard.classList.add('focused-card');
            searchInput.placeholder = 'Type your prompt here...'; // More engaging placeholder on focus
            
            // Show suggestions if input has value AND suggestions are available AND tags dropdown is not open
            if (searchInput.value.length > 0 && promptSuggestionsContainer.children.length > 0 && !tagsDropdownContainer.classList.contains('visible')) {
                 const event = new Event('input', { bubbles: true }); // Trigger input to re-evaluate suggestions
                 searchInput.dispatchEvent(event);
            } else if (searchInput.value.length === 0) { // If focused and empty, maybe show general suggestions?
                // For now, clear existing suggestions if input is empty on focus
                promptSuggestionsContainer.innerHTML = '';
                promptSuggestionsContainer.classList.remove('visible');
            }
        });

        searchInput.addEventListener('blur', () => {
             // Delay to allow click on popups
            setTimeout(() => {
                const activeElement = document.activeElement;
                const isFocusWithinSearchArea = searchBarCard.contains(activeElement) ||
                                                 tagsDropdownContainer.contains(activeElement) ||
                                                 promptSuggestionsContainer.contains(activeElement) ||
                                                 activeElement.closest('.action-icon');

                if (!isFocusWithinSearchArea) {
                    searchBarCard.classList.remove('focused-card');
                    if (searchInput.value === '') {
                        searchInput.placeholder = originalPlaceholder;
                    }
                    tagsDropdownContainer.classList.remove('visible');
                    promptSuggestionsContainer.classList.remove('visible');
                }
            }, 100); // Slightly longer delay for safety
        });

        function togglePopup(popupElement) {
            const isOpening = !popupElement.classList.contains('visible');
            
            // Close other popups first
            if (popupElement !== tagsDropdownContainer) tagsDropdownContainer.classList.remove('visible');
            if (popupElement !== promptSuggestionsContainer) promptSuggestionsContainer.classList.remove('visible');

            if (isOpening) {
                popupElement.classList.add('visible');
                searchBarCard.classList.add('focused-card'); // Keep card focused
            } else {
                popupElement.classList.remove('visible');
                // If not focusing input and no other popup is open, unfocus card
                if (document.activeElement !== searchInput && !tagsDropdownContainer.classList.contains('visible') && !promptSuggestionsContainer.classList.contains('visible')) {
                   // searchBarCard.classList.remove('focused-card'); // Handled by blur with timeout
                }
            }
        }

        hashtagBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            togglePopup(tagsDropdownContainer);
            searchInput.focus(); // Keep focus on input
        });
        
        document.getElementById('copyBtn').addEventListener('click', (event) => {
            event.stopPropagation();
            const textToCopy = searchInput.value;
            const tooltip = event.currentTarget.querySelector('.icon-tooltip'); // Get tooltip of the clicked icon
            const originalTooltipText = tooltip.textContent;

            if (textToCopy) {
                 navigator.clipboard.writeText(textToCopy).then(() => {
                    tooltip.textContent = 'Copied!';
                    setTimeout(() => {
                        tooltip.textContent = originalTooltipText;
                    }, 1500);
                 }).catch(err => {
                     console.error('Failed to copy: ', err);
                     tooltip.textContent = 'Copy Failed!';
                     setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500);
                 });
             } else {
                 tooltip.textContent = 'Nothing to copy!';
                 setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500);
             }
             searchInput.focus(); // Keep focus on input
        });

        document.getElementById('sendBtn').addEventListener('click', (event) => {
            event.stopPropagation();
            const textToSend = searchInput.value;
            const tooltip = event.currentTarget.querySelector('.icon-tooltip');
            const originalTooltipText = tooltip.textContent;

            if (textToSend) {
                // Simulate sending
                tooltip.textContent = 'Sending...';
                setTimeout(() => {
                    alert('Prompt sent: ' + textToSend);
                    tooltip.textContent = 'Sent!';
                     setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500);
                    // searchInput.value = ''; // Optionally clear input
                    // promptSuggestionsContainer.classList.remove('visible');
                }, 500);
             } else {
                 tooltip.textContent = 'Prompt is empty!';
                 setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500);
             }
             searchInput.focus(); // Keep focus on input
        });

         tagsDropdownContainer.querySelectorAll('li').forEach(item => {
             item.addEventListener('mousedown', (e) => { // Use mousedown to prevent blur before click registers
                 e.preventDefault();
                 const selectedModel = item.textContent;
                 // Example: Update a hidden input or display the selected model
                 console.log("Selected model:", selectedModel); 
                 // You could prepend it to the searchInput or handle it differently
                 // searchInput.value = selectedModel + ": " + searchInput.value;
                 tagsDropdownContainer.classList.remove('visible');
                 searchInput.focus();
             });
         });


        const basePromptSuggestions = {
            "redacta": ["redacta un correo electrónico formal", "redacta un ensayo sobre", "redacta una descripción de producto para", "redacta un post para redes sociales sobre"],
            "escribe": ["escribe un poema corto sobre", "escribe un guion para un video de", "escribe una historia de ficción acerca de", "escribe los pros y contras de"],
            "genera": ["genera ideas para un startup de", "genera un plan de contenidos para", "genera código Python para", "genera una lista de preguntas para una entrevista sobre"],
            "resume": ["resume este texto en 3 puntos clave", "resume el siguiente artículo", "resume las ideas principales de"],
            "traduce": ["traduce este texto a inglés", "traduce esta frase a francés", "traduce al español:"]
        };

        searchInput.addEventListener('input', function() {
            const inputText = this.value.toLowerCase().trim();
            promptSuggestionsContainer.innerHTML = ''; 

            if (inputText.length > 0) {
                let matchedSuggestions = [];
                const inputWords = inputText.split(" ");
                const firstInputWord = inputWords[0];

                // Prioritize suggestions starting with the first word of input
                if (basePromptSuggestions[firstInputWord]) {
                    basePromptSuggestions[firstInputWord].forEach(sugg => {
                        if (sugg.toLowerCase().startsWith(inputText) && !matchedSuggestions.some(ms => ms.text === sugg)) {
                            matchedSuggestions.push({text: sugg, score: 2}); // Higher score for direct prefix match
                        }
                    });
                }
                
                // Broader search: check if any suggestion starts with the full input text
                Object.values(basePromptSuggestions).flat().forEach(sugg => {
                    if (sugg.toLowerCase().startsWith(inputText) && !matchedSuggestions.some(ms => ms.text === sugg)) {
                         matchedSuggestions.push({text: sugg, score: 1});
                    }
                });

                // Sort by score (optional, if using different scores), then alphabetically, and limit
                matchedSuggestions.sort((a, b) => b.score - a.score || a.text.localeCompare(b.text));
                const filteredSuggestions = matchedSuggestions.map(s => s.text).slice(0, 5);


                if (filteredSuggestions.length > 0 && !tagsDropdownContainer.classList.contains('visible')) {
                    filteredSuggestions.forEach(suggText => {
                        const item = document.createElement('div');
                        item.classList.add('prompt-suggestion-item');
                        
                        const matchIndex = suggText.toLowerCase().indexOf(inputText);
                        let displayHTML = `<i class="fas fa-check-circle"></i><span>`;
                        if (matchIndex === 0) { // Highlight only if it's a prefix match
                            displayHTML += `<strong>${suggText.substring(0, inputText.length)}</strong>${suggText.substring(inputText.length)}`;
                        } else {
                            displayHTML += suggText; // Or highlight differently if contains but not prefix
                        }
                        displayHTML += `</span>`;
                        item.innerHTML = displayHTML;

                        item.addEventListener('mousedown', (e) => {
                            e.preventDefault(); 
                            searchInput.value = suggText;
                            promptSuggestionsContainer.classList.remove('visible');
                            searchInput.focus();
                            // Trigger input event again if you want suggestions based on the newly selected text
                            // const newEvent = new Event('input', { bubbles: true });
                            // searchInput.dispatchEvent(newEvent);
                        });
                        promptSuggestionsContainer.appendChild(item);
                    });
                    promptSuggestionsContainer.classList.add('visible');
                } else {
                    promptSuggestionsContainer.classList.remove('visible');
                }
            } else {
                promptSuggestionsContainer.classList.remove('visible');
            }
        });

        // Global click listener to hide popups
        document.addEventListener('click', (event) => {
            const clickedElement = event.target;
            const isSearchRelated = searchBarCard.contains(clickedElement) ||
                                    tagsDropdownContainer.contains(clickedElement) ||
                                    promptSuggestionsContainer.contains(clickedElement) ||
                                    clickedElement.closest('.action-icon'); // Important for icon clicks

            if (!isSearchRelated) {
                tagsDropdownContainer.classList.remove('visible');
                promptSuggestionsContainer.classList.remove('visible');
                // Only remove focused-card if searchInput itself is not the active element
                if(document.activeElement !== searchInput) {
                    searchBarCard.classList.remove('focused-card');
                     if (searchInput.value === '') searchInput.placeholder = originalPlaceholder;
                }
            }
        });
        
        // Prevent blur on mousedown for popups so click events can fire
        [tagsDropdownContainer, promptSuggestionsContainer].forEach(el => {
            el.addEventListener('mousedown', e => e.preventDefault());
        });

    </script>
</body>
</html>