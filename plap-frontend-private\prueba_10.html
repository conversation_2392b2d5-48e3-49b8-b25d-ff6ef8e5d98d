<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;
            --color-bg: #F3F4F6;
            --color-card-bg: #FFFFFF;
            --color-text: #111827;
            --color-cta: #3B82F6;
            --color-cta-text: #FFFFFF;
            --color-shadow-outset-primary: #E5E7EB;
            --color-shadow-outset-secondary: #D1D5DB;
            --shadow-soft-outset: 0 6px 15px -4px rgba(0, 0, 0, 0.07), 0 3px 10px -5px rgba(0, 0, 0, 0.05);
            --shadow-soft-outset-hover: 0 12px 25px -6px rgba(0, 0, 0, 0.07), 0 5px 12px -5px rgba(0, 0, 0, 0.05);
            --color-accent-start: #FF8A00;
            --color-accent-end: #DA00FF;
            --color-icon-gray: #9CA3AF;
            --color-placeholder: #9CA3AF;
            --color-hover-bg: #F9FAFB;
            --border-radius-card: 16px;
            --border-radius-pill: 9999px;
            --border-radius-element: 8px;
            --color-star-active: #FFD700; /* Gold color for active star */
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background-color: var(--color-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
            color: var(--color-text);
            background-image: linear-gradient(to bottom left, #ffffff, lch(91.25% 13.31 276.59));
        }
        
        .all-hub-logo { position: absolute; top: 25px; left: 30px; font-family: var(--font-header); font-size: 1.5rem; font-weight: 700; color: #023c99; z-index: 10; }
        .auth-buttons { position: absolute; top: 25px; right: 30px; display: flex; gap: 15px; z-index: 10; }
        .auth-btn { padding: 8px 18px; border-radius: var(--border-radius-pill); font-size: 0.9rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; line-height: 1.5; }
        .btn-signin { background-color: transparent; border: 1px solid var(--color-shadow-outset-secondary); color: var(--color-text); }
        .btn-signin:hover { background-color: rgba(17, 24, 39, 0.03); border-color: var(--color-text); }
        .btn-signup { background: linear-gradient(to right, #60A5FA, #01378d); color: var(--color-cta-text); border: none; box-shadow: var(--shadow-soft-outset); }
        .btn-signup:hover { transform: translateY(-2px); box-shadow: 0 0 15px 0px rgba(59, 130, 246, 0.6), var(--shadow-soft-outset-hover); animation-play-state: var(--color-accent-start); }
        .header-container { text-align: center; margin-top: 18vh; margin-bottom: 30px; position: relative; z-index: 5; width: 100%; }
        .main-header { font-family: var(--font-header); font-size: 3rem; margin-bottom: 5px; font-weight: 600; letter-spacing: 1px; background: linear-gradient(90deg, #FF6EC4, #7873F5, #4ADEDE, #FFCB57); background-size: 800% 800%; -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: gradientShift 15s ease infinite; display: inline-block; }
        @keyframes gradientShift { 0% { background-position: 0% 50%; } 50% { background-position: 100% 50%; } 100% { background-position: 0% 50%; } }
        .copilot-subtitle { font-size: 1.1rem; font-weight: 400; color: #6B7280; line-height: 1.6; margin-top: 0.5rem; margin-bottom: 1.5rem; }
        .search-area-container { display: flex; flex-direction: column; align-items: center; width: 100%; max-width: 700px; /* margin-top: 8vh; */ margin-bottom: auto; position: relative; animation: fadeInUp 2s ease-out 0.3s; animation-fill-mode: backwards; }

        .search-bar-card {
            width: 100%;
            background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card);
            padding: 8px;
            box-shadow: var(--shadow-soft-outset);
            border: 1px solid var(--color-shadow-outset-primary);
            position: relative;
            display: flex; 
            flex-direction: column; /* To stack inner-content and new suggestions */
            /* min-height: 58px; Removed to allow expansion */
            box-sizing: border-box;
            transition: box-shadow 0.3s ease, border-color 0.3s ease, min-height 0.3s ease;
            padding-bottom: 16px; /* Add some padding at the bottom if suggestions appear */
        }
        .search-bar-card:hover { box-shadow: var(--shadow-soft-outset-hover); }
        .search-bar-card::before { content: ''; position: absolute; top: -2px; left: -2px; right: -2px; bottom: -2px; background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-end)); z-index: -1; border-radius: calc(var(--border-radius-card) + 2px); opacity: 0; transition: opacity 0.3s ease; pointer-events: none; }
        .search-bar-card.focused-card::before { opacity: 1; }
        .search-bar-card.focused-card { border-color: transparent; }

        .search-bar-inner-content { display: flex; align-items: center; flex-grow: 1; background-color: var(--color-card-bg); border-radius: calc(var(--border-radius-card) - 10px); padding: 15px 8px 6px 8px; /* Increased top padding */ width: calc(100% - 0px); /* Make it take full width of its parent padding box */ box-sizing: border-box; }
        .search-input { flex-grow: 1; background: transparent; border: none; outline: none; color: var(--color-text); margin-left: 12px; font-size: 1rem; font-family: var(--font-body); font-weight: 400; }
        .search-input::placeholder { color: var(--color-placeholder); opacity: 1; font-weight: 400; }
        .search-actions { display: flex; align-items: center; gap: 10px; margin-left: 12px; }
        .action-icon { font-size: 1.1rem; color: var(--color-icon-gray); cursor: pointer; transition: color 0.2s ease, transform 0.2s ease; padding: 6px; border-radius: var(--border-radius-element); position: relative; }
        .action-icon:hover { color: var(--color-text); background-color: var(--color-hover-bg); transform: scale(1.05); }
        .action-icon:hover .icon-tooltip { opacity: 1; visibility: visible; transform: translateX(-50%) translateY(-4px); }

        .icon-tooltip, .tags-dropdown, .prompt-suggestions-list { background-color: var(--color-card-bg); border: 1px solid var(--color-shadow-outset-primary); box-shadow: var(--shadow-soft-outset); border-radius: var(--border-radius-element); color: var(--color-text); font-family: var(--font-body); z-index: 100; opacity: 0; visibility: hidden; transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease; }
        #hashtagBtn .icon-tooltip, #copyBtn .icon-tooltip, #sendBtn .icon-tooltip, #guidedCreateBtn .icon-tooltip { bottom: calc(100% + 16px); transform: translateX(-50%) translateY(-4px); }
        .icon-tooltip { position: absolute; bottom: calc(100% + 8px); left: 50%; transform: translateX(-50%) translateY(4px); font-size: 0.85rem; padding: 6px 10px; white-space: nowrap; }
        .icon-tooltip::after { content: ''; position: absolute; top: 100%; left: 50%; margin-left: -5px; border-width: 5px; border-style: solid; border-color: var(--color-shadow-outset-primary) transparent transparent transparent; transition: border-color 0.25s ease; }
        .action-icon:hover .icon-tooltip::after { border-top-color: var(--color-shadow-outset-primary); }

        .tags-dropdown, .prompt-suggestions-list { position: absolute; top: calc(100% + 16px); /* Increased spacing below the search bar */ left: 0; right: 0; padding: 8px; transform: translateY(8px); max-height: 280px; overflow-y: auto; }
        .tags-dropdown.visible, .prompt-suggestions-list.visible { opacity: 1; visibility: visible; transform: translateY(0); }
        .tags-dropdown ul { list-style: none; padding: 0; margin: 0; }
        .tags-dropdown li, .prompt-suggestion-item { padding: 8px 12px; font-size: 0.95rem; color: var(--color-text); cursor: pointer; border-radius: calc(var(--border-radius-element) - 2px); transition: background-color 0.2s ease, color 0.2s ease; display: flex; align-items: center; }
        .tags-dropdown li:hover, .prompt-suggestion-item:hover { background-color: var(--color-hover-bg); }
        .prompt-suggestion-item { margin-bottom: 4px; }
        .prompt-suggestion-item:last-child { margin-bottom: 0; }
        .prompt-suggestion-item .fa-check-circle { margin-right: 10px; color: var(--color-icon-gray); font-size: 0.95rem; }
        .prompt-suggestion-item strong { font-weight: 600; }

        /* NEW STYLES for Enhanced Prompts */
        .analyzing-indicator {
            text-align: center;
            padding: 15px 10px;
            font-size: 0.95rem;
            color: var(--color-icon-gray);
            border-top: 1px solid var(--color-shadow-outset-primary);
            margin-top: 8px; /* Space from search bar inner */
        }
        .analyzing-indicator .fa-spinner {
            margin-right: 8px;
            animation: spin 1.5s linear infinite;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        .enhanced-prompt-suggestions {
            padding: 0 8px; /* Align with inner-content padding */
            margin-top: 8px;
            max-height: 400px; /* Limit height and allow scroll if many */
            overflow-y: auto;
        }
        .enhanced-prompt-item {
            background-color: transparent; /* Match card bg */
            border: 1px solid transparent; /* Placeholder for potential hover border */
            border-radius: var(--border-radius-element);
            padding: 12px 15px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: background-color 0.2s ease, border-color 0.2s ease;
            border-bottom: 1px solid var(--color-shadow-outset-primary); /* Separator */
        }
        .enhanced-prompt-item:last-child {
            border-bottom: none;
            margin-bottom: 0;
        }
        .enhanced-prompt-item:hover {
            background-color: var(--color-hover-bg);
            /* border-color: var(--color-shadow-outset-secondary); */
        }
        .enhanced-prompt-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 5px;
        }
        .enhanced-prompt-item-header h5 {
            margin: 0;
            font-size: 1rem;
            font-weight: 500;
            color: var(--color-text);
            flex-grow: 1;
        }
        .enhanced-prompt-item-header .star-icon {
            font-size: 1.1rem;
            color: var(--color-icon-gray);
            transition: color 0.2s ease, transform 0.2s ease;
        }
        .enhanced-prompt-item-header .star-icon:hover {
            transform: scale(1.2);
        }
        .enhanced-prompt-item-header .star-icon.favorited {
            color: var(--color-star-active);
        }
        .enhanced-prompt-item-meta {
            font-size: 0.8rem;
            color: #4ade80; /* Tailwind green-400 for effective */
            margin-bottom: 6px;
            font-weight: 500;
        }
        .enhanced-prompt-item-meta .fa-history { /* Recently used icon */
            margin-right: 4px;
            color: var(--color-icon-gray);
        }
        .enhanced-prompt-item p.description {
            font-size: 0.85rem;
            color: #6B7280; /* gray-500 */
            line-height: 1.5;
            margin: 0;
        }
        .favorite-prompts-section {
            padding: 10px 15px;
            margin-top: 15px;
            border-top: 1px solid var(--color-shadow-outset-primary);
        }
        .favorite-prompts-section h4 {
            font-size: 0.9rem;
            font-weight: 600;
            color: var(--color-text);
            margin: 0 0 10px 0;
            display: flex;
            align-items: center;
        }
        .favorite-prompts-section h4 .fa-star {
            margin-right: 8px;
            color: var(--color-star-active);
        }
        .favorite-prompts-list .enhanced-prompt-item { /* Reuse style */
             padding: 10px 12px; /* Slightly smaller padding for fav list */
        }
        .favorite-prompts-list .enhanced-prompt-item:last-child {
            margin-bottom: 0;
        }


        /* Animations */
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-15px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px); } to { opacity: 1; transform: translateY(0); } }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .auth-buttons { top: 20px; right: 20px; gap: 10px; } .auth-btn { padding: 7px 15px; font-size: 0.85rem; }
            .header-container { margin-top: 15vh;} .main-header { font-size: 2.5rem; } .copilot-subtitle { font-size: 1rem; }
            .search-area-container { max-width: 90%; /* margin-top: 6vh; */ }
            .search-bar-card { /* min-height: 54px; */ border-radius: 14px;} .search-bar-inner-content { border-radius: 10px; padding: 5px 6px;}
            .search-input { font-size: 0.95rem; margin-left: 10px; } .search-actions { gap: 8px; margin-left: 10px; } .action-icon { font-size: 1.05rem; padding: 5px; }
            .tags-dropdown, .prompt-suggestions-list { border-radius: 10px; padding: 6px; } .tags-dropdown li, .prompt-suggestion-item { font-size: 0.9rem; padding: 7px 10px; } .icon-tooltip { font-size: 0.8rem; padding: 5px 8px; }
            .enhanced-prompt-item-header h5 {font-size: 0.95rem;} .enhanced-prompt-item p.description {font-size: 0.8rem;}
        }
        @media (max-width: 480px) {
             .all-hub-logo { top: 15px; left: 15px; font-size: 1.3rem; }
            .auth-buttons { position: static; width: 100%; justify-content: center; margin-top: 20px; margin-bottom: 25px; }
            .header-container { margin-top: 8vh;} .main-header { font-size: 2rem; } .copilot-subtitle { font-size: 0.9rem; }
            .search-area-container { /* margin-top: 4vh; */ }
            .search-bar-card { /* min-height: 50px; */ border-radius: 12px; padding: 6px; padding-bottom: 12px; } .search-bar-inner-content { border-radius: 8px; padding: 4px 5px;}
            .search-input { font-size: 0.9rem; margin-left: 8px;} .search-actions { gap: 6px; margin-left: 8px; } .action-icon { font-size: 1rem; padding: 4px; }
            .tags-dropdown, .prompt-suggestions-list { border-radius: 8px; padding: 5px;} .tags-dropdown li, .prompt-suggestion-item { font-size: 0.85rem; padding: 6px 8px; } .icon-tooltip { font-size: 0.75rem; padding: 4px 7px; }
        }
        .copyright-footer { text-align: center; margin-top: 40px; color: var(--color-text); opacity: 0.7; font-size: 0.85rem; width: 100%; padding-bottom: 20px; }
        @keyframes pulse { 0% { box-shadow: 0 0 3px 3px rgba(59, 130, 246, 0.5); } 70% { box-shadow: 0 0 8px 15px rgba(59, 130, 246, 0); } 100% { box-shadow: 0 0 0 0px rgba(59, 130, 246, 0); } }
    </style>
</head>
<body>
    <div class="all-hub-logo">allhub</div>
    <div class="auth-buttons">
        <a href="#" class="auth-btn btn-signin">Sign In</a>
        <a href="#" class="auth-btn btn-signup" style="animation: pulse 3s infinite;">Sign Up Free</a>
    </div>

    <div class="header-container">
        <h1 class="main-header">Prompt like a pro</h1>
        <p class="copilot-subtitle">Craft the perfect prompts with intelligent suggestions</p>
    </div>

    <div class="search-area-container">
        <div class="search-bar-card" id="searchBarCard">
            <div class="search-bar-inner-content" id="searchBarInnerContent">
                <i class="fas fa-brain action-icon" id="hashtagBtn"> 
                    <span class="icon-tooltip">Select Model</span>
                </i>
                <input type="text" class="search-input" id="searchInput" placeholder="Describe your prompt clearly..." autocomplete="off">
                <div class="search-actions">
                    <i class="fas fa-wand-magic-sparkles action-icon" id="guidedCreateBtn">
                        <span class="icon-tooltip">Guided Creation</span>
                    </i>
                    <i class="fas fa-copy action-icon" id="copyBtn">
                        <span class="icon-tooltip">Copy</span>
                    </i>
                    <i class="fas fa-paper-plane action-icon" id="sendBtn">
                        <span class="icon-tooltip">Send</span>
                    </i>
                </div>
            </div>

            <!-- NUEVO: Indicador de Análisis -->
            <div class="analyzing-indicator" id="analyzingIndicator" style="display: none;">
                <i class="fas fa-spinner fa-spin"></i> Analyzing your prompt...
            </div>

            <!-- NUEVO: Contenedor para las sugerencias de prompt mejoradas -->
            <div class="enhanced-prompt-suggestions" id="enhancedPromptSuggestionsContainer">
                <!-- JS llenará esto -->
            </div>

            <!-- NUEVO: Sección de Prompts Favoritos -->
            <div class="favorite-prompts-section" id="favoritePromptsSection" style="display: none;">
                <h4><i class="fas fa-star"></i> Prompts Populares</h4>
                <div class="favorite-prompts-list" id="favoritePromptsList">
                    <!-- JS llenará esto -->
                </div>
            </div>
        </div>

        <div class="tags-dropdown" id="tagsDropdownContainer">
            <ul>
                <li>Gemini 2.5 Pro</li>
                <li>Grok 3</li>
                <li>Claude 3.5 Sonnet</li>
                <li>GPT-4o</li>
                <li>DeepSeek R1</li>
            </ul>
        </div>

        <div class="prompt-suggestions-list" id="promptSuggestionsContainer">
            <!-- JS (original) poblará esto para autocompletado simple -->
        </div>
    </div>

    <p class="copyright-footer">Copyright © 2023 - 2025 All Hub. All rights reserved.</p>

    <script>
        const searchBarCard = document.getElementById('searchBarCard');
        const searchInput = document.getElementById('searchInput');
        const hashtagBtn = document.getElementById('hashtagBtn');
        const guidedCreateBtn = document.getElementById('guidedCreateBtn');
        const tagsDropdownContainer = document.getElementById('tagsDropdownContainer');
        const simplePromptSuggestionsContainer = document.getElementById('promptSuggestionsContainer'); // Renamed for clarity
        const originalPlaceholder = searchInput.placeholder;

        // NEW Elements
        const analyzingIndicator = document.getElementById('analyzingIndicator');
        const enhancedPromptSuggestionsContainer = document.getElementById('enhancedPromptSuggestionsContainer');
        const favoritePromptsSection = document.getElementById('favoritePromptsSection');
        const favoritePromptsList = document.getElementById('favoritePromptsList');

        // --- Debounce Function ---
        function debounce(func, delay) {
            let timeout;
            return function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
        }

        // --- Favorite Prompts Logic ---
        let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHub')) || [];

        function renderFavoritePrompts() {
            favoritePromptsList.innerHTML = '';
            if (favoritePrompts.length > 0) {
                favoritePrompts.forEach(promptData => {
                    const item = createEnhancedPromptSuggestionItem(promptData, true); // true for isFavoriteContext
                    favoritePromptsList.appendChild(item);
                });
                favoritePromptsSection.style.display = 'block';
            } else {
                favoritePromptsSection.style.display = 'none';
            }
        }

        function toggleFavorite(promptData, starIconElement) {
            const promptId = promptData.id || promptData.title; // Use a unique ID
            const index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptId);

            if (index > -1) { // Already a favorite, remove it
                favoritePrompts.splice(index, 1);
                starIconElement.classList.remove('favorited');
                starIconElement.classList.replace('fas', 'far'); // Empty star
            } else { // Not a favorite, add it
                favoritePrompts.push({...promptData, id: promptId }); // Ensure it has an id
                starIconElement.classList.add('favorited');
                starIconElement.classList.replace('far', 'fas'); // Solid star
            }
            localStorage.setItem('favoritePromptsAllHub', JSON.stringify(favoritePrompts));
            renderFavoritePrompts(); // Re-render favorites list
             // Also update the star in the main suggestions list if it exists
            const mainSuggestionStar = document.querySelector(`.enhanced-prompt-item[data-prompt-id="${promptId}"] .star-icon`);
            if (mainSuggestionStar) {
                if (index > -1) { // Was removed
                     mainSuggestionStar.classList.remove('favorited');
                     mainSuggestionStar.classList.replace('fas', 'far');
                } else { // Was added
                     mainSuggestionStar.classList.add('favorited');
                     mainSuggestionStar.classList.replace('far', 'fas');
                }
            }
        }


        // --- Enhanced Prompt Suggestion Logic ---
        function createEnhancedPromptSuggestionItem(promptData, isFavoriteContext = false) {
            const item = document.createElement('div');
            item.classList.add('enhanced-prompt-item');
            const promptId = promptData.id || promptData.title;
            item.dataset.promptId = promptId;


            const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === promptId);
            const starClass = isFavorited ? 'fas fa-star star-icon favorited' : 'far fa-star star-icon';

            item.innerHTML = `
                <div class="enhanced-prompt-item-header">
                    <h5>${promptData.title}</h5>
                    <i class="${starClass}" title="Mark as favorite"></i>
                </div>
                ${promptData.meta ? `<div class="enhanced-prompt-item-meta">${promptData.meta}</div>` : ''}
                <p class="description">${promptData.description}</p>
            `;

            item.addEventListener('click', (e) => {
                if (!e.target.classList.contains('star-icon')) { // Don't fill input if star is clicked
                    searchInput.value = promptData.fullPrompt || promptData.title; // Use fullPrompt if available
                    enhancedPromptSuggestionsContainer.innerHTML = ''; // Clear suggestions
                    analyzingIndicator.style.display = 'none';
                    searchInput.focus();
                }
            });

            const starIcon = item.querySelector('.star-icon');
            starIcon.addEventListener('click', (e) => {
                e.stopPropagation(); // Prevent item click event
                toggleFavorite(promptData, starIcon);
            });
            return item;
        }
        
        // SIMULATED PROMPT GENERATION
        function generateEnhancedPrompts(userInput) {
            const techniques = [
                {
                    name: "Zero-Shot",
                    apply: (input) => `Crea ${input}`,
                    desc: "Directo y al grano. Bueno para tareas simples donde el modelo ya tiene contexto."
                },
                {
                    name: "Role Prompting",
                    apply: (input) => `Actúa como un experto en ${input.split(' ')[0] || 'el tema'}. Tu tarea es: ${input}. Proporciona una respuesta detallada y bien estructurada.`,
                    desc: "Define un rol para el AI, lo que puede mejorar la calidad y el enfoque de la respuesta."
                },
                {
                    name: "Few-Shot (Ejemplo)",
                    apply: (input) => `Aquí tienes un ejemplo de cómo abordar una tarea similar:\nEjemplo: Para "crear un logo para una cafetería", se podría sugerir "Diseña un logo minimalista con un grano de café estilizado y colores tierra."\n\nAhora, para tu solicitud: "${input}", genera una propuesta.`,
                    desc: "Proporciona ejemplos al modelo para guiarlo hacia el formato o tipo de respuesta deseado."
                },
                {
                    name: "Chain of Thought (CoT)",
                    apply: (input) => `Para la tarea "${input}", primero piensa en los pasos necesarios para completarla y luego genera la respuesta final. Desglosa tu razonamiento.`,
                    desc: "Pide al modelo que piense paso a paso, útil para problemas complejos o de razonamiento."
                }
            ];

            let suggestions = [];
            if (!userInput.trim()) return suggestions;

            techniques.forEach(tech => {
                // A simple way to make titles somewhat relevant
                let titlePrefix = tech.name;
                if (userInput.toLowerCase().includes("react")) titlePrefix = "Componente React con " + tech.name;
                else if (userInput.toLowerCase().includes("diseño")) titlePrefix = "Sistema de Diseño usando " + tech.name;
                else if (userInput.toLowerCase().includes("precio")) titlePrefix = "Estrategia de Precios (via " + tech.name + ")";


                suggestions.push({
                    title: `${titlePrefix} para "${userInput.substring(0, 20)}${userInput.length > 20 ? '...' : ''}"`,
                    fullPrompt: tech.apply(userInput),
                    meta: `<i class="fas fa-chart-line"></i> ${Math.floor(85 + Math.random() * 14)}% efectivo`, // Random effectiveness
                    description: tech.desc,
                    id: `${tech.name}-${userInput.substring(0,10)}` // Basic unique ID
                });
            });
            return suggestions.slice(0, 3); // Show 3 suggestions
        }


        const handleSearchInput = debounce(async function() {
            const inputText = searchInput.value;
            enhancedPromptSuggestionsContainer.innerHTML = ''; // Clear previous enhanced suggestions

            if (inputText.length < 3) { // Only trigger analysis for reasonably long input
                analyzingIndicator.style.display = 'none';
                if (inputText.length === 0) { // If input is cleared, also hide favorites if they were shown due to focus
                     renderFavoritePrompts(); // This will hide if empty, or show if populated
                }
                return;
            }

            analyzingIndicator.style.display = 'block';
            // searchBarCard.style.minHeight = 'auto'; // Allow card to expand

            // Simulate API call / heavy processing
            await new Promise(resolve => setTimeout(resolve, 1200)); 

            const suggestions = generateEnhancedPrompts(inputText);

            if (suggestions.length > 0) {
                suggestions.forEach(suggData => {
                    const item = createEnhancedPromptSuggestionItem(suggData);
                    enhancedPromptSuggestionsContainer.appendChild(item);
                });
            }
            
            analyzingIndicator.style.display = 'none';
            renderFavoritePrompts(); // Show favorites below suggestions
        }, 750); // Debounce time in ms

        searchInput.addEventListener('input', handleSearchInput);


        // --- Existing Focus/Blur and Popup Logic (adapted slightly) ---
        searchInput.addEventListener('focus', () => {
            searchBarCard.classList.add('focused-card');
            searchInput.placeholder = 'Type your prompt here...';
            
            // Show simple suggestions if input has value AND simple suggestions are available AND tags dropdown is not open
            if (searchInput.value.length > 0 && simplePromptSuggestionsContainer.children.length > 0 && !tagsDropdownContainer.classList.contains('visible')) {
                 const event = new Event('input', { bubbles: true }); 
                 searchInput.dispatchEvent(event); // This is for the simple suggestions, not the enhanced ones
            } else if (searchInput.value.length === 0) {
                simplePromptSuggestionsContainer.innerHTML = '';
                simplePromptSuggestionsContainer.classList.remove('visible');
                enhancedPromptSuggestionsContainer.innerHTML = ''; // Clear enhanced as well
                analyzingIndicator.style.display = 'none';
                renderFavoritePrompts(); // Show favorites if any, even on empty focus
            }
            // If there's text, trigger enhanced suggestions analysis immediately on focus
            if (searchInput.value.length >=3) {
                handleSearchInput();
            }
        });

        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                const activeElement = document.activeElement;
                const isFocusWithinSearchArea = searchBarCard.contains(activeElement) ||
                                                 tagsDropdownContainer.contains(activeElement) ||
                                                 simplePromptSuggestionsContainer.contains(activeElement) ||
                                                 enhancedPromptSuggestionsContainer.contains(activeElement) || // Check new container
                                                 activeElement.closest('.action-icon');

                if (!isFocusWithinSearchArea) {
                    searchBarCard.classList.remove('focused-card');
                    if (searchInput.value === '') {
                        searchInput.placeholder = originalPlaceholder;
                        enhancedPromptSuggestionsContainer.innerHTML = ''; // Clear enhanced suggestions
                        analyzingIndicator.style.display = 'none';
                        // searchBarCard.style.minHeight = '58px'; // Reset height
                    }
                    tagsDropdownContainer.classList.remove('visible');
                    simplePromptSuggestionsContainer.classList.remove('visible');
                    // Don't hide enhanced suggestions on blur if they are populated, user might want to click them
                }
            }, 150); 
        });

        function togglePopup(popupElement) {
            const isOpening = !popupElement.classList.contains('visible');
            if (popupElement !== tagsDropdownContainer) tagsDropdownContainer.classList.remove('visible');
            if (popupElement !== simplePromptSuggestionsContainer) simplePromptSuggestionsContainer.classList.remove('visible');

            if (isOpening) {
                popupElement.classList.add('visible');
                searchBarCard.classList.add('focused-card'); 
            } else {
                popupElement.classList.remove('visible');
            }
        }

        hashtagBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            togglePopup(tagsDropdownContainer);
            enhancedPromptSuggestionsContainer.innerHTML = ''; // Hide enhanced when opening tags
            analyzingIndicator.style.display = 'none';
            searchInput.focus(); 
        });

        guidedCreateBtn.addEventListener('click', (event) => {
            event.stopPropagation();
            alert('Guided Creation Flow Activated! (Placeholder)');
            // Here you would typically show a modal or navigate to a different UI section
            searchInput.focus();
        });
        
        document.getElementById('copyBtn').addEventListener('click', (event) => {
            event.stopPropagation(); /* ... (existing copy logic) ... */ 
            const textToCopy = searchInput.value;
            const tooltip = event.currentTarget.querySelector('.icon-tooltip');
            const originalTooltipText = tooltip.textContent;
            if (textToCopy) { navigator.clipboard.writeText(textToCopy).then(() => { tooltip.textContent = 'Copied!'; setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500); }).catch(err => { console.error('Failed to copy: ', err); tooltip.textContent = 'Copy Failed!'; setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500); }); } else { tooltip.textContent = 'Nothing to copy!'; setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500); }
            searchInput.focus();
        });

        document.getElementById('sendBtn').addEventListener('click', (event) => {
            event.stopPropagation(); /* ... (existing send logic) ... */ 
            const textToSend = searchInput.value;
            const tooltip = event.currentTarget.querySelector('.icon-tooltip');
            const originalTooltipText = tooltip.textContent;
            if (textToSend) { tooltip.textContent = 'Sending...'; setTimeout(() => { alert('Prompt sent: ' + textToSend); tooltip.textContent = 'Sent!'; setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500); }, 500); } else { tooltip.textContent = 'Prompt is empty!'; setTimeout(() => { tooltip.textContent = originalTooltipText; }, 1500); }
            searchInput.focus();
        });

         tagsDropdownContainer.querySelectorAll('li').forEach(item => {
             item.addEventListener('mousedown', (e) => { 
                 e.preventDefault(); /* ... (existing tags logic) ... */ 
                 const selectedModel = item.textContent; console.log("Selected model:", selectedModel); 
                 tagsDropdownContainer.classList.remove('visible');
                 searchInput.focus();
             });
         });

        // --- Original Simple Prompt Suggestions (Kept for reference/coexistence) ---
        const basePromptSuggestions = { /* ... (existing simple suggestions data) ... */ 
            "redacta": ["redacta un correo electrónico formal", "redacta un ensayo sobre", "redacta una descripción de producto para", "redacta un post para redes sociales sobre"], "escribe": ["escribe un poema corto sobre", "escribe un guion para un video de", "escribe una historia de ficción acerca de", "escribe los pros y contras de"], "genera": ["genera ideas para un startup de", "genera un plan de contenidos para", "genera código Python para", "genera una lista de preguntas para una entrevista sobre"], "resume": ["resume este texto en 3 puntos clave", "resume el siguiente artículo", "resume las ideas principales de"], "traduce": ["traduce este texto a inglés", "traduce esta frase a francés", "traduce al español:"]
        };

        searchInput.addEventListener('input', function() { // Original listener for simple suggestions
            const inputText = this.value.toLowerCase().trim();
            // Only show simple suggestions if enhanced are not active or input is very short
            if (enhancedPromptSuggestionsContainer.children.length > 0 || inputText.length >= 3) {
                simplePromptSuggestionsContainer.innerHTML = '';
                simplePromptSuggestionsContainer.classList.remove('visible');
                return;
            }
            
            simplePromptSuggestionsContainer.innerHTML = ''; 
            if (inputText.length > 0) {
                let matchedSuggestions = []; /* ... (existing simple suggestions filtering logic) ... */ 
                const inputWords = inputText.split(" "); const firstInputWord = inputWords[0];
                if (basePromptSuggestions[firstInputWord]) { basePromptSuggestions[firstInputWord].forEach(sugg => { if (sugg.toLowerCase().startsWith(inputText) && !matchedSuggestions.some(ms => ms.text === sugg)) { matchedSuggestions.push({text: sugg, score: 2}); } }); }
                Object.values(basePromptSuggestions).flat().forEach(sugg => { if (sugg.toLowerCase().startsWith(inputText) && !matchedSuggestions.some(ms => ms.text === sugg)) { matchedSuggestions.push({text: sugg, score: 1}); } });
                matchedSuggestions.sort((a, b) => b.score - a.score || a.text.localeCompare(b.text)); const filteredSuggestions = matchedSuggestions.map(s => s.text).slice(0, 5);

                if (filteredSuggestions.length > 0 && !tagsDropdownContainer.classList.contains('visible')) {
                    filteredSuggestions.forEach(suggText => { /* ... (existing simple suggestions rendering logic) ... */ 
                        const item = document.createElement('div'); item.classList.add('prompt-suggestion-item'); const matchIndex = suggText.toLowerCase().indexOf(inputText); let displayHTML = `<i class="fas fa-check-circle"></i><span>`; if (matchIndex === 0) { displayHTML += `<strong>${suggText.substring(0, inputText.length)}</strong>${suggText.substring(inputText.length)}`; } else { displayHTML += suggText; } displayHTML += `</span>`; item.innerHTML = displayHTML;
                        item.addEventListener('mousedown', (e) => { e.preventDefault(); searchInput.value = suggText; simplePromptSuggestionsContainer.classList.remove('visible'); searchInput.focus(); });
                        simplePromptSuggestionsContainer.appendChild(item);
                    });
                    simplePromptSuggestionsContainer.classList.add('visible');
                } else { simplePromptSuggestionsContainer.classList.remove('visible'); }
            } else { simplePromptSuggestionsContainer.classList.remove('visible'); }
        });

        // --- Global Click Listener (adapted) ---
        document.addEventListener('click', (event) => {
            const clickedElement = event.target;
            const isSearchRelated = searchBarCard.contains(clickedElement) ||
                                    tagsDropdownContainer.contains(clickedElement) ||
                                    simplePromptSuggestionsContainer.contains(clickedElement) ||
                                    enhancedPromptSuggestionsContainer.contains(clickedElement) || // Check new container
                                    clickedElement.closest('.action-icon'); 

            if (!isSearchRelated) {
                tagsDropdownContainer.classList.remove('visible');
                simplePromptSuggestionsContainer.classList.remove('visible');
                // Don't hide enhanced on global click if they have content, let user interact
                // if (enhancedPromptSuggestionsContainer.children.length === 0) {
                //    enhancedPromptSuggestionsContainer.innerHTML = ''; // Should be empty already
                // }
                if(document.activeElement !== searchInput) {
                    searchBarCard.classList.remove('focused-card');
                     if (searchInput.value === '') {
                        searchInput.placeholder = originalPlaceholder;
                        enhancedPromptSuggestionsContainer.innerHTML = ''; // Clear if input is empty and focus lost
                        analyzingIndicator.style.display = 'none';
                        // searchBarCard.style.minHeight = '58px';
                     }
                }
            }
        });
        
        [tagsDropdownContainer, simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer].forEach(el => {
            el.addEventListener('mousedown', e => e.preventDefault());
        });

        // Initial render of favorites
        renderFavoritePrompts();

    </script>
    <!-- The canvas and its script from your original code are removed as they are not directly related to the prompt functionality -->
</body>
</html>