<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;
            --color-bg: #F3F4F6;
            --color-card-bg: #FFFFFF;
            --color-text: #111827;
            --color-cta: #3B82F6;
            --color-cta-text: #FFFFFF;
            --color-shadow-outset-primary: #E5E7EB;
            --color-shadow-outset-secondary: #D1D5DB;
            --shadow-soft-outset: 0 6px 15px -4px rgba(0, 0, 0, 0.07), 0 3px 10px -5px rgba(0, 0, 0, 0.05);
            --shadow-soft-outset-hover: 0 12px 25px -6px rgba(0, 0, 0, 0.07), 0 5px 12px -5px rgba(0, 0, 0, 0.05);
            --color-accent-start: #FF6EC4; 
            --color-accent-middle: #7873F5;
            --color-accent-end: #4ADEDE;
            --color-icon-gray: #9CA3AF;
            --color-placeholder: #9CA3AF;
            --color-hover-bg: #F9FAFB;
            --border-radius-card: 16px;
            --border-radius-pill: 9999px;
            --border-radius-element: 8px;
            --color-star-active: #FFD700;
        }

        body {
            margin: 0;
            font-family: var(--font-body);
            background-color: var(--color-bg);
            min-height: 100vh;
            display: flex;
            flex-direction: column;
            align-items: center;
            padding: 20px;
            box-sizing: border-box;
            position: relative;
            color: var(--color-text);
            background-image: linear-gradient(to bottom left, #ffffff, lch(91.25% 13.31 276.59));
        }
        
        .all-hub-logo { position: absolute; top: 25px; left: 30px; font-family: var(--font-header); font-size: 1.5rem; font-weight: 700; color: #023c99; z-index: 10; }
        .auth-buttons { position: absolute; top: 25px; right: 30px; display: flex; gap: 15px; z-index: 10; }
        .auth-btn { padding: 8px 18px; border-radius: var(--border-radius-pill); font-size: 0.9rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; line-height: 1.5; }
        .btn-signin { background-color: transparent; border: 1px solid var(--color-shadow-outset-secondary); color: var(--color-text); }
        .btn-signin:hover { background-color: rgba(17, 24, 39, 0.03); border-color: var(--color-text); }
        .btn-signup { background: linear-gradient(to right, #60A5FA, #01378d); color: var(--color-cta-text); border: none; box-shadow: var(--shadow-soft-outset); }
        .btn-signup:hover { transform: translateY(-2px); box-shadow: 0 0 15px 0px rgba(59, 130, 246, 0.6), var(--shadow-soft-outset-hover); }
        .header-container { text-align: center; margin-top: 15vh; margin-bottom: 30px; position: relative; z-index: 5; width: 100%; }
        .main-header { font-family: var(--font-header); font-size: 3rem; margin-bottom: 5px; font-weight: 600; letter-spacing: 1px; background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end)); background-size: 300% 300%; -webkit-background-clip: text; -webkit-text-fill-color: transparent; animation: gradientShift 10s ease infinite alternate; display: inline-block; }
        @keyframes gradientShift { 0% { background-position: 0% 50%; } 100% { background-position: 100% 50%; } }
        .copilot-subtitle { font-size: 1.1rem; font-weight: 400; color: #6B7280; line-height: 1.6; margin-top: 0.5rem; margin-bottom: 1.5rem; }
        .search-area-container { display: flex; flex-direction: column; align-items: center; width: 100%; max-width: 700px; margin-bottom: auto; position: relative; animation: fadeInUp 1s ease-out 0.2s; animation-fill-mode: backwards; }

        .search-bar-card {
            width: 100%;
            background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card);
            padding: 0; 
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 6px 15px rgba(0,0,0,0.07);
            border: none; /* Borde manejado por pseudo-elementos */
            position: relative;
            display: flex; 
            flex-direction: column; 
            box-sizing: border-box;
            overflow: visible; /* Para que el resplandor del borde no se corte */
            transition: box-shadow 0.3s ease;
        }
        /* Borde Base Fino Degradado */
        .search-bar-card::before { 
            content: ''; 
            position: absolute; 
            top: 0; left: 0; right: 0; bottom: 0; 
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end));
            background-size: 200% 200%; 
            z-index: -2; 
            border-radius: var(--border-radius-card); 
            padding: 1.5px; 
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; 
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.4s ease;
        }
        /* Borde Animado Grueso (el que se mueve) */
        .search-bar-card::after {
            content: ""; position: absolute; z-index: -1; 
            top: -3px; left: -3px; right: -3px; bottom: -3px; 
            border-radius: calc(var(--border-radius-card) + 3px);
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 400% 400%;
            filter: blur(6px); 
            opacity: 0;
            animation: neonShinePath 6s linear infinite paused; 
            transition: opacity 0.4s ease;
        }
        .search-bar-card.focused-card::before,
        .search-bar-card.always-focused::before { 
            opacity: 0.7; 
        }
        .search-bar-card.focused-card::after,
        .search-bar-card.always-focused::after { 
            opacity: 0.9; 
            animation-play-state: running; 
        }
        @keyframes neonShinePath {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }
        
        .search-input { 
            width: 100%; padding: 18px 20px; background: transparent; border: none; 
            outline: none; color: var(--color-text); font-size: 1rem;
            font-family: var(--font-body); font-weight: 400;
            resize: none; overflow-y: hidden; line-height: 1.6; 
            min-height: 70px; box-sizing: border-box;
        }
        .search-input::placeholder { color: var(--color-placeholder); opacity: 1; font-weight: 400; }

        .toolbar-bottom {
            display: flex; justify-content: space-between; align-items: center;
            padding: 10px 16px; background-color: var(--color-card-bg);
        }

        .model-selector-container { position: relative; }
        .model-selector-btn {
            position: relative; background-color: var(--color-hover-bg); border: none;
            color: var(--color-text); padding: 8px 15px; border-radius: var(--border-radius-element);
            cursor: pointer; font-size: 0.85rem; font-weight: 500;
            display: flex; align-items: center; gap: 8px;
            transition: box-shadow 0.2s ease; z-index: 1;
        }
        .model-selector-btn::before {
            content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            border-radius: var(--border-radius-element); padding: 1.5px; 
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-end));
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: subtract; mask-composite: subtract;
            z-index: -1; opacity: 0.8; transition: opacity 0.3s ease;
        }
        .model-selector-btn:hover::before { opacity: 1; }
        .model-selector-btn:hover { box-shadow: 0 0 8px rgba(var(--color-cta), 0.3); }
        .model-selector-btn #currentModelName {
            max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap;
        }
        .model-selector-btn .fa-chevron-down { font-size: 0.75em; transition: transform 0.2s ease; }
        .model-selector-btn.open .fa-chevron-down { transform: rotate(180deg); }

        .model-dropdown {
            position: absolute; bottom: calc(100% + 8px); left: 0;
            width: max-content; min-width: 220px; background-color: var(--color-card-bg);
            border: 1px solid var(--color-shadow-outset-primary); box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: var(--border-radius-element); z-index: 110; 
            opacity: 0; visibility: hidden; transform: translateY(10px);
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
        }
        .model-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0); }
        .model-dropdown ul { list-style: none; padding: 5px; margin: 0; max-height: 200px; overflow-y: auto;}
        .model-dropdown li { padding: 8px 12px; font-size: 0.85rem; color: var(--color-text); cursor: pointer; border-radius: 6px; transition: background-color 0.2s ease; }
        .model-dropdown li:hover { background-color: var(--color-hover-bg); }
        .model-dropdown li.selected-model-item { background-color: var(--color-cta); color: var(--color-cta-text); font-weight: 500; }

        .search-actions { display: flex; align-items: center; gap: 12px; }
        .action-icon { font-size: 1.2rem; color: var(--color-icon-gray); cursor: pointer; transition: color 0.2s ease, transform 0.2s ease; padding: 8px; border-radius: 50%; }
        .action-icon:hover { color: var(--color-cta); background-color: var(--color-hover-bg); transform: scale(1.1); }
        
        .dynamic-content-area { padding: 0 16px 16px 16px; }
        .analyzing-indicator {
            text-align: center; padding: 20px 10px; font-size: 1rem; 
            color: var(--color-icon-gray); margin-top: 10px; 
        }
        .analyzing-indicator .fa-spinner { margin-right: 10px; font-size: 1.2em; animation: spin 1.5s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        .simple-suggestions-container {
            background-color: var(--color-card-bg); 
            z-index: 99; max-height: 280px; overflow-y: auto;
            margin-top: 10px; border-radius: var(--border-radius-element);
            border: 1px solid var(--color-shadow-outset-primary);
        }
        .simple-suggestion-item {
            padding: 10px 15px; font-size: 0.9rem; color: var(--color-text);
            cursor: pointer; border-bottom: 1px solid var(--color-shadow-outset-primary);
            transition: background-color 0.2s ease;
        }
        .simple-suggestion-item:last-child { border-bottom: none; }
        .simple-suggestion-item:hover { background-color: var(--color-hover-bg); }
        .simple-suggestion-item strong { font-weight: 600; }

        .enhanced-prompt-suggestions { margin-top: 10px; }
        .enhanced-prompt-item { 
            background-color: #fdfdff; border: 1px solid #e0e7ff;
            border-radius: var(--border-radius-element); padding: 18px 22px; 
            margin-bottom: 20px; box-shadow: 0 2px 8px rgba(100,116,139,0.08);
        }
        .enhanced-prompt-item:last-child { margin-bottom: 0; }
        .enhanced-prompt-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .enhanced-prompt-item-header h5 { margin: 0; font-size: 1.1rem; font-weight: 600; color: var(--color-text); flex-grow: 1; padding-right: 10px; }
        .enhanced-prompt-item-header .star-icon { font-size: 1.1rem; color: var(--color-icon-gray); transition: color 0.2s ease, transform 0.2s ease; flex-shrink: 0; cursor:pointer;}
        .enhanced-prompt-item-header .star-icon:hover { transform: scale(1.2); }
        .enhanced-prompt-item-header .star-icon.favorited { color: var(--color-star-active); }
        .enhanced-prompt-item-meta-minimal {
            display: flex; flex-wrap: wrap; gap: 15px; font-size: 0.8rem;
            color: #4B5563; margin-bottom: 15px; padding: 10px;
            background-color: #f0f4f8; border-radius: 6px;
        }
        .meta-item { display: flex; align-items: center; gap: 6px; }
        .meta-item i { color: var(--color-cta); font-size: 0.95em; }
        .full-prompt-display { 
            font-size: 0.9rem; line-height: 1.65; color: #374151;
            white-space: pre-wrap; word-break: break-word; 
            margin-top: 0; margin-bottom: 15px; 
        }
        .full-prompt-display .prompt-heading { font-weight: 600; color: #1e40af; margin-top: 0.8em; margin-bottom: 0.4em; display: block; }
        .full-prompt-display br + .prompt-heading { margin-top: 1.2em; }
        .full-prompt-display br { content: ""; display: block; margin-bottom: 0.3em; }
        .full-prompt-display br + br { margin-bottom: 0.75em; }
        .select-prompt-button { 
            display: inline-block; background-color: var(--color-cta); color: var(--color-cta-text);
            padding: 9px 18px; border-radius: var(--border-radius-element);
            font-size: 0.9rem; font-weight: 500; cursor: pointer;
            border: none; transition: background-color 0.2s ease;
        }
        .select-prompt-button:hover { background-color: #2563EB; }
        .select-prompt-button i { margin-right: 7px; }


        .favorite-prompts-section h4 { font-size: 0.9rem; font-weight: 600; color: var(--color-text); margin: 0 0 10px 0; display: flex; align-items: center; }
        .favorite-prompts-section h4 .fa-star { margin-right: 8px; color: var(--color-star-active); }
        .favorite-prompts-list .enhanced-prompt-item { padding: 10px 12px; margin-bottom:12px; }
        .favorite-prompts-list .enhanced-prompt-item:last-child { margin-bottom: 0; }

        .hidden { display: none !important; }
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-15px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px); } to { opacity: 1; transform: translateY(0); } }
        @media (max-width: 768px) { .header-container { margin-top: 10vh;} .main-header { font-size: 2.5rem; } .search-area-container { max-width: 95%; } .toolbar-bottom { flex-wrap: wrap; gap: 10px; padding: 8px;} .model-selector-btn { flex-grow: 1; justify-content: center;} .search-actions { flex-grow: 1; justify-content: flex-end;} }
        .copyright-footer { text-align: center; margin-top: 40px; color: var(--color-text); opacity: 0.7; font-size: 0.85rem; width: 100%; padding-bottom: 20px; }
    </style>
</head>
<body>
    <div class="all-hub-logo">allhub</div>
    <div class="auth-buttons">
        <a href="#" class="auth-btn btn-signin">Sign In</a>
        <a href="#" class="auth-btn btn-signup" style="animation: pulse 3s infinite;">Sign Up Free</a>
    </div>

    <div class="header-container">
        <h1 class="main-header">Prompt like a pro</h1>
        <p class="copilot-subtitle">Craft the perfect prompts with intelligent suggestions</p>
    </div>

    <div class="search-area-container">
        <div class="search-bar-card" id="searchBarCard">
            <textarea class="search-input" id="searchInput" placeholder="Describe your prompt clearly..." rows="1"></textarea>

            <div class="toolbar-bottom">
                <div class="model-selector-container">
                    <button class="model-selector-btn" id="modelSelectorBtn">
                        <span id="currentModelName">Gemini 2.5 Pro</span> <i class="fas fa-chevron-down"></i>
                    </button>
                    <div class="model-dropdown hidden" id="modelDropdownList">
                        <ul>
                            <li data-model-name="Gemini 2.5 Pro Preview 05-06">Gemini 2.5 Pro Preview 05-06</li>
                            <li data-model-name="GPT-4.5 Orion">GPT-4.5 Orion</li>
                            <li data-model-name="Claude 3.7 Sonnet">Claude 3.7 Sonnet</li>
                            <li data-model-name="Grok 3">Grok 3</li>
                            <li data-model-name="DeepSeek R1">DeepSeek R1</li>
                            <li data-model-name="Qwen2.5-Max">Qwen2.5-Max</li>
                        </ul>
                    </div>
                </div>

                <div class="search-actions">
                    <i class="fas fa-wand-magic-sparkles action-icon" id="guidedCreateBtn" title="Guided Creation"></i>
                    <i class="fas fa-copy action-icon" id="copyBtn" title="Copy Prompt"></i>
                    <i class="fas fa-paper-plane action-icon" id="sendBtn" title="Send Prompt"></i>
                </div>
            </div>
            
            <div class="dynamic-content-area">
                <div class="analyzing-indicator hidden" id="analyzingIndicator">
                    <i class="fas fa-spinner fa-spin"></i> Analyzing your prompt...
                </div>
                <div class="simple-suggestions-container hidden" id="simplePromptSuggestionsContainer"></div>
                <div class="enhanced-prompt-suggestions" id="enhancedPromptSuggestionsContainer"></div>
                 <div class="favorite-prompts-section hidden" id="favoritePromptsSection">
                    <h4><i class="fas fa-star"></i> Prompts Populares</h4>
                    <div class="favorite-prompts-list" id="favoritePromptsList"></div>
                </div>
            </div>
        </div>
    </div>

    <p class="copyright-footer">Copyright © 2023 - 2025 All Hub. All rights reserved.</p>

    <script>
        const searchBarCard = document.getElementById('searchBarCard');
        const searchInput = document.getElementById('searchInput'); 
        const guidedCreateBtn = document.getElementById('guidedCreateBtn');
        const simplePromptSuggestionsContainer = document.getElementById('simplePromptSuggestionsContainer');
        const originalPlaceholder = searchInput.placeholder;
        const analyzingIndicator = document.getElementById('analyzingIndicator');
        const enhancedPromptSuggestionsContainer = document.getElementById('enhancedPromptSuggestionsContainer');
        const favoritePromptsSection = document.getElementById('favoritePromptsSection');
        const favoritePromptsList = document.getElementById('favoritePromptsList');

        const modelSelectorBtn = document.getElementById('modelSelectorBtn');
        const currentModelNameSpan = document.getElementById('currentModelName');
        const modelDropdownList = document.getElementById('modelDropdownList');

        let isGuidedModeActive = false;
        let currentGuidedSuggestions = [];

        function debounce(func, delay) {
            let timeout;
            const debounced = function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
            debounced.cancel = function() { clearTimeout(timeout); };
            return debounced;
        }

        let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHub')) || [];

        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto'; 
            let scrollHeight = textarea.scrollHeight;
            textarea.style.height = scrollHeight + 'px';
            textarea.style.overflowY = 'hidden'; 
            // Esto podría causar un bucle si el card también se ajusta, manejar con cuidado
            // searchBarCard.style.height = 'auto'; 
            // searchBarCard.style.height = searchBarCard.scrollHeight + 'px';
        }

        function renderFavoritePrompts() {
            favoritePromptsList.innerHTML = '';
            if (isGuidedModeActive) {
                favoritePromptsSection.classList.add('hidden'); return;
            }
            if (favoritePrompts.length > 0 && searchInput.value.length === 0 && simplePromptSuggestionsContainer.children.length === 0 && enhancedPromptSuggestionsContainer.children.length === 0) {
                favoritePrompts.forEach(promptData => {
                    const item = createPromptCard(promptData, false, true);
                    favoritePromptsList.appendChild(item);
                });
                favoritePromptsSection.classList.remove('hidden');
            } else {
                favoritePromptsSection.classList.add('hidden');
            }
        }
        
        function formatFullPromptForDisplay(fullPromptText) {
            if (!fullPromptText) return "";
            let html = fullPromptText;
            html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim,
                (match) => `<span class="prompt-heading">${match.trim()}</span>`);
            return html;
        }

        function createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) {
            const card = document.createElement('div');
            card.classList.add('enhanced-prompt-item');
            if (isFavoriteListItem) card.classList.add('favorite-list-item-card');
            card.dataset.promptId = promptData.id;

            const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === promptData.id);
            const starClass = isFavorited ? 'fas fa-star star-icon favorited' : 'far fa-star star-icon';

            let metaMinimalHTML = '';
            if (promptData.metaMinimal) {
                metaMinimalHTML = '<div class="enhanced-prompt-item-meta-minimal">';
                if (promptData.metaMinimal.inputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input Tokens: ${promptData.metaMinimal.inputTokens}</span>`;
                if (promptData.metaMinimal.outputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output Tokens: ${promptData.metaMinimal.outputTokens}</span>`;
                if (promptData.metaMinimal.time) metaMinimalHTML += `<span class="meta-item"><i class="far fa-clock"></i> Time: ${promptData.metaMinimal.time}</span>`;
                if (promptData.metaMinimal.reuse) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-redo-alt"></i> Reuse: ${promptData.metaMinimal.reuse}</span>`;
                metaMinimalHTML += '</div>';
            }
            
            card.innerHTML = `
                <div class="enhanced-prompt-item-header">
                    <h5>${promptData.title || "Prompt"}</h5>
                    <i class="${starClass}" title="Mark as favorite"></i>
                </div>
                ${metaMinimalHTML}
                <div class="full-prompt-display">${formatFullPromptForDisplay(promptData.fullPrompt || promptData.title)}</div>
                <button class="select-prompt-button">
                    <i class="fas fa-check-circle"></i> 
                    ${isFavoriteListItem ? 'Usar Favorito' : 'Seleccionar este Prompt'}
                </button>
            `;

            card.querySelector('.star-icon').addEventListener('click', (e) => { e.stopPropagation(); toggleFavorite(promptData, e.target); });
            card.querySelector('.select-prompt-button').addEventListener('click', () => {
                searchInput.value = promptData.fullPrompt || promptData.title; 
                autoResizeTextarea(searchInput); 
                exitGuidedModeIfNeeded(); 
                simplePromptSuggestionsContainer.classList.add('hidden');
                favoritePromptsSection.classList.add('hidden');
                enhancedPromptSuggestionsContainer.innerHTML = ''; 
                searchInput.focus();
            });
            return card;
        }

        function displayGuidedPromptList(suggestions) {
            isGuidedModeActive = true; 
            enhancedPromptSuggestionsContainer.innerHTML = '';
            simplePromptSuggestionsContainer.classList.add('hidden');
            favoritePromptsSection.classList.add('hidden');

            if (suggestions && suggestions.length > 0) {
                suggestions.forEach(suggData => {
                    const card = createPromptCard(suggData); 
                    enhancedPromptSuggestionsContainer.appendChild(card);
                });
            }
            searchBarCard.classList.add('always-focused'); 
            analyzingIndicator.classList.add('hidden');
        }
        
        function generateGuidedPrompts() {
            let suggestions = [];
            suggestions.push({
                title: "Improve with Few-Shot Examples", id: "few-shot-summarize-pdf",
                fullPrompt: `Write a blog article about how to summarize a PDF. Follow the style and structure of these examples:\n\nExample 1:\nTitle: "How to Create a Mind Map"\nIntroduction: Mind maps are great for organizing thoughts. This article explains simple steps to create one.\nStep 1: Choose a central topic...\nStep 2: Add branches for subtopics...\nConclusion: Mind mapping is easy and fun. Try it today!\n\nExample 2:\nTitle: "How to Take Better Notes"\nIntroduction: Good notes help you study better. Here’s how to do it.\nStep 1: Use a clear format...\nStep 2: Highlight key points...\nConclusion: Better notes lead to better learning. Start now!\n\nWrite a 400-word blog article titled "How to Summarize a PDF Effectively" with an introduction, 3 practical steps, and a conclusion. Use a clear and simple tone for students.`,
                metaMinimal: { inputTokens: "170", outputTokens: "~400-600", time: "~1-2s", reuse: "High" },
                description: "Uses few-shot examples."
            });
            suggestions.push({
                title: "Enhance with Chain of Thought", id: "cot-summarize-pdf",
                fullPrompt: `Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process as follows:\n\nIdentify the key components of a blog article (e.g., introduction, main steps, conclusion).\n\nList 3 practical methods for summarizing a PDF (e.g., using software, manual highlighting, or online tools).\n\nFor each method, explain one benefit and one challenge.\n\nStructure the article with a clear introduction, a section for each method, and a conclusion encouraging readers to try summarizing.\n\nUse a professional yet accessible tone for small business owners. Title the article "Mastering PDF Summarization: A Step-by-Step Guide."`,
                metaMinimal: { inputTokens: "195", outputTokens: "~500-800", time: "~1-3s", reuse: "High" },
                description: "Uses Chain of Thought."
            });
            currentGuidedSuggestions = suggestions;
            return suggestions;
        }

        function generateSimpleSuggestions(inputText) {
             const baseSuggestions = [
                "Write a blog post about how to automatically extract key points from a PDF", "Write an article about summarizing research papers using AI",
                "Write a tweet about the best free tools to summarize PDFs", "Write a blog post about building a PDF summarizer with Python",
                "Write a tutorial about using OCR to summarize scanned PDFs", "Write a blog article about summarizing legal documents without reading them",
                "Write a post about converting PDFs into bullet points with open-source tools", "Write a comparison article about different PDF summarization APIs",
                "Write a workflow guide about using n8n to summarize incoming PDF attachments", "Write a blog post about syncing summarized PDF meeting notes with Notion"
            ];
            if (!inputText) return [];
            const lowerInput = inputText.toLowerCase();
            return baseSuggestions.filter(sugg => sugg.toLowerCase().includes(lowerInput)).slice(0, 5);
        }

        const handleSimpleInput = debounce(function() {
            if (isGuidedModeActive) return;
            const inputText = searchInput.value;
            simplePromptSuggestionsContainer.innerHTML = '';
            enhancedPromptSuggestionsContainer.innerHTML = ''; 
            
            if (inputText.length < 3) {
                simplePromptSuggestionsContainer.classList.add('hidden');
                if (inputText.length === 0) renderFavoritePrompts();
                else favoritePromptsSection.classList.add('hidden');
                return;
            }
            const suggestions = generateSimpleSuggestions(inputText);
            if (suggestions.length > 0) {
                simplePromptSuggestionsContainer.classList.remove('hidden');
                favoritePromptsSection.classList.add('hidden'); 
                suggestions.forEach(suggText => { 
                    const item = document.createElement('div');
                    item.classList.add('simple-suggestion-item');
                    const matchIndex = suggText.toLowerCase().indexOf(inputText.toLowerCase());
                    if (matchIndex > -1) {
                        item.innerHTML = suggText.substring(0, matchIndex) +
                                         `<strong>${suggText.substring(matchIndex, matchIndex + inputText.length)}</strong>` +
                                         suggText.substring(matchIndex + inputText.length);
                    } else { item.textContent = suggText; }
                    item.addEventListener('click', () => {
                        searchInput.value = suggText;
                        autoResizeTextarea(searchInput);
                        simplePromptSuggestionsContainer.classList.add('hidden');
                        searchInput.focus();
                        exitGuidedModeIfNeeded(); 
                    });
                    simplePromptSuggestionsContainer.appendChild(item);
                });
            } else {
                simplePromptSuggestionsContainer.classList.add('hidden');
                 if (inputText.length === 0) renderFavoritePrompts();
                 else favoritePromptsSection.classList.add('hidden');
            }
        }, 300);

        async function handleGuidedCreateClick() {
            isGuidedModeActive = true;
            simplePromptSuggestionsContainer.classList.add('hidden');
            enhancedPromptSuggestionsContainer.innerHTML = ''; 
            favoritePromptsSection.classList.add('hidden');
            searchBarCard.classList.add('always-focused'); 
            analyzingIndicator.classList.remove('hidden');
            await new Promise(resolve => setTimeout(resolve, 4200)); 
            const guidedPrompts = generateGuidedPrompts(); 
            displayGuidedPromptList(guidedPrompts); 
        }
        
        function exitGuidedModeIfNeeded() {
            if (isGuidedModeActive) {
                isGuidedModeActive = false;
                searchBarCard.classList.remove('always-focused');
                enhancedPromptSuggestionsContainer.innerHTML = ''; 
                if (searchInput.value === '') { renderFavoritePrompts(); } 
                else { handleSimpleInput(); } 
                autoResizeTextarea(searchInput); 
            }
        }

        searchInput.addEventListener('input', () => {
            autoResizeTextarea(searchInput);
            if (isGuidedModeActive && searchInput.value === '') { 
                exitGuidedModeIfNeeded(); 
            } else if (!isGuidedModeActive) {
                handleSimpleInput();
            }
        });
        guidedCreateBtn.addEventListener('click', handleGuidedCreateClick);

        searchInput.addEventListener('focus', () => {
            searchBarCard.classList.add('always-focused'); 
            autoResizeTextarea(searchInput);
            if (isGuidedModeActive) return; 
            searchInput.placeholder = 'Type your prompt here...';
            if (searchInput.value === '') renderFavoritePrompts();
            else handleSimpleInput();
        });

        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                if (!searchBarCard.contains(document.activeElement) && 
                    !modelDropdownList.contains(document.activeElement) && /* No quitar foco si se clickea el dropdown */
                    document.activeElement !== modelSelectorBtn) { 
                        
                    searchBarCard.classList.remove('always-focused');
                    searchBarCard.classList.remove('focused-card'); 
                    simplePromptSuggestionsContainer.classList.add('hidden');
                    if (searchInput.value === '' && !isGuidedModeActive) renderFavoritePrompts();
                }
            }, 150);
        });

        document.addEventListener('click', (event) => {
            if (!modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target)) {
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }
            if (!searchBarCard.contains(event.target)) {
                simplePromptSuggestionsContainer.classList.add('hidden');
                searchBarCard.classList.remove('always-focused'); 
                searchBarCard.classList.remove('focused-card');
                if (isGuidedModeActive) { exitGuidedModeIfNeeded(); } 
                else if (searchInput.value === '') {
                    searchInput.placeholder = originalPlaceholder;
                    renderFavoritePrompts();
                }
            }
        });
        
        function toggleFavorite(promptData, starIconElement) {
            const promptId = promptData.id || promptData.title;
            let index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptId);
            let wasFavorited = index > -1;

            if (wasFavorited) { favoritePrompts.splice(index, 1); } 
            else {
                const favData = { 
                    id: promptData.id, title: promptData.title, fullPrompt: promptData.fullPrompt, 
                    metaMinimal: promptData.metaMinimal, description: promptData.description || "N/A"
                };
                favoritePrompts.push(favData);
            }
            localStorage.setItem('favoritePromptsAllHub', JSON.stringify(favoritePrompts));
            document.querySelectorAll(`[data-prompt-id="${promptId}"] .star-icon`).forEach(star => {
                if (wasFavorited) { star.classList.remove('favorited'); star.classList.replace('fas','far'); } 
                else { star.classList.add('favorited'); star.classList.replace('far','fas'); }
            });
            if (!isGuidedModeActive && searchInput.value === '') { renderFavoritePrompts(); }
        }

        modelSelectorBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            modelDropdownList.classList.toggle('hidden');
            modelSelectorBtn.classList.toggle('open');
            if (!modelDropdownList.classList.contains('hidden')) { // Si se está abriendo
                exitGuidedModeIfNeeded(); 
                simplePromptSuggestionsContainer.classList.add('hidden');
                favoritePromptsSection.classList.add('hidden');
            }
        });
        modelDropdownList.querySelectorAll('li').forEach(item => {
            item.addEventListener('click', (e) => { 
                e.stopPropagation();
                const selectedModel = item.dataset.modelName;
                currentModelNameSpan.textContent = selectedModel;
                modelDropdownList.querySelectorAll('li').forEach(li => li.classList.remove('selected-model-item'));
                item.classList.add('selected-model-item');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
                searchInput.focus();
            });
        });
        const firstModelInList = modelDropdownList.querySelector('li');
        if (firstModelInList) {
            currentModelNameSpan.textContent = firstModelInList.dataset.modelName;
            firstModelInList.classList.add('selected-model-item');
        }

        document.getElementById('copyBtn').addEventListener('click', (e) => { e.stopPropagation(); if(searchInput.value) navigator.clipboard.writeText(searchInput.value).then(() => alert('Copied!')); });
        document.getElementById('sendBtn').addEventListener('click', (e) => { e.stopPropagation(); if(searchInput.value) alert('Prompt sent: ' + searchInput.value);});
        
        autoResizeTextarea(searchInput); 
        renderFavoritePrompts();

    </script>
</body>
</html>