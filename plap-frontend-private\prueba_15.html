<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Poppins', sans-serif;
            --color-bg: #F3F4F6;
            --color-card-bg: #FFFFFF;
            --color-text: #111827;
            --color-cta: #3B82F6;
            --color-cta-text: #FFFFFF;
            --color-shadow-outset-primary: #E5E7EB;
            --color-shadow-outset-secondary: #D1D5DB;
            --color-accent-start: #FF6EC4;
            --color-accent-middle: #7873F5;
            --color-accent-end: #4ADEDE;
            --color-icon-gray: #9CA3AF;
            --color-placeholder: #9CA3AF;
            --color-hover-bg: #F9FAFB;
            --border-radius-card: 16px;
            --border-radius-pill: 9999px;
            --border-radius-element: 8px;
            --color-star-active: #FFD700; /* Usada para favoritos, no para iconos de modelo*/
        }

        body {
            margin: 0; font-family: var(--font-body); background-color: var(--color-bg);
            min-height: 100vh; display: flex; flex-direction: column; align-items: center;
            padding: 20px; box-sizing: border-box; position: relative; color: var(--color-text);
            background-image: linear-gradient(to bottom left, #ffffff, lch(91.25% 13.31 276.59));
        }

        .all-hub-logo { position: absolute; top: 25px; left: 30px; font-family: var(--font-header); font-size: 1.5rem; font-weight: 700; color: #023c99; z-index: 100; }
        .auth-buttons { position: absolute; top: 25px; right: 30px; display: flex; gap: 15px; z-index: 100; }
        .auth-btn { padding: 8px 18px; border-radius: var(--border-radius-pill); font-size: 0.9rem; font-weight: 500; cursor: pointer; transition: all 0.3s ease; text-decoration: none; display: inline-block; line-height: 1.5; }

        @keyframes signUpGradientAnimation {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .btn-signin {
            background-color: transparent;
            border: 1.5px solid #2563EB;
            color: #2563EB;
        }
        .btn-signin:hover {
            background-color: rgba(37, 99, 235, 0.1);
            border-color: var(--color-cta);
            color: var(--color-cta);
        }

        .btn-signup {
            background: linear-gradient(90deg,
                var(--color-accent-start),
                var(--color-accent-middle),
                var(--color-accent-end),
                var(--color-accent-middle),
                var(--color-accent-start)
            );
            background-size: 300% 100%;
            color: var(--color-cta-text);
            border: none;
            box-shadow: 0 6px 15px -4px rgba(0, 0, 0, 0.07), 0 3px 10px -5px rgba(0, 0, 0, 0.05);
            animation: signUpGradientAnimation 6s linear infinite;
        }
        .btn-signup:hover {
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 0 15px 0px rgba(120, 115, 245, 0.6),
                        0 12px 25px -6px rgba(0, 0, 0, 0.07),
                        0 5px 12px -5px rgba(0, 0, 0, 0.05);
        }

        .header-container { text-align: center; margin-top: 15vh; margin-bottom: 30px; position: relative; z-index: 5; width: 100%; }

        @keyframes synchronizedNeonShine {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .main-header {
            font-family: var(--font-header); font-size: 3rem; margin-bottom: 5px; font-weight: 600; letter-spacing: 1px;
            background: linear-gradient(90deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%;
            -webkit-background-clip: text; -webkit-text-fill-color: transparent;
            animation: synchronizedNeonShine 5s linear infinite;
            display: inline-block;
        }
        .copilot-subtitle { font-size: 1.1rem; font-weight: 400; color: #6B7280; line-height: 1.6; margin-top: 0.5rem; margin-bottom: 1.5rem; }

        .search-area-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            width: 100%; max-width: 700px;
            margin-bottom: auto;
            position: relative;
            animation: fadeInUp 1s ease-out 0.2s;
            animation-fill-mode: backwards;
            z-index: 2;
        }

        .search-bar-card {
            width: 100%; background-color: var(--color-card-bg);
            border-radius: var(--border-radius-card);
            box-shadow: 0 10px 30px rgba(0, 0, 0, 0.1), 0 6px 15px rgba(0,0,0,0.07);
            border: none;
            position: relative;
            display: flex; flex-direction: column;
            box-sizing: border-box;
            transition: box-shadow 0.4s ease;
            overflow: hidden;
            z-index: 1;
        }
        .search-bar-card::before {
            content: ''; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            background: transparent(120deg,
                var(--color-accent-start),
                var(--color-accent-middle),
                var(--color-accent-end),
                var(--color-accent-middle),
                var(--color-accent-start)
            );
            background-size: 300% 100%;
            z-index: -1;
            border-radius: inherit;
            padding: 2.5px;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out;
            mask-composite: exclude;
            opacity: 0;
            transition: opacity 0.4s ease;
            animation: synchronizedNeonShine 10s linear infinite paused;
        }

        .search-bar-card.focused-card::before,
        .search-bar-card.always-focused::before {
            opacity: 1;
            animation-play-state: running;
        }
        .search-bar-card.focused-card,
        .search-bar-card.always-focused {
            box-shadow:
                0 0 7px rgba(255, 110, 196, 0.5),
                0 0 12px rgba(120, 115, 245, 0.4),
                0 0 18px rgba(74, 222, 222, 0.3),
                0 10px 30px rgba(0, 0, 0, 0.1),
                0 6px 35px rgba(0,0,0,0.07);
        }

        .search-input {
            width: 100%;
            padding: 20px 20px 10px 20px;
            background: transparent; border: none; outline: none; color: var(--color-text);
            font-size: 1rem; font-family: var(--font-body); font-weight: 400;
            resize: none; overflow-y: hidden; line-height: 1.6; min-height: 70px;
            box-sizing: border-box;
        }
        .search-input::placeholder { color: var(--color-placeholder); opacity: 1; font-weight: 400; }

        .toolbar-bottom {
            display: flex; justify-content: space-between; align-items: center;
            padding: 8px 16px;
            background-color: var(--color-card-bg);
            position: relative;
            z-index: 1;
        }

        .model-selector-container {
        }
        .model-selector-btn {
            position: relative; background-color: var(--color-hover-bg); border: none;
            color: var(--color-text); padding: 8px 15px; border-radius: var(--border-radius-element);
            cursor: pointer; font-size: 0.85rem; font-weight: 500;
            display: flex; align-items: center; gap: 8px;
            transition: box-shadow 0.2s ease;
            z-index: 3;
        }
        .model-selector-btn::before {
            content: ""; position: absolute; top: 0; left: 0; right: 0; bottom: 0;
            border-radius: var(--border-radius-element); padding: 1.5px;
            background: linear-gradient(120deg, var(--color-accent-start), var(--color-accent-middle), var(--color-accent-end), var(--color-accent-middle), var(--color-accent-start));
            background-size: 300% 100%; animation: synchronizedNeonShine 5s linear infinite;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: subtract; mask-composite: subtract;
            z-index: -1; opacity: 0.8;
            transition: opacity 0.3s ease;
        }
        .model-selector-btn:hover::before { opacity: 1; }
        .model-selector-btn:hover { box-shadow: 0 0 8px rgba(var(--color-cta), 0.3); }
        .model-selector-btn #currentModelName { max-width: 180px; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
        .model-selector-btn .fa-chevron-down { font-size: 0.75em; transition: transform 0.2s ease; }
        .model-selector-btn.open .fa-chevron-down { transform: rotate(180deg); }

        #modelDropdownList {
            position: absolute;
            background-color: var(--color-card-bg);
            border: 1px solid var(--color-shadow-outset-primary);
            box-shadow: 0 4px 15px rgba(0,0,0,0.1);
            border-radius: var(--border-radius-element);
            z-index: 110;
            opacity: 0; visibility: hidden;
            transform: translateY(-10px);
            transition: opacity 0.25s ease, visibility 0.25s ease, transform 0.25s ease;
            padding: 8px;
            box-sizing: border-box;
            width: max-content;
            min-width: 260px;
        }
        #modelDropdownList.visible {
            opacity: 1;
            visibility: visible;
            transform: translateY(0);
        }

        .model-search-container { position: relative; margin-bottom: 8px; }
        .model-search-container .fa-search { position: absolute; top: 50%; left: 12px; transform: translateY(-50%); color: var(--color-placeholder); font-size: 0.9em; }
        #modelSearchInput { width: 100%; padding: 10px 12px 10px 35px; border: 1px solid var(--color-shadow-outset-primary); border-radius: 6px; font-size: 0.85rem; box-sizing: border-box; outline: none; }
        #modelSearchInput:focus { border-color: var(--color-cta); box-shadow: 0 0 0 2px rgba(59, 130, 246, 0.2); }
        #modelDropdownList ul { list-style: none; padding: 0; margin: 0; max-height: 200px; overflow-y: auto;}
        #modelDropdownList ul::-webkit-scrollbar { width: 6px; }
        #modelDropdownList ul::-webkit-scrollbar-track { background: #f1f1f1; border-radius: 3px; }
        #modelDropdownList ul::-webkit-scrollbar-thumb { background: #ccc; border-radius: 3px; }
        #modelDropdownList ul::-webkit-scrollbar-thumb:hover { background: #aaa; }
        #modelDropdownList li { padding: 10px 12px; font-size: 0.85rem; color: var(--color-text); cursor: pointer; border-radius: 6px; transition: background-color 0.2s ease, color 0.2s ease; display: flex; align-items: center; gap: 10px; }
        #modelDropdownList li img, #modelDropdownList li .model-icon { width: 18px; height: 18px; object-fit: contain; flex-shrink: 0; border-radius: 3px; text-align: center; line-height: 18px; font-size: 16px; transition: color 0.2s ease, filter 0.2s ease; }
        #modelDropdownList li .icon-placeholder { background-color: #eee; display: inline-block; }
        /* Colores existentes de Google */
        #modelDropdownList li .model-icon.google-blue { color: #4285F4; }
        #modelDropdownList li .model-icon.google-green { color: #34A853; }
        #modelDropdownList li .model-icon.google-red { color: #EA4335; }
        /* NUEVOS COLORES DE ESTRELLA PARA MODELOS */
        #modelDropdownList li .model-icon.star-black { color: #000000; }
        #modelDropdownList li .model-icon.star-orange { color: #FFA500; }
        #modelDropdownList li .model-icon.star-blue-gemini { color: #007BFF; }
        #modelDropdownList li .model-icon.star-darkgray { color: #696969; }
        #modelDropdownList li .model-icon.star-skyblue { color: #87CEEB; }

        #modelDropdownList li span { white-space: nowrap; overflow: hidden; text-overflow: ellipsis; }
        #modelDropdownList li:hover { background-color: var(--color-hover-bg); }
        #modelDropdownList li.selected-model-item { background-color: #3B82F6; color: var(--color-cta-text); font-weight: 500; }
        #modelDropdownList li.selected-model-item img { filter: brightness(0) invert(1); }
        #modelDropdownList li.selected-model-item .model-icon { color: var(--color-cta-text); }

        .search-actions { display: flex; align-items: center; gap: 12px; z-index: 2; }
        .action-icon { font-size: 1.2rem; color: var(--color-icon-gray); cursor: pointer; transition: color 0.2s ease, transform 0.2s ease; padding: 8px; border-radius: 50%; }
        .action-icon:hover { color: var(--color-cta); background-color: var(--color-hover-bg); transform: scale(1.1); }

        #dynamicContentArea {
            padding: 0 16px 16px 16px;
            background-color: var(--color-card-bg);
        }
        #dynamicContentArea > *:first-child:not(.hidden) {
             margin-top: 10px;
        }

        .analyzing-indicator { text-align: center; padding: 20px 10px; font-size: 1rem; color: var(--color-icon-gray); }
        .analyzing-indicator .fa-spinner { margin-right: 10px; font-size: 1.2em; animation: spin 1.5s linear infinite; }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }
        .simple-suggestions-container { background-color: var(--color-card-bg); max-height: 280px; overflow-y: auto; border-radius: var(--border-radius-element); border: 1px solid var(--color-shadow-outset-primary); }
        .simple-suggestion-item { padding: 10px 15px; font-size: 0.9rem; color: var(--color-text); cursor: pointer; border-bottom: 1px solid var(--color-shadow-outset-primary); transition: background-color 0.2s ease; }
        .simple-suggestion-item:last-child { border-bottom: none; }
        .simple-suggestion-item:hover { background-color: var(--color-hover-bg); }
        .simple-suggestion-item strong { font-weight: 600; }
        .enhanced-prompt-suggestions { }
        .enhanced-prompt-item { background-color: #fdfdff; border: 1px solid #e0e7ff; border-radius: var(--border-radius-element); padding: 18px 22px; margin-bottom: 20px; box-shadow: 0 2px 8px rgba(100,116,139,0.08); }
        .enhanced-prompt-item:last-child { margin-bottom: 0; }
        .enhanced-prompt-item-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .enhanced-prompt-item-header h5 { margin: 0; font-size: 1.1rem; font-weight: 600; color: var(--color-text); flex-grow: 1; padding-right: 10px; }
        .enhanced-prompt-item-header .star-icon { font-size: 1.1rem; color: var(--color-icon-gray); transition: color 0.2s ease, transform 0.2s ease; flex-shrink: 0; cursor:pointer;}
        .enhanced-prompt-item-header .star-icon:hover { transform: scale(1.2); }
        .enhanced-prompt-item-header .star-icon.favorited { color: var(--color-star-active); }
        .enhanced-prompt-item-meta-minimal { display: flex; flex-wrap: wrap; gap: 15px; font-size: 0.8rem; color: #4B5563; margin-bottom: 15px; padding: 10px; background-color: #f0f4f8; border-radius: 6px; }
        .meta-item { display: flex; align-items: center; gap: 6px; }
        .meta-item i { color: var(--color-cta); font-size: 0.95em; }
        .full-prompt-display { font-size: 0.9rem; line-height: 1.65; color: #374151; white-space: pre-wrap; word-break: break-word; margin-top: 0; margin-bottom: 15px; }
        .full-prompt-display .prompt-heading { font-weight: 600; color: #1e40af; margin-top: 0.8em; margin-bottom: 0.4em; display: block; }
        .full-prompt-display br + .prompt-heading { margin-top: 1.2em; }
        .full-prompt-display br { content: ""; display: block; margin-bottom: 0.3em; }
        .full-prompt-display br + br { margin-bottom: 0.75em; }
        .select-prompt-button { display: inline-block; background-color: var(--color-cta); color: var(--color-cta-text); padding: 9px 18px; border-radius: var(--border-radius-element); font-size: 0.9rem; font-weight: 500; cursor: pointer; border: none; transition: background-color 0.2s ease; }
        .select-prompt-button:hover { background-color: #2563EB; }
        .select-prompt-button i { margin-right: 7px; }
        .favorite-prompts-section { border-top: 1px solid var(--color-shadow-outset-primary); }
        .favorite-prompts-section h4 { font-size: 0.9rem; font-weight: 600; color: var(--color-text); margin: 0 0 10px 0; display: flex; align-items: center; padding-top:15px; }
        .favorite-prompts-section h4 .fa-star { margin-right: 8px; color: var(--color-star-active); }
        .favorite-prompts-list .enhanced-prompt-item { padding: 10px 12px; margin-bottom:12px; }
        .favorite-prompts-list .enhanced-prompt-item:last-child { margin-bottom: 0; }
        .hidden { display: none !important; }
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-15px); } to { opacity: 1; transform: translateY(0); } }
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px); } to { opacity: 1; transform: translateY(0); } }
        @media (max-width: 768px) { .header-container { margin-top: 10vh;} .main-header { font-size: 2.5rem; } .search-area-container { max-width: 95%; } .toolbar-bottom { flex-wrap: wrap; gap: 10px; padding: 8px;} .model-selector-btn { flex-grow: 1; justify-content: center;} .search-actions { flex-grow: 1; justify-content: flex-end;} }
        .copyright-footer { text-align: center; margin-top: 40px; color: var(--color-text); opacity: 0.7; font-size: 0.85rem; width: 100%; padding-bottom: 20px; }
    </style>
</head>
<body>
    <div class="all-hub-logo">allhub</div>
    <div class="auth-buttons">
        <a href="#" class="auth-btn btn-signin">Sign In</a>
        <a href="#" class="auth-btn btn-signup">Sign Up Free</a>
    </div>

    <div class="header-container">
        <h1 class="main-header">Prompt like a pro</h1>
        <p class="copilot-subtitle">Craft the perfect prompts with intelligent suggestions</p>
    </div>

    <div class="search-area-container" id="searchAreaContainer">
        <div class="search-bar-card" id="searchBarCard">
            <textarea class="search-input" id="searchInput" placeholder="Type your prompt here..." rows="1"></textarea>

            <div class="toolbar-bottom">
                <div class="model-selector-container">
                    <button class="model-selector-btn" id="modelSelectorBtn">
                        <span id="currentModelName">Model</span> <i class="fas fa-chevron-down"></i>
                    </button>
                </div>

                <div class="search-actions">
                    <i class="fas fa-microphone action-icon" id="micBtn" title="Voice Input"></i>
                    <i class="fas fa-wand-magic-sparkles action-icon" id="guidedCreateBtn" title="Guided Creation"></i>
                    <i class="fas fa-copy action-icon" id="copyBtn" title="Copy Prompt"></i>
                    <i class="fas fa-paper-plane action-icon" id="sendBtn" title="Send Prompt"></i>
                </div>
            </div>

            <div id="dynamicContentArea" class="hidden">
                <div class="analyzing-indicator hidden" id="analyzingIndicator">
                    <i class="fas fa-spinner fa-spin"></i> Analyzing your prompt...
                </div>
                <div class="simple-suggestions-container hidden" id="simplePromptSuggestionsContainer"></div>
                <div class="enhanced-prompt-suggestions hidden" id="enhancedPromptSuggestionsContainer"></div>
                 <div class="favorite-prompts-section hidden" id="favoritePromptsSection">
                    <h4><i class="fas fa-star"></i> Popular Prompts</h4>
                    <div class="favorite-prompts-list" id="favoritePromptsList"></div>
                </div>
            </div>
        </div>

        <div class="model-dropdown hidden" id="modelDropdownList">
            <div class="model-search-container">
                <i class="fas fa-search"></i>
                <input type="text" id="modelSearchInput" placeholder="Search models">
            </div>
            <ul></ul>
        </div>
    </div>

    <p class="copyright-footer">Copyright © 2023 - 2025 All Hub. All rights reserved.</p>

    <script>
        // --- CONSTANTES Y VARIABLES GLOBALES ---
        const searchAreaContainer = document.getElementById('searchAreaContainer');
        const searchBarCard = document.getElementById('searchBarCard');
        const searchInput = document.getElementById('searchInput');
        const guidedCreateBtn = document.getElementById('guidedCreateBtn');
        const micBtn = document.getElementById('micBtn');
        const simplePromptSuggestionsContainer = document.getElementById('simplePromptSuggestionsContainer');
        const analyzingIndicator = document.getElementById('analyzingIndicator');
        const enhancedPromptSuggestionsContainer = document.getElementById('enhancedPromptSuggestionsContainer');
        const favoritePromptsSection = document.getElementById('favoritePromptsSection');
        const favoritePromptsList = document.getElementById('favoritePromptsList');
        const dynamicContentArea = document.getElementById('dynamicContentArea');

        const modelSelectorBtn = document.getElementById('modelSelectorBtn');
        const currentModelNameSpan = document.getElementById('currentModelName');
        const modelDropdownList = document.getElementById('modelDropdownList');
        const modelSearchInput = document.getElementById('modelSearchInput');
        const modelListUl = modelDropdownList.querySelector('ul');

        let isGuidedModeActive = false;
        let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHub')) || [];
        let blockSuggestionsOnNextFocus = false; // <--- NUEVA BANDERA

        // --- DATOS DE MODELOS ACTUALIZADOS ---
        const modelsData = [
            { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
            { id: "anthropic-claude-3", displayName: "Anthropic: Claude 3", shortName: "Claude 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-orange" },
            { id: "google-gemini-1.5", displayName: "Google: Gemini 1.5", shortName: "Gemini 1.5", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-blue-gemini" },
            { id: "mistral-large", displayName: "Mistral: Large", shortName: "Mistral Large", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-darkgray" },
            { id: "meta-llama-3", displayName: "Meta: LLaMA 3", shortName: "LLaMA 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-skyblue" },
            { id: "mistral-devstral-small-free", displayName: "Mistral: Devstral Small (free)", shortName: "Devstral Sm Free", iconType: "img", iconUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF8C00'%3E%3Cpath d='M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5'/%3E%3C/svg%3E" },
            { id: "google-gemma-3n-4b-free", displayName: "Google: Gemma 3n 4B (free)", shortName: "Gemma 3n 4B", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "google-blue" }
        ];

        function debounce(func, delay) { let timeout; const debounced = function(...args) { const context = this; clearTimeout(timeout); timeout = setTimeout(() => func.apply(context, args), delay); }; debounced.cancel = function() { clearTimeout(timeout); }; return debounced; }
        function autoResizeTextarea(textarea) { textarea.style.height = 'auto'; let scrollHeight = textarea.scrollHeight; textarea.style.height = scrollHeight + 'px'; textarea.style.overflowY = 'hidden'; }
        function formatFullPromptForDisplay(fullPromptText) { if (!fullPromptText) return ""; let html = fullPromptText; html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim, (match) => `<span class="prompt-heading">${match.trim()}</span>`); return html; }

        function updateDynamicContentAreaVisibility() {
            const isSimpleVisible = !simplePromptSuggestionsContainer.classList.contains('hidden') && simplePromptSuggestionsContainer.innerHTML.trim() !== '';
            const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedPromptSuggestionsContainer.innerHTML.trim() !== '';
            const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && favoritePromptsList.innerHTML.trim() !== '';
            const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden');

            if (isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible) {
                dynamicContentArea.classList.remove('hidden');
            } else {
                dynamicContentArea.classList.add('hidden');
            }
        }

        function hideAllDynamicContentExcept(exceptContainer = null) {
            [simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => {
                if (container !== exceptContainer) {
                    container.classList.add('hidden');
                    if (container !== analyzingIndicator) container.innerHTML = '';
                }
            });
        }

        function setDefaultModel() {
            if (modelsData.length > 0) {
                const defaultModel = modelsData[0];
                currentModelNameSpan.textContent = defaultModel.shortName;
                currentModelNameSpan.dataset.selectedModelId = defaultModel.id;
            } else {
                currentModelNameSpan.textContent = "Model";
                delete currentModelNameSpan.dataset.selectedModelId;
            }
        }

        function populateModelDropdown() {
            modelListUl.innerHTML = '';
            const searchTerm = modelSearchInput.value.toLowerCase();
            const currentSelectedModelId = currentModelNameSpan.dataset.selectedModelId;

            modelsData.filter(model => model.displayName.toLowerCase().includes(searchTerm)).forEach(model => {
                const li = document.createElement('li');
                li.dataset.modelId = model.id;
                li.dataset.modelShortName = model.shortName;
                li.dataset.modelDisplayName = model.displayName;

                let iconElement;
                if (model.iconType === "fa") {
                    iconElement = document.createElement('i');
                    iconElement.className = `model-icon ${model.iconClass} ${model.iconColorClass || ''}`;
                } else {
                    iconElement = document.createElement('img');
                    iconElement.src = model.iconUrl;
                    iconElement.alt = model.displayName.split(':')[0];
                    iconElement.onerror = () => { iconElement.style.display = 'none'; };
                }
                li.appendChild(iconElement);

                const span = document.createElement('span');
                span.textContent = model.displayName;
                li.appendChild(span);

                if (currentSelectedModelId && currentSelectedModelId === model.id) {
                    li.classList.add('selected-model-item');
                }

                li.addEventListener('click', (e) => {
                    e.stopPropagation();
                    currentModelNameSpan.textContent = model.shortName;
                    currentModelNameSpan.dataset.selectedModelId = model.id;

                    modelListUl.querySelectorAll('li').forEach(item => item.classList.remove('selected-model-item'));
                    li.classList.add('selected-model-item');

                    modelDropdownList.classList.remove('visible');
                    modelDropdownList.classList.add('hidden');
                    modelSelectorBtn.classList.remove('open');
                    modelDropdownList.style.transform = 'translateY(-10px)';
                    searchInput.focus();
                    hideAllDynamicContentExcept();
                    updateDynamicContentAreaVisibility();
                });
                modelListUl.appendChild(li);
            });
        }

        modelSelectorBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const isCurrentlyHidden = modelDropdownList.classList.contains('hidden');

            exitGuidedModeIfNeeded();
            hideAllDynamicContentExcept();

            if (isCurrentlyHidden) {
                const cardRect = searchBarCard.getBoundingClientRect();
                const containerRect = searchAreaContainer.getBoundingClientRect();
                const buttonRect = modelSelectorBtn.getBoundingClientRect();
                const spaceBetweenCardAndDropdown = 8;

                modelDropdownList.style.top = (cardRect.bottom - containerRect.top + spaceBetweenCardAndDropdown) + 'px';
                modelDropdownList.style.left = (buttonRect.left - containerRect.left) + 'px';


                modelDropdownList.style.transform = 'translateY(-10px)';

                modelDropdownList.classList.remove('hidden');
                void modelDropdownList.offsetHeight;
                modelDropdownList.classList.add('visible');
                modelSelectorBtn.classList.add('open');
                modelSearchInput.value = '';
                populateModelDropdown();
                modelSearchInput.focus();
            } else {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
                modelDropdownList.style.transform = 'translateY(-10px)';
                if (searchInput.value === '') renderFavoritePrompts(); else handleSimpleInput();
            }
            updateDynamicContentAreaVisibility();
        });

        modelSearchInput.addEventListener('input', populateModelDropdown);
        modelSearchInput.addEventListener('click', (e) => e.stopPropagation());

        function renderFavoritePrompts() {
            if (isGuidedModeActive || modelDropdownList.classList.contains('visible')) return;

            hideAllDynamicContentExcept(favoritePromptsSection);
            favoritePromptsList.innerHTML = '';

            if (favoritePrompts.length > 0 && searchInput.value.length === 0) {
                favoritePrompts.forEach(promptData => {
                    const item = createPromptCard(promptData, false, true);
                    favoritePromptsList.appendChild(item);
                });
                favoritePromptsSection.classList.remove('hidden');
            } else {
                favoritePromptsSection.classList.add('hidden');
            }
            updateDynamicContentAreaVisibility();
        }

        function toggleFavorite(promptData, starIconElement) { const promptId = promptData.id || promptData.title; let index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptId); let wasFavorited = index > -1; if (wasFavorited) { favoritePrompts.splice(index, 1); } else { const favData = { id: promptData.id, title: promptData.title, fullPrompt: promptData.fullPrompt, metaMinimal: promptData.metaMinimal, description: promptData.description || "N/A" }; favoritePrompts.push(favData); } localStorage.setItem('favoritePromptsAllHub', JSON.stringify(favoritePrompts)); document.querySelectorAll(`[data-prompt-id="${promptId}"] .star-icon`).forEach(star => { if (wasFavorited) { star.classList.remove('favorited'); star.classList.replace('fas','far'); } else { star.classList.add('favorited'); star.classList.replace('far','fas'); } }); if (!isGuidedModeActive && searchInput.value === '') { renderFavoritePrompts(); } }
        function createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) { const card = document.createElement('div'); card.classList.add('enhanced-prompt-item'); if (isFavoriteListItem) card.classList.add('favorite-list-item-card'); card.dataset.promptId = promptData.id || promptData.title; const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === (promptData.id||promptData.title)); const starClass = isFavorited ? 'fas fa-star star-icon favorited' : 'far fa-star star-icon'; let metaMinimalHTML = ''; if (promptData.metaMinimal) { metaMinimalHTML = '<div class="enhanced-prompt-item-meta-minimal">'; if (promptData.metaMinimal.inputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-in-alt"></i> Input Tokens: ${promptData.metaMinimal.inputTokens}</span>`; if (promptData.metaMinimal.outputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-out-alt"></i> Output Tokens: ${promptData.metaMinimal.outputTokens}</span>`; if (promptData.metaMinimal.time) metaMinimalHTML += `<span class="meta-item"><i class="far fa-clock"></i> Time: ${promptData.metaMinimal.time}</span>`; if (promptData.metaMinimal.reuse) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-redo-alt"></i> Reuse: ${promptData.metaMinimal.reuse}</span>`; metaMinimalHTML += '</div>'; } card.innerHTML = ` <div class="enhanced-prompt-item-header"> <h5>${promptData.title || "Prompt"}</h5> <i class="${starClass}" title="Mark as favorite"></i> </div> ${metaMinimalHTML} <div class="full-prompt-display">${formatFullPromptForDisplay(promptData.fullPrompt || promptData.title)}</div> <button class="select-prompt-button"> <i class="fas fa-check-circle"></i> ${isFavoriteListItem ? 'Usar Favorito' : 'Seleccionar este Prompt'} </button> `; card.querySelector('.star-icon').addEventListener('click', (e) => { e.stopPropagation(); toggleFavorite(promptData, e.target); }); card.querySelector('.select-prompt-button').addEventListener('click', () => { searchInput.value = promptData.fullPrompt || promptData.title; autoResizeTextarea(searchInput); exitGuidedModeIfNeeded(); hideAllDynamicContentExcept(); updateDynamicContentAreaVisibility(); searchInput.focus(); }); return card;  }

        function displayGuidedPromptList(suggestions) {
            isGuidedModeActive = true;
            hideAllDynamicContentExcept(enhancedPromptSuggestionsContainer);

            if (suggestions && suggestions.length > 0) {
                suggestions.forEach(suggData => {
                    const card = createPromptCard(suggData);
                    enhancedPromptSuggestionsContainer.appendChild(card);
                });
                enhancedPromptSuggestionsContainer.classList.remove('hidden');
            }
            searchBarCard.classList.add('always-focused');
            searchBarCard.classList.add('focused-card');
            updateDynamicContentAreaVisibility();
        }

        function generateGuidedPrompts() { let suggestions = []; suggestions.push({ title: "Improve with Few-Shot Examples", id: "few-shot-summarize-pdf", fullPrompt: `Write a blog article about how to summarize a PDF. Follow the style and structure of these examples:\n\nExample 1:\nTitle: "How to Create a Mind Map"\nIntroduction: Mind maps are great for organizing thoughts. This article explains simple steps to create one.\nStep 1: Choose a central topic...\nStep 2: Add branches for subtopics...\nConclusion: Mind mapping is easy and fun. Try it today!\n\nExample 2:\nTitle: "How to Take Better Notes"\nIntroduction: Good notes help you study better. Here’s how to do it.\nStep 1: Use a clear format...\nStep 2: Highlight key points...\nConclusion: Better notes lead to better learning. Start now!\n\nWrite a 400-word blog article titled "How to Summarize a PDF Effectively" with an introduction, 3 practical steps, and a conclusion. Use a clear and simple tone for students.`, metaMinimal: { inputTokens: "170", outputTokens: "~400-600", time: "~1-2s", reuse: "High" }, description: "Uses few-shot examples." }); suggestions.push({ title: "Enhance with Chain of Thought", id: "cot-summarize-pdf", fullPrompt: `Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process as follows:\n\nIdentify the key components of a blog article (e.g., introduction, main steps, conclusion).\n\nList 3 practical methods for summarizing a PDF (e.g., using software, manual highlighting, or online tools).\n\nFor each method, explain one benefit and one challenge.\n\nStructure the article with a clear introduction, a section for each method, and a conclusion encouraging readers to try summarizing.\n\nUse a professional yet accessible tone for small business owners. Title the article "Mastering PDF Summarization: A Step-by-Step Guide."`, metaMinimal: { inputTokens: "195", outputTokens: "~500-800", time: "~1-3s", reuse: "High" }, description: "Uses Chain of Thought." }); return suggestions; }

        async function handleGuidedCreateClick() {
            if (modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }
            hideAllDynamicContentExcept(analyzingIndicator);
            analyzingIndicator.classList.remove('hidden');
            searchBarCard.classList.add('always-focused');
            searchBarCard.classList.add('focused-card');
            updateDynamicContentAreaVisibility();

            await new Promise(resolve => setTimeout(resolve, 1200));
            const guidedPrompts = generateGuidedPrompts();
            displayGuidedPromptList(guidedPrompts);
        }

        function exitGuidedModeIfNeeded() {
            if (isGuidedModeActive) {
                isGuidedModeActive = false;
                hideAllDynamicContentExcept();

                if (document.activeElement !== searchInput) {
                    searchBarCard.classList.remove('always-focused');
                }
                if (searchInput.value === '') renderFavoritePrompts(); else handleSimpleInput();
                updateDynamicContentAreaVisibility();
            }
        }

        function generateSimpleSuggestions(inputText) {
            const baseSuggestions = [
                "Write a blog post about how to automatically extract key points from a PDF",
                "Write an article about summarizing research papers using AI",
                "Write a tweet about the best free tools to summarize PDFs",
                "Write a blog post about building a PDF summarizer with Python",
                "Write a blog article about summarizing legal documents without reading them",
                "Write a blog post about syncing summarized PDF meeting notes with Notion"
            ];
            if (!inputText) return [];
            const lowerInput = inputText.toLowerCase();
            return baseSuggestions.filter(sugg => sugg.toLowerCase().includes(lowerInput)).slice(0, 4);
        }

        const handleSimpleInput = debounce(function() {
            if (isGuidedModeActive || modelDropdownList.classList.contains('visible')) {
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
                return;
            }

            const inputText = searchInput.value;
            hideAllDynamicContentExcept(simplePromptSuggestionsContainer);
            simplePromptSuggestionsContainer.innerHTML = '';

            if (inputText.length < 1) {
                simplePromptSuggestionsContainer.classList.add('hidden');
                if (inputText.length === 0) renderFavoritePrompts();
                updateDynamicContentAreaVisibility();
                return;
            }

            const suggestions = generateSimpleSuggestions(inputText);
            if (suggestions.length > 0) {
                simplePromptSuggestionsContainer.classList.remove('hidden');
                suggestions.forEach(suggText => {
                    const item = document.createElement('div');
                    item.classList.add('simple-suggestion-item');
                    const matchIndex = suggText.toLowerCase().indexOf(inputText.toLowerCase());
                    if (matchIndex > -1) {
                        item.innerHTML = suggText.substring(0, matchIndex) +
                                         `<strong>${suggText.substring(matchIndex, matchIndex + inputText.length)}</strong>` +
                                         suggText.substring(matchIndex + inputText.length);
                    } else {
                        item.textContent = suggText;
                    }
                    item.addEventListener('click', () => {
                        searchInput.value = suggText;
                        autoResizeTextarea(searchInput);
                        hideAllDynamicContentExcept();
                        updateDynamicContentAreaVisibility();
                        blockSuggestionsOnNextFocus = true; // <--- ESTABLECE LA BANDERA
                        searchInput.focus();
                    });
                    simplePromptSuggestionsContainer.appendChild(item);
                });
            } else {
                simplePromptSuggestionsContainer.classList.add('hidden');
            }
            updateDynamicContentAreaVisibility();
        }, 300);

        searchInput.addEventListener('input', () => {
            autoResizeTextarea(searchInput);
            if (isGuidedModeActive && searchInput.value === '') {
                exitGuidedModeIfNeeded();
            } else if (!isGuidedModeActive && !modelDropdownList.classList.contains('visible')) {
                handleSimpleInput();
            } else if (modelDropdownList.classList.contains('visible')) {
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
            }
        });

        guidedCreateBtn.addEventListener('click', handleGuidedCreateClick);
        if (micBtn) {
            micBtn.addEventListener('click', () => {
                console.log("Micrófono presionado (implementar funcionalidad)");
            });
        }

        searchInput.addEventListener('focus', () => {
            searchBarCard.classList.add('always-focused');
            searchBarCard.classList.add('focused-card');
            autoResizeTextarea(searchInput);

            if (modelDropdownList.classList.contains('visible') || isGuidedModeActive) {
                return;
            }

            if (searchInput.value === '') {
                renderFavoritePrompts();
            } else {
                if (blockSuggestionsOnNextFocus) { // <--- COMPRUEBA Y RESETEA LA BANDERA
                    blockSuggestionsOnNextFocus = false;
                } else {
                    handleSimpleInput.cancel();
                    handleSimpleInput();
                }
            }
        });

        searchInput.addEventListener('blur', () => {
            setTimeout(() => {
                const activeEl = document.activeElement;
                const isFocusWithinRelevantArea = searchBarCard.contains(activeEl) || modelDropdownList.contains(activeEl);

                if (!isFocusWithinRelevantArea) {
                    searchBarCard.classList.remove('always-focused');
                    if (!modelDropdownList.classList.contains('visible')) {
                         searchBarCard.classList.remove('focused-card');
                    }

                    if (!modelDropdownList.classList.contains('visible') && !isGuidedModeActive) {
                        hideAllDynamicContentExcept();
                        if (searchInput.value === '') renderFavoritePrompts();
                        updateDynamicContentAreaVisibility();
                    }
                }
            }, 150);
        });

        document.addEventListener('click', (event) => {
            if (!modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target) && modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
                modelDropdownList.style.transform = 'translateY(-10px)';
                if (searchInput.value === '') renderFavoritePrompts(); else handleSimpleInput();
            }

            if (!searchBarCard.contains(event.target) &&
                !modelDropdownList.contains(event.target) &&
                !simplePromptSuggestionsContainer.contains(event.target) &&
                !enhancedPromptSuggestionsContainer.contains(event.target) &&
                !favoritePromptsSection.contains(event.target)
                ) {

                if (!isGuidedModeActive && !modelDropdownList.classList.contains('visible')) {
                    hideAllDynamicContentExcept();
                    if(searchInput.value === '') renderFavoritePrompts();
                    updateDynamicContentAreaVisibility();
                }
                if (!modelSelectorBtn.contains(event.target)){
                     searchBarCard.classList.remove('always-focused');
                     searchBarCard.classList.remove('focused-card');
                }
            }
        });

        document.getElementById('copyBtn').addEventListener('click', (e) => { e.stopPropagation(); if(searchInput.value) navigator.clipboard.writeText(searchInput.value).then(() => console.log('Prompt Copied!')).catch(err => console.error('Failed to copy prompt: ', err)); });
        document.getElementById('sendBtn').addEventListener('click', (e) => { e.stopPropagation(); if(searchInput.value) console.log('Prompt sent: ' + searchInput.value + ' (Model: ' + currentModelNameSpan.textContent + ')');});

        // --- INITIALIZATION ---
        autoResizeTextarea(searchInput);
        setDefaultModel();

        if (searchInput.value === '') {
            renderFavoritePrompts();
        } else {
            handleSimpleInput();
        }
        if (document.activeElement === searchInput) {
            searchBarCard.classList.add('always-focused');
            searchBarCard.classList.add('focused-card');
        }
    </script>
</body>
</html>