<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro - Glass Edition</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            
            /* Glassmorphism Color Palette (from your image) */
            --color-primary: #C645F9; /* Vibrante Magenta */
            --color-secondary: #5E6CE7; /* Indigo Azul */
            --color-dark: #0D0425; /* Púrpura Oscuro Profundo */
            --color-light: #FFFFFF; /* Blanco Puro */
            
            /* Background & Glass */
            /* --glass-bg: rgba(255, 255, 255, 0.1); /* Reducido para más transparencia */
            --glass-bg: rgba(255, 255, 255, 0.08); /* Aún más sutil para elementos anidados */
            --glass-bg-hover: rgba(255, 255, 255, 0.15);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.06); /* Toolbar un poco más transparente */

            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-border-highlight: rgba(255, 255, 255, 0.3); /* Para hover/focus */
            
            /* Sombras más suaves y direccionales */
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.1); /* Sombra más oscura y sutil */
            --glass-hover-shadow: 0 12px 40px rgba(13, 4, 37, 0.15);
            --glass-inset-highlight: inset 0 1px 1px rgba(255,255,255,0.2), 
                                     inset 0 -1px 1px rgba(0,0,0,0.05); /* Efecto de borde biselado */


            /* Text Colors */
            --color-text-primary: var(--color-dark); /* Usar el color oscuro para el texto principal */
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 70%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 50%, transparent);
            --color-text-on-glass: var(--color-light); /* Para texto sobre fondos de vidrio más oscuros o gradientes */
            --color-text-on-gradient: var(--color-light);

            /* Interactive States */
            --color-hover-accent: rgba(198, 69, 249, 0.15); /* Magenta claro para hover */
            --color-active-accent: rgba(94, 108, 231, 0.25); /* Indigo claro para active */
            
            --border-radius-card: 20px; /* Un poco más redondeado */
            --border-radius-pill: 50px;
            --border-radius-element: 14px; /* Un poco más redondeado */

            /* Transiciones */
            --transition-speed: 0.3s;
            --transition-ease: ease-in-out;
        }

        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            color: var(--color-text-primary);
            background-color: #E9E7FF; /* Un color de fondo base muy claro, el gradiente irá encima */
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* Animated Background */
        .background-container {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: -1;
            /* Gradiente de fondo más suave y con el púrpura oscuro en una esquina */
            background: linear-gradient(145deg, 
                rgba(200, 180, 255, 0.2) 0%, /* Lavanda muy claro */
                rgba(150, 170, 255, 0.15) 30%, /* Azulado claro */
                rgba(233, 231, 255, 0.3) 60%, /* Casi blanco */
                rgba(13, 4, 37, 0.05) 100%); /* Toque del púrpura oscuro */
            overflow: hidden; /* Para contener las formas */
        }

        .floating-shapes { /* Contenedor para formas */
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden; /* Importante para que las formas no generen scroll */
        }

        .shape {
            position: absolute;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 60% 40% 30% 70% / 50% 30% 70% 50%; /* Formas más orgánicas */
            opacity: 0.08; /* Más sutil */
            animation: float 20s infinite ease-in-out alternate; /* Movimiento más suave y alternado */
            filter: blur(20px); /* Blur más pronunciado para efecto etéreo */
        }
        /* Animación float más suave y con rotación */
        @keyframes float {
            0% { transform: translate(0px, 0px) rotate(0deg) scale(1); }
            100% { transform: translate(calc(var(--tx, 0) * 1px), calc(var(--ty, 0) * 1px)) rotate(calc(var(--r,0) * 1deg)) scale(var(--s, 1)); }
        }
        /* Posicionamiento y animación individual para las formas */
        .shape:nth-child(1) { width: 350px; height: 350px; top: 5%; left: -15%; --tx: 80; --ty: 50; --r: 60; --s:1.2; animation-duration: 35s; }
        .shape:nth-child(2) { width: 250px; height: 250px; top: 55%; right: -10%; --tx: -60; --ty: -70; --r: -45; --s:0.9; animation-duration: 40s; animation-delay: -5s; }
        .shape:nth-child(3) { width: 280px; height: 280px; bottom: 10%; left: 50%; --tx: 40; --ty: -90; --r: 90; --s:1.1; animation-duration: 45s; animation-delay: -10s; }
        .shape:nth-child(4) { width: 180px; height: 180px; top: 25%; right: 25%; --tx: -50; --ty: 60; --r: -70; --s:0.8; animation-duration: 38s; animation-delay: -2s; }


        /* Glass Navigation */
        .glass-nav {
            position: fixed;
            top: 25px; /* Un poco más de espacio */
            left: 50%;
            transform: translateX(-50%); /* Centrado */
            width: calc(100% - 50px); /* Ancho con márgenes */
            max-width: 1200px; /* Ancho máximo */
            z-index: 100;
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 14px 28px; /* Padding ajustado */
            background: var(--glass-bg);
            backdrop-filter: blur(18px); /* Blur sutil */
            -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-pill);
            box-shadow: var(--glass-shadow);
            transition: background var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease);
        }
        .glass-nav:hover {
            background: rgba(255, 255, 255, 0.15); /* Ligeramente más opaco en hover */
        }

        .glass-logo {
            font-family: var(--font-header);
            font-size: 1.5rem; /* Ligeramente más grande */
            font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
        }

        .glass-auth-buttons { display: flex; gap: 12px; }
        .glass-btn {
            padding: 10px 22px; /* Padding ajustado */
            border-radius: var(--border-radius-pill);
            font-size: 0.9rem;
            font-weight: 600; /* Manrope Semibold */
            cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            text-decoration: none;
            border: none;
            backdrop-filter: blur(8px); /* Blur más ligero para botones */
            -webkit-backdrop-filter: blur(8px);
        }
        .glass-btn-signin {
            background: rgba(255, 255, 255, 0.15); /* Más opaco */
            color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }
        .glass-btn-signin:hover {
            background: rgba(255, 255, 255, 0.25);
            border-color: var(--glass-border-highlight);
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(13,4,37, 0.08);
        }
        .glass-btn-signup {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: 1px solid rgba(255, 255, 255, 0.1); /* Borde sutil */
        }
        .glass-btn-signup:hover {
            transform: translateY(-2px) scale(1.03); /* Ligero scale en hover */
            box-shadow: 0 8px 25px color-mix(in srgb, var(--color-primary) 30%, transparent);
        }

        /* Main Content */
        .main-container {
            display: flex;
            flex-direction: column;
            align-items: center;
            min-height: 100vh;
            padding: 140px 20px 40px; /* Más padding superior */
            width: 100%;
        }
        .header-section { text-align: center; margin-bottom: 50px; z-index: 5; }
        .glass-title {
            font-family: var(--font-header);
            font-size: clamp(2.8rem, 6vw, 4.5rem); /* Ligeramente más grande */
            font-weight: 800; /* Orbitron Bold/Black */
            margin-bottom: 16px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: 1.1;
        }
        .glass-subtitle {
            font-size: clamp(1rem, 2.5vw, 1.25rem); /* Responsive */
            font-weight: 500; /* Manrope Medium */
            color: var(--color-text-secondary);
            max-width: 650px; /* Un poco más ancho */
            margin: 0 auto;
            line-height: 1.65;
        }

        /* Glass Search Container */
        .glass-search-container { width: 100%; max-width: 760px; position: relative; z-index: 10; }
        .glass-search-card {
            background: var(--glass-bg);
            backdrop-filter: blur(22px); /* Ajustar blur principal */
            -webkit-backdrop-filter: blur(22px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card);
            box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
            overflow: visible; /* Permitir que el dropdown sobresalga */
            position: relative;
        }
        .glass-search-card::before { /* Efecto de brillo sutil en el borde al enfocar */
            content: ''; position: absolute; top: -1px; left: -1px; right: -1px; bottom: -1px;
            border-radius: inherit; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            padding: 1.5px; /* Grosor del "borde" brillante */
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; mask-composite: exclude;
            opacity: 0; transition: opacity var(--transition-speed) var(--transition-ease);
            pointer-events: none; z-index: -1; /* Detrás del contenido */
        }
        .glass-search-card.focused {
            transform: translateY(-3px);
            box-shadow: var(--glass-hover-shadow);
            border-color: transparent; /* El borde brillante lo reemplaza */
        }
        .glass-search-card.focused::before { opacity: 0.7; }

        .glass-search-input {
            width: 100%; background: transparent; border: none; outline: none;
            padding: 22px 28px 16px; /* Ajuste de padding */
            font-size: 1.05rem; /* Ajuste de tamaño */
            font-family: var(--font-body); font-weight: 500; /* Manrope Medium */
            color: var(--color-text-primary);
            resize: none; min-height: 100px; /* Ligeramente más pequeño */
            line-height: 1.6;
        }
        .glass-search-input::placeholder { color: var(--color-text-muted); font-weight: 400; }

        /* Glass Toolbar */
        .glass-toolbar {
            display: flex; justify-content: space-between; align-items: center;
            padding: 14px 24px; /* Ajuste de padding */
            background: var(--glass-bg-toolbar); /* Toolbar más transparente */
            backdrop-filter: blur(12px); /* Blur más ligero para toolbar */
            -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
        }
        .glass-model-selector { position: relative; }
        .glass-model-btn {
            display: flex; align-items: center; gap: 10px; /* Reducir gap */
            background: rgba(255, 255, 255, 0.1); /* Más transparente */
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 10px 16px; /* Padding ajustado */
            font-size: 0.85rem; font-weight: 600; /* Manrope Semibold */
            color: var(--color-text-primary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
        }
        .glass-model-btn:hover {
            background: rgba(255, 255, 255, 0.2);
            border-color: var(--glass-border-highlight);
            transform: translateY(-1px);
            box-shadow: 0 2px 10px rgba(13,4,37, 0.08);
        }
        .glass-model-btn .fa-chevron-down { transition: transform var(--transition-speed) var(--transition-ease); }
        .glass-model-btn.open .fa-chevron-down { transform: rotate(180deg); }

        .glass-actions { display: flex; align-items: center; gap: 12px; /* Reducir gap */ }
        .glass-action-btn {
            width: 40px; height: 40px; /* Ligeramente más pequeños */
            display: flex; align-items: center; justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: 50%; color: var(--color-text-secondary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            font-size: 1rem; /* Ajuste de tamaño */
        }
        .glass-action-btn:hover {
            background: var(--color-hover-accent);
            color: var(--color-primary);
            border-color: var(--color-primary);
            transform: translateY(-2px) scale(1.08);
            box-shadow: 0 4px 15px color-mix(in srgb, var(--color-primary) 20%, transparent);
        }
        .glass-action-btn:active { /* Estado presionado */
            transform: translateY(0px) scale(1);
            background: var(--color-active-accent);
        }

        /* Glass Content Area (para sugerencias) */
        .glass-content-area {
            background: rgba(255, 255, 255, 0.03); /* Muy transparente */
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
            padding: 20px; /* Padding ajustado */
            max-height: 350px; /* Altura máxima con scroll */
            overflow-y: auto;
        }
        /* Scrollbar styling para Webkit */
        .glass-content-area::-webkit-scrollbar { width: 6px; }
        .glass-content-area::-webkit-scrollbar-track { background: transparent; }
        .glass-content-area::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.2); border-radius: 3px;}
        .glass-content-area::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.3); }


        .glass-suggestions { display: grid; gap: 14px; animation: fadeInUp 0.5s var(--transition-ease) forwards; }
        .glass-suggestion-item {
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 18px; /* Padding ajustado */
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
        }
        .glass-suggestion-item:hover {
            background: rgba(255, 255, 255, 0.18);
            border-color: var(--glass-border-highlight);
            transform: translateY(-2px);
            box-shadow: 0 6px 20px rgba(13,4,37, 0.08);
        }
        .glass-suggestion-title { font-size: 1rem; font-weight: 600; color: var(--color-text-primary); margin-bottom: 6px; }
        .glass-suggestion-desc { font-size: 0.85rem; color: var(--color-text-secondary); line-height: 1.55; }

        /* Glass Modal/Dropdown */
        .glass-dropdown {
            position: absolute;
            top: calc(100% + 6px); /* Espacio ajustado */
            left: 0; width: 100%; /* Ancho completo del botón selector */
            min-width: 220px; /* Ancho mínimo */
            background: var(--glass-bg);
            backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: 0 10px 30px rgba(13,4,37,0.1); /* Sombra más pronunciada */
            opacity: 0; visibility: hidden;
            transform: translateY(-8px) scale(0.98); /* Efecto de aparición */
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1000;
        }
        .glass-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0) scale(1); }
        .glass-dropdown-item {
            padding: 12px 18px; /* Padding ajustado */
            color: var(--color-text-primary); font-weight: 500; /* Manrope Medium */
            cursor: pointer; transition: background-color var(--transition-speed) var(--transition-ease);
            border-bottom: 1px solid rgba(255, 255, 255, 0.08);
            display: flex; align-items: center; gap: 10px;
        }
        .glass-dropdown-item:last-child { border-bottom: none; }
        .glass-dropdown-item:hover { background: var(--color-hover-accent); color: var(--color-primary); }
        .glass-dropdown-item i.fa-star { font-size: 0.9em; } /* Ajustar tamaño de iconos en dropdown */


        /* Animations */
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(20px) scale(0.98); } to { opacity: 1; transform: translateY(0) scale(1); } }
        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 15px color-mix(in srgb, var(--color-primary) 20%, transparent), 0 0 5px var(--color-primary); }
            50% { box-shadow: 0 0 30px color-mix(in srgb, var(--color-primary) 40%, transparent), 0 0 10px var(--color-primary); }
        }
        .pulse-effect { animation: pulseGlow 1.8s ease-in-out infinite; }

        /* Responsive Design */
        @media (max-width: 768px) {
            .glass-nav { margin: 15px; padding: 12px 18px; width: calc(100% - 30px); }
            .main-container { padding: 120px 16px 32px; }
            .glass-title { font-size: clamp(2.2rem, 7vw, 3rem); }
            .glass-subtitle { font-size: clamp(0.9rem, 3.5vw, 1.1rem); }
            .glass-toolbar { flex-direction: column; gap: 16px; padding: 16px; align-items: stretch; }
            .glass-model-selector { width: 100%; }
            .glass-model-btn { justify-content: center; }
            .glass-actions { justify-content: space-around; width: 100%; }
            .glass-search-input { min-height: 80px; padding: 18px 22px 12px;}
        }

        /* Loading States */
        .glass-loading { display: flex; align-items: center; justify-content: center; padding: 30px; color: var(--color-text-secondary); }
        .glass-spinner { width: 22px; height: 22px; border: 2px solid color-mix(in srgb, var(--color-primary) 20%, transparent); border-top-color: var(--color-primary); border-radius: 50%; animation: spin 0.8s linear infinite; margin-right: 10px; }
        /* @keyframes spin ya definido globalmente */

        /* Enhanced Prompt Cards */
        .enhanced-glass-card {
            background: rgba(255, 255, 255, 0.06); /* Más sutil */
            backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element); /* Usar element radius */
            padding: 20px; margin-bottom: 16px;
            transition: all var(--transition-speed) var(--transition-ease);
        }
        .enhanced-glass-card:hover {
            background: rgba(255, 255, 255, 0.12);
            border-color: var(--glass-border-highlight);
            transform: translateY(-3px);
            box-shadow: 0 10px 30px rgba(13,4,37, 0.1);
        }
        .enhanced-card-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 12px; }
        .enhanced-card-title { font-size: 1.1rem; font-weight: 600; color: var(--color-text-primary); }
        .glass-star-btn {
            background: rgba(255, 255, 255, 0.1);
            border: 1px solid var(--glass-border);
            border-radius: 50%; width: 32px; height: 32px;
            display: flex; align-items: center; justify-content: center;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-muted); font-size: 0.9rem;
        }
        .glass-star-btn:hover {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-primary);
            transform: scale(1.1);
        }
        .glass-star-btn.favorited {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-primary);
            border-color: var(--color-primary);
        }

        /* Copyright Footer */
        .glass-footer { margin-top: auto; padding: 30px 20px 20px; text-align: center; color: var(--color-text-muted); font-size: 0.85rem; }
        .glass-badge {
            display: inline-flex; align-items: center; gap: 6px;
            background: color-mix(in srgb, var(--color-primary) 10%, transparent);
            color: var(--color-primary);
            padding: 5px 10px; /* Ajuste de padding */
            border-radius: var(--border-radius-pill);
            font-size: 0.75rem; font-weight: 600; /* Manrope Semibold */
            border: 1px solid color-mix(in srgb, var(--color-primary) 20%, transparent);
        }
        .hidden { display: none !important; }
    </style>
</head>
<body>
    <!-- Animated Background -->
    <div class="background-container">
        <div class="floating-shapes">
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
            <div class="shape"></div>
        </div>
    </div>

    <!-- Glass Navigation -->
    <nav class="glass-nav">
        <div class="glass-logo">allhub</div>
        <div class="glass-auth-buttons">
            <a href="#" class="glass-btn glass-btn-signin">Sign In</a>
            <a href="#" class="glass-btn glass-btn-signup">Sign Up Free</a>
        </div>
    </nav>

    <!-- Main Container -->
    <main class="main-container">
        <!-- Header Section -->
        <section class="header-section">
            <h1 class="glass-title">Prompt like a pro</h1>
            <p class="glass-subtitle">Craft the perfect prompts with intelligent suggestions powered by advanced AI</p>
        </section>

        <!-- Glass Search Container -->
        <div class="glass-search-container" id="searchContainer">
            <div class="glass-search-card" id="searchCard">
                <textarea 
                    class="glass-search-input" 
                    id="searchInput" 
                    placeholder="Describe what you want to create and let AI enhance your prompt..."
                    rows="1"
                ></textarea>

                <div class="glass-toolbar">
                    <div class="glass-model-selector">
                        <button class="glass-model-btn" id="modelBtn">
                            <i class="fas fa-cogs" style="color: var(--color-secondary);"></i> <!-- Icono cambiado y coloreado -->
                            <span id="currentModel">Advanced AI</span> <!-- Nombre más genérico o el modelo por defecto -->
                            <i class="fas fa-chevron-down"></i>
                        </button>
                        
                        <div class="glass-dropdown" id="modelDropdown">
                            <div class="glass-dropdown-item" data-model="gpt-4">
                                <i class="fab fa-gripfire" style="color: var(--color-primary);"></i> OpenAI: GPT-4 Turbo
                            </div>
                            <div class="glass-dropdown-item" data-model="claude-3">
                                <i class="fas fa-brain" style="color: var(--color-secondary);"></i> Anthropic: Claude 3 Opus
                            </div>
                            <div class="glass-dropdown-item" data-model="gemini-1.5">
                                <i class="fas fa-gem" style="color: #2ECC71;"></i> Google: Gemini 1.5 Pro
                            </div>
                            <!-- Añadir más modelos si es necesario -->
                        </div>
                    </div>

                    <div class="glass-actions">
                        <button class="glass-action-btn" id="micBtn" title="Voice Input"><i class="fas fa-microphone"></i></button>
                        <button class="glass-action-btn" id="magicBtn" title="AI Enhancement"><i class="fas fa-wand-magic-sparkles"></i></button>
                        <button class="glass-action-btn" id="copyBtn" title="Copy Prompt"><i class="fas fa-copy"></i></button>
                        <button class="glass-action-btn pulse-effect" id="sendBtn" title="Generate"><i class="fas fa-paper-plane"></i></button>
                    </div>
                </div>

                <div class="glass-content-area hidden" id="contentArea">
                    <div class="glass-loading hidden" id="loadingState">
                        <div class="glass-spinner"></div>
                        Enhancing your prompt with AI magic...
                    </div>
                    <div class="glass-suggestions hidden" id="suggestions">
                        <!-- Example enhanced prompt -->
                        <div class="enhanced-glass-card">
                            <div class="enhanced-card-header">
                                <h3 class="enhanced-card-title">Refined: Technical Article Outline</h3>
                                <button class="glass-star-btn" title="Add to favorites"><i class="far fa-star"></i></button>
                            </div>
                            <div class="glass-badge" style="margin-bottom: 12px;">
                                <i class="fas fa-lightbulb"></i> AI Suggested Structure
                            </div>
                            <p class="glass-suggestion-desc">
                                **Objective:** Create a comprehensive guide on "Advanced CSS Grid Layouts for Responsive Design."
                                **Target Audience:** Intermediate to Advanced Web Developers.
                                **Key Sections:** 
                                1. Introduction: Brief overview of CSS Grid and its importance.
                                2. Core Concepts Revisited: `grid-template-columns/rows`, `grid-gap`, `fr` unit.
                                3. Advanced Techniques: Named grid lines, `minmax()`, `auto-fit` vs `auto-fill`, `grid-auto-flow`.
                                4. Real-world Examples: Complex card layouts, full-page magazine-style designs.
                                5. Performance & Accessibility Considerations.
                                6. Conclusion & Further Resources.
                                **Tone:** Technical, clear, and concise. Include code snippets for each technique.
                            </p>
                        </div>
                    </div>
                    <div class="glass-suggestions hidden" id="popularPrompts">
                         <h3 style="color: var(--color-text-primary); margin-bottom: 16px; display: flex; align-items: center; gap: 8px; font-weight:600; font-size: 1.1rem;">
                            <i class="fas fa-fire" style="color: var(--color-primary);"></i>
                            Trending Prompt Starters
                        </h3>
                        <div class="glass-suggestion-item">
                            <div class="glass-suggestion-title">Explain [complex topic] like I'm five.</div>
                            <div class="glass-suggestion-desc">Simplifies intricate subjects into easily understandable explanations.</div>
                        </div>
                        <div class="glass-suggestion-item">
                            <div class="glass-suggestion-title">Brainstorm 10 innovative marketing ideas for [product/service].</div>
                            <div class="glass-suggestion-desc">Generates creative marketing strategies tailored to your needs.</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <footer class="glass-footer">
            Copyright © 2025 All Hub. All rights reserved.
        </footer>
    </main>

    <script>
        const searchInput = document.getElementById('searchInput');
        const searchCard = document.getElementById('searchCard');
        const contentArea = document.getElementById('contentArea');
        const loadingState = document.getElementById('loadingState');
        const suggestionsContainer = document.getElementById('suggestions'); // Renombrado para claridad
        const popularPromptsContainer = document.getElementById('popularPrompts'); // Renombrado
        const modelBtn = document.getElementById('modelBtn');
        const modelDropdown = document.getElementById('modelDropdown');
        const currentModelDisplay = document.getElementById('currentModel'); // Renombrado
        
        // Auto-resize textarea
        searchInput.addEventListener('input', function() {
            this.style.height = 'auto';
            this.style.height = (this.scrollHeight < 80 ? 80 : this.scrollHeight) + 'px'; // Min height
            
            // Debounce showing suggestions for performance
            clearTimeout(this.suggestionTimeout);
            this.suggestionTimeout = setTimeout(() => {
                if (this.value.trim().length > 2) { // Show suggestions if more than 2 chars
                    showLoadingAndThenSuggestions();
                } else if (this.value.trim().length === 0 && searchCard.classList.contains('focused')) {
                    showPopularPrompts();
                } else {
                     hideAllContentPanels();
                }
            }, 300); // 300ms delay
        });

        function showLoadingAndThenSuggestions() {
            hideAllContentPanels();
            contentArea.classList.remove('hidden');
            loadingState.classList.remove('hidden');
            
            // Simulate AI processing
            setTimeout(() => {
                loadingState.classList.add('hidden');
                suggestionsContainer.classList.remove('hidden');
                // TODO: Populate suggestionsContainer with actual AI suggestions
            }, 1200); // Simulate 1.2 second processing time
        }

        function showPopularPrompts() {
            hideAllContentPanels();
            contentArea.classList.remove('hidden');
            popularPromptsContainer.classList.remove('hidden');
        }
        
        function hideAllContentPanels() {
            contentArea.classList.add('hidden');
            loadingState.classList.add('hidden');
            suggestionsContainer.classList.add('hidden');
            popularPromptsContainer.classList.add('hidden');
        }


        searchInput.addEventListener('focus', function() {
            searchCard.classList.add('focused');
            if (this.value.trim().length === 0) {
                showPopularPrompts();
            } else if (this.value.trim().length > 2) {
                showLoadingAndThenSuggestions(); // Or just show suggestions if already fetched
            }
        });

        let blurTimeout;
        searchInput.addEventListener('blur', function() {
             // Delay hiding to allow clicks on suggestions/dropdown
            blurTimeout = setTimeout(() => {
                if (!searchCard.contains(document.activeElement) && !modelDropdown.contains(document.activeElement)) {
                     searchCard.classList.remove('focused');
                     hideAllContentPanels();
                }
            }, 150);
        });

        // Prevent blur timeout if focus moves to dropdown or content area
        [modelDropdown, contentArea].forEach(el => {
            el.addEventListener('focusin', () => clearTimeout(blurTimeout));
            el.addEventListener('mousedown', () => clearTimeout(blurTimeout)); // Also on mousedown
        });


        modelBtn.addEventListener('click', function(e) {
            e.stopPropagation();
            modelDropdown.classList.toggle('visible');
            modelBtn.classList.toggle('open'); // For chevron rotation
        });

        document.querySelectorAll('.glass-dropdown-item').forEach(item => {
            item.addEventListener('click', function() {
                const modelName = this.textContent.trim().split(':')[1]?.trim() || this.textContent.trim();
                currentModelDisplay.textContent = modelName;
                modelDropdown.classList.remove('visible');
                modelBtn.classList.remove('open');
                searchInput.focus(); // Return focus to input
            });
        });

        document.addEventListener('click', function(e) {
            if (!modelBtn.contains(e.target) && !modelDropdown.contains(e.target)) {
                modelDropdown.classList.remove('visible');
                modelBtn.classList.remove('open');
            }
        });

        // Placeholder for action buttons
        document.getElementById('magicBtn')?.addEventListener('click', () => {
            if(searchInput.value.trim().length > 0) showLoadingAndThenSuggestions();
        });
        document.getElementById('copyBtn')?.addEventListener('click', () => navigator.clipboard.writeText(searchInput.value));
        document.getElementById('sendBtn')?.addEventListener('click', () => alert('Prompt sent: ' + searchInput.value));

        // Initialize text area height
        if(searchInput) {
            searchInput.style.height = 'auto';
            searchInput.style.height = (searchInput.scrollHeight < 80 ? 80 : searchInput.scrollHeight) + 'px';
        }

    </script>
</body>
</html>