# === Dependencias Node.js ===
node_modules/
npm-debug.log*
yarn-debug.log*
yarn-error.log*
package-lock.json # Opcional: A veces se ignora si se usa yarn.lock, pero es bueno incluirlo si usas npm
# O ignora yarn.lock si usas package-lock.json
# yarn.lock

# === Archivos de Build/Output de Next.js ===
.next/
out/
build/

# === Archivos de Configuración Local/Sensible ===
.env
.env*.local
.env.*.local

# === Archivos del Sistema Operativo ===
.DS_Store
Thumbs.db
ehthumbs.db

# === Archivos de IDEs / Editores ===
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# === Archivos de Log y Debug ===
*.log
logs/
*.log.*
debug.log*

# === Archivos Temporales ===
*~
*.tmp
*.swp
*.swo

# === Credenciales (Aunque no deberían estar aquí) ===
*.pem
*.key
*.cer
*.p12
*.pfx
*.jks
google-cloud-key.json # Por si acaso

# === Otros (Añade según tus herramientas) ===
.cache/
coverage/
*.env.test

# === Archivos específicos de Windows ===
desktop.ini