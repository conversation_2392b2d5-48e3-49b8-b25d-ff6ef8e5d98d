<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro - Fusión Mejorada</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <!-- Fuentes: Orbitron para cabeceras/logo, Manrope para cuerpo/UI -->
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            /* === FUENTES PRINCIPALES (de C2, Orbitron ya estaba en C1 para header) === */
            --font-header: 'Orbitron', sans-serif;
            --font-body: 'Manrope', sans-serif;
            
            /* === PALETA DE COLORES GLASSMORPHISM (de C2) === */
            --color-primary: #C645F9; /* Vibrante Magenta */
            --color-secondary: #000d83; /* Indigo Azul */
            --color-dark: #0D0425; /* Púrpura Oscuro Profundo */
            --color-light: #FFFFFF; /* Blanco Puro */
            
            /* === BACKGROUND & GLASS (de C2) === */
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-hover: rgba(255, 255, 255, 0.15);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.06);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-border-highlight: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.1);
            --glass-hover-shadow: 0 12px 40px rgba(13, 4, 37, 0.15);

            /* === TEXT COLORS (de C2) === */
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 70%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 50%, transparent);
            --color-text-on-gradient: var(--color-light);

            /* === INTERACTIVE STATES (de C2) === */
            --color-hover-accent: rgba(198, 69, 249, 0.15);
            --color-active-accent: rgba(94, 108, 231, 0.25);
            
            /* === BORDER RADIUS (de C2, C1 tenía --border-radius-card: 16px, --border-radius-pill: 9999px, --border-radius-element: 8px) === */
            --border-radius-card: 20px;
            --border-radius-pill: 50px;
            --border-radius-element: 14px;

            /* === TRANSICIONES (de C2) === */
            --transition-speed: 0.3s;
            --transition-ease: ease-in-out;

            /* === VARIABLES ADICIONALES (de C1, si son necesarias) === */
            --color-star-active: #FFD700; /* Para estrellas favoritas, si se mantiene el amarillo */
            --color-placeholder: var(--color-text-muted); /* Unificar con C2 */
            --color-icon-gray: var(--color-text-secondary); /* Unificar con C2 */

            /* Variables para iconos de modelos de C1 */
            --google-blue: #4285F4;
            --google-green: #34A853;
            --google-red: #EA4335;
            --star-black: #2c3e50;
            --star-orange: #f39c12;
            --star-blue-gemini: #4A88DA; /* Similar a un azul Gemini */
            --star-darkgray: #7f8c8d;
            --star-skyblue: #3498db;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            color: var(--color-text-primary);
            background-image: linear-gradient(to bottom left, #ffffff, #e2e7fa);

            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        /* === ANIMATED BACKGROUND (de C2) === */
        .background-container {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            z-index: -1;
            background: linear-gradient(145deg, 
                rgba(250, 248, 248, 0.2) 0%, rgba(150, 170, 255, 0.15) 30%,
                rgba(231, 228, 255, 0.3) 60%, rgba(13, 4, 37, 0.05) 100%);
            overflow: hidden;

        }
        /* === GLASS NAVIGATION (de C2) === */
        .glass-nav {
            position: fixed; top: 25px; left: 50%; transform: translateX(-50%);
            width: calc(100% - 50px); max-width: 1200px; z-index: 1000; /* Z-index alto */
            display: flex; justify-content: space-between; align-items: center;
            padding: 14px 28px; background: var(--glass-bg);
            backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-pill); box-shadow: var(--glass-shadow);
            transition: background var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease);
        }
        .glass-nav:hover { background: var(--glass-bg-hover); }
        .glass-logo {
            font-family: var(--font-header); font-size: 1.5rem; font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
            text-decoration: none;
        }
        /* Efecto hover del logo de C1 (puede ser bueno mantenerlo) */
        .glass-logo::after {
            content: ''; position: absolute; bottom: -3px; /* Ajustar según el padding del nav */
            left: 0; width: 100%; height: px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 2px; opacity: 0; transform: scaleX(0);
            transition: all 0.3s ease;
        }
        .glass-logo:hover::after { opacity: 1; transform: scaleX(1); }

        .glass-auth-buttons { display: flex; gap: 12px; }
        .glass-btn {
            padding: 10px 22px; border-radius: var(--border-radius-pill);
            font-family: var(--font-body); font-size: 0.9rem; font-weight: 600;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            text-decoration: none; border: none;
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            position: relative; overflow: hidden; /* Para efecto de brillo de C1 */
        }
        /* Efecto brillo en botones de C1 */
        .glass-btn::before {
            content: ''; position: absolute; top: 0; left: -100%;
            width: 100%; height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }
        .glass-btn:hover::before { left: 100%; }

        .glass-btn-signin { /* Estilo C2, color de texto de C1 si es mejor */
            background: rgba(255, 255, 255, 0.15); color: var(--color-text-primary);
            border: 1px solid var(--glass-border);
        }
        .glass-btn-signin:hover {
            background: rgba(255, 255, 255, 0.25); border-color: var(--glass-border-highlight);
            transform: translateY(-2px); box-shadow: 0 4px 15px rgba(13,4,37, 0.08);
        }
        .glass-btn-signup { /* Estilo C2 */
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }
        .glass-btn-signup:hover {
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 8px 25px color-mix(in srgb, var(--color-primary) 30%, transparent);
        }

        /* === MAIN CONTENT (Estructura de C2, padding considera nav) === */
        .main-container {
            display: flex; flex-direction: column; align-items: center;
            min-height: 100vh;
            padding: 250px 20px 40px; /* Padding superior para el nav fijo, ajustado */
            width: 100%;
        }
        .header-section { text-align: center; margin-bottom: 40px; z-index: 5; }


        
        .glass-title { /* Estilo de C2 */
            font-family: var(--font-header);
            font-size: clamp(1rem, 4vw, 3rem); /* Ligeramente ajustado */
            font-weight: 600; margin-bottom: 12px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
            letter-spacing: -0.02em; line-height: 1.1;
        }
        .glass-subtitle { /* Estilo de C2 */
            font-size: clamp(0.9rem, 2.2vw, 1.07rem);
            font-weight: 500; color: var(--color-text-secondary);
            max-width: 600px; margin: 0 auto; line-height: 1.6;
        }

        /* === GLASS SEARCH CONTAINER (Estructura de C2) === */
        .glass-search-container { width: 100%; max-width: 760px; position: relative; z-index: 10; }
        .glass-search-card {
            background: var(--glass-bg);
            backdrop-filter: blur(22px); -webkit-backdrop-filter: blur(22px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card); box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
            overflow: visible; position: relative;
        }
        .glass-search-card::before { /* Efecto de brillo en borde (C2) */
            content: ''; position: absolute; top: -1px; left: -1px; right: -1px; bottom: -1px;
            border-radius: inherit; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            padding: 1.5px;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; mask-composite: exclude;
            opacity: 0; transition: opacity var(--transition-speed) var(--transition-ease);
            pointer-events: none; z-index: -1;
        }
        .glass-search-card.focused { /* 'focused' en vez de 'always-focused' de C1 */
            transform: translateY(-3px); box-shadow: var(--glass-hover-shadow);
            border-color: transparent;
        }
        .glass-search-card.focused::before { opacity: 0.7; }

        .glass-search-input { /* Textarea (C2) */
            width: 100%; background: transparent; border: none; outline: none;
            padding: 40px 26px 14px; font-size: 1rem;
            font-family: var(--font-body); font-weight: 500;
            color: var(--color-text-primary); resize: none;
            min-height: 50px; /* Ajuste para C1, C2 tenía 100px */
            line-height: 1.5;
        }
        .glass-search-input::placeholder { color: var(--color-placeholder); font-weight: 400; }

        /* === GLASS TOOLBAR (C2) === */
        .glass-toolbar {
            display: flex; justify-content: space-between; align-items: center;
            padding: 12px 22px; background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
        }
        .glass-model-selector-container { position: relative; } /* Renombrado de C1 para consistencia */
        .glass-model-btn { /* Botón selector de modelo (C2) */
            display: flex; align-items: center; gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 9px 20px; font-size: 0.8rem; font-weight: 600;
            color: var(--color-text-primary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
        }
        .glass-model-btn:hover {
            background: rgba(255, 255, 255, 0.2); border-color: var(--glass-border-highlight);
            transform: translateY(-1px); box-shadow: 0 2px 10px rgba(13,4,37, 0.08);
        }
        .glass-model-btn .fa-chevron-down { transition: transform var(--transition-speed) var(--transition-ease); }
        .glass-model-btn.open .fa-chevron-down { transform: rotate(180deg); }
        /* Icono para el botón de modelo (C2 tiene fa-cogs, C1 no tenía uno específico en el botón) */
        .glass-model-btn > i:first-child { color: var(--color-secondary); font-size:0.9em; }


        .glass-search-actions { display: flex; align-items: center; gap: 10px; } /* Renombrado de C1 para consistencia */
        .glass-action-btn { /* Botones de acción (C2) */
            width: 38px; height: 38px;
            display: flex; align-items: center; justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: 50%; color: var(--color-text-secondary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            font-size: 0.95rem;
        }
        .glass-action-btn:hover {
            background: var(--color-hover-accent); color: var(--color-primary);
            border-color: var(--color-primary); transform: translateY(-2px) scale(1.08);
            box-shadow: 0 4px 15px color-mix(in srgb, var(--color-primary) 20%, transparent);
        }
        .glass-action-btn:active { transform: translateY(0px) scale(1); background: var(--color-active-accent); }
        .pulse-effect { animation: pulseGlow 1.8s ease-in-out infinite; } /* De C2 */
        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 12px color-mix(in srgb, var(--color-primary) 15%, transparent), 0 0 4px var(--color-primary); }
            50% { box-shadow: 0 0 25px color-mix(in srgb, var(--color-primary) 35%, transparent), 0 0 8px var(--color-primary); }
        }


        /* === GLASS CONTENT AREA (para sugerencias, C2) === */
        /* Este reemplaza a #dynamicContentArea de C1 */
        .glass-content-area {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
            padding: 18px; max-height: 380px; /* Altura ajustada */
            overflow-y: auto;
        }
        .glass-content-area::-webkit-scrollbar { width: 6px; }
        .glass-content-area::-webkit-scrollbar-track { background: transparent; }
        .glass-content-area::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.2); border-radius: 3px;}
        .glass-content-area::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.3); }

        /* Indicador de Análisis/Carga (C1 `analyzing-indicator`, C2 `glass-loading`) */
        .glass-loading-indicator { /* Renombrado para fusionar */
            display: flex; align-items: center; justify-content: center;
            padding: 25px; color: var(--color-text-secondary); font-size: 0.9rem;
            font-weight: 500;
        }
        .glass-spinner { /* De C2, C1 tenía fa-spinner */
            width: 20px; height: 20px;
            border: 2.5px solid color-mix(in srgb, var(--color-primary) 20%, transparent);
            border-top-color: var(--color-primary);
            border-radius: 50%; animation: spin 0.8s linear infinite; margin-right: 12px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        /* Contenedores para diferentes tipos de sugerencias (IDs de C1) */
        /* Estilizados con la base de C2 .glass-suggestions */
        .suggestions-panel { /* Clase base para paneles en glass-content-area */
             animation: fadeInUp 0.4s var(--transition-ease) forwards;
        }

        /* === Sugerencias Simples (Lógica C1, Estilo C2 .glass-suggestion-item) === */
        .simple-suggestions-list { display: flex; flex-direction: column; gap: 10px; }
        .simple-suggestion-item {
            background: rgba(255, 255, 255, 0.07);
            backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 14px 18px; cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            font-size: 0.9rem; color: var(--color-text-primary); line-height: 1.5;
        }
        .simple-suggestion-item:hover {
            background: rgba(255, 255, 255, 0.16); border-color: var(--glass-border-highlight);
            transform: translateY(-2px); box-shadow: 0 5px 18px rgba(13,4,37, 0.07);
        }
        .simple-suggestion-item strong { font-weight: 700; color: var(--color-primary); }


        /* === Sugerencias Mejoradas / Favoritos (Lógica C1, Estilo C2 .enhanced-glass-card) === */
        .enhanced-prompt-list { display: flex; flex-direction: column; gap: 16px; }
        .enhanced-prompt-card { /* Reemplaza .enhanced-prompt-item de C1 */
            background: rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element); padding: 18px;
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative; /* Para el botón de selección si se mantiene */
        }
        .enhanced-prompt-card:hover {
            background: rgba(255, 255, 255, 0.12); border-color: var(--glass-border-highlight);
            transform: translateY(-3px); box-shadow: 0 10px 30px rgba(13,4,37, 0.1);
        }
        .enhanced-card-header { /* De C2, adaptado de C1 .enhanced-prompt-item-header */
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 10px;
        }
        .enhanced-card-title { /* De C2, adaptado de C1 h5 */
            font-size: 1.05rem; font-weight: 600; color: var(--color-text-primary);
            line-height: 1.3;
        }
        .glass-star-btn { /* De C2, reemplaza .star-icon de C1 */
            background: rgba(255, 255, 255, 0.1); border: 1px solid var(--glass-border);
            border-radius: 50%; width: 30px; height: 30px;
            display: flex; align-items: center; justify-content: center;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-muted); font-size: 0.85rem;
        }
        .glass-star-btn:hover {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-primary); transform: scale(1.1);
        }
        .glass-star-btn.favorited { /* Clase 'favorited' de C1 */
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-star-active); /* Usar amarillo de C1 */
            border-color: var(--color-primary);
        }
        .glass-star-btn.favorited i { font-weight: 900; /* fas */ }


        .enhanced-prompt-meta-minimal { /* De C1, estilizado */
            display: flex; flex-wrap: wrap; gap: 6px 12px;
            margin-bottom: 10px; font-size: 0.75rem; color: var(--color-text-secondary);
        }
        .meta-item { display: flex; align-items: center; gap: 5px; }
        .meta-item i { font-size: 0.9em; }

        .full-prompt-display { /* De C1 */
            font-size: 0.85rem; line-height: 1.6; color: var(--color-text-secondary);
            margin-bottom: 12px; white-space: pre-wrap; /* Para mantener saltos de línea */
        }
        .prompt-heading { /* De C1 */
            font-weight: 600; color: var(--color-text-primary); display: block; margin-top: 8px;
        }
        .full-prompt-display br + .prompt-heading { margin-top: 8px; }

        .select-prompt-button { /* De C1, estilizado como un botón glass */
            display: inline-flex; align-items: center; gap: 8px;
            padding: 8px 16px; border-radius: var(--border-radius-pill);
            font-family: var(--font-body); font-size: 0.8rem; font-weight: 600;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            text-decoration: none; border: none;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            margin-top: 8px;
        }
        .select-prompt-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }
        .select-prompt-button i { font-size: 0.9em; }

        /* Sección de Favoritos/Populares (Título de C2, lista de C1) */
        .popular-prompts-section-title { /* De C2 "Trending Prompt Starters" */
            color: var(--color-text-primary); margin-bottom: 14px;
            display: flex; align-items: center; gap: 8px;
            font-weight: 600; font-size: 1.05rem;
        }
        .popular-prompts-section-title i { color: var(--color-primary); }


        /* === GLASS DROPDOWN (para modelos, C2 con adaptaciones de C1) === */
        .glass-model-dropdown { /* Renombrado de C1 para consistencia */
            position: absolute; top: calc(100% + 6px); left: 0; 
            width: auto; min-width: 280px; max-width: 350px; /* Ancho ajustado */
            background: var(--glass-bg);
            backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: 0 10px 30px rgba(13,4,37,0.1);
            opacity: 0; visibility: hidden;
            transform: translateY(-8px) scale(0.98);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1100; /* Encima del nav si es necesario, y de otros elementos */
            padding: 8px; /* Padding para la barra de búsqueda y lista */
        }
        .glass-model-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0) scale(1); }
        
        .model-search-input-container { /* De C1, estilizado glass */
            position: relative; margin-bottom: 8px;
        }
        .model-search-input-container .fa-search {
            position: absolute; top: 50%; left: 12px; transform: translateY(-50%);
            color: var(--color-text-muted); font-size: 0.9em;
        }
        #modelSearchInput { /* De C1, estilizado glass */
            width: 100%;
            padding: 10px 12px 10px 34px; /* Padding para el icono */
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);
            border: 1px solid var(--glass-border);
            border-radius: calc(var(--border-radius-element) - 4px); /* Un poco menos que el dropdown */
            font-family: var(--font-body); font-size: 0.85rem;
            color: var(--color-text-primary); outline: none;
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
        #modelSearchInput::placeholder { color: var(--color-placeholder); }
        #modelSearchInput:focus { border-color: var(--glass-border-highlight); }

        .model-list-ul { /* De C1 ul */
            list-style: none; padding: 0; margin: 0;
            max-height: 250px; overflow-y: auto;
        }
        /* Scrollbar para la lista de modelos */
        .model-list-ul::-webkit-scrollbar { width: 5px; }
        .model-list-ul::-webkit-scrollbar-track { background: transparent; }
        .model-list-ul::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.25); border-radius: 3px;}
        .model-list-ul::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.35); }

        .model-list-ul li { /* De C1 li, adaptado a C2 .glass-dropdown-item */
            padding: 10px 14px;
            color: var(--color-text-primary); font-weight: 500; font-size: 0.85rem;
            cursor: pointer; transition: background-color var(--transition-speed) var(--transition-ease);
            border-radius: calc(var(--border-radius-element) - 6px); /* Aún más pequeño */
            display: flex; align-items: center; gap: 10px;
            margin-bottom: 4px; /* Espacio entre items */
        }
        .model-list-ul li:last-child { margin-bottom: 0; }
        .model-list-ul li:hover { background: var(--color-hover-accent); color: var(--color-primary); }
        .model-list-ul li.selected-model-item { /* De C1 */
            background-color: var(--color-active-accent);
            color: var(--color-primary); font-weight: 600;
        }
        .model-list-ul li img, .model-list-ul li .model-icon { /* De C1 */
            width: 18px; height: 18px; object-fit: contain; flex-shrink: 0;
        }
        .model-list-ul li .model-icon { font-size: 1em; text-align: center; }
        /* Clases de color para iconos de modelos de C1 */
        .model-icon.google-blue { color: var(--google-blue); }
        .model-icon.google-green { color: var(--google-green); }
        .model-icon.google-red { color: var(--google-red); }
        .model-icon.star-black { color: var(--star-black); }
        .model-icon.star-orange { color: var(--star-orange); }
        .model-icon.star-blue-gemini { color: var(--star-blue-gemini); }
        .model-icon.star-darkgray { color: var(--star-darkgray); }
        .model-icon.star-skyblue { color: var(--star-skyblue); }


        /* === FOOTER (de C2) === */
        .glass-footer {
            margin-top: auto; padding: 40px 20px 25px; text-align: center;
            color: var(--color-text-muted); font-size: 0.8rem;
        }
        .glass-footer i.fa-heart { color: var(--color-primary); }


        /* === ANIMACIONES (C2 fadeInUp, C1 puede tener otros) === */
        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px) scale(0.99); } to { opacity: 1; transform: translateY(0) scale(1); } }
        /* C1 tenía fadeInDown, que podría usarse para el dropdown de modelos si se desea */
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-10px); } to { opacity: 1; transform: translateY(0); } }

        /* Utilidad */
        .hidden { display: none !important; }

        /* === RESPONSIVE DESIGN (de C2, revisar con C1) === */
        @media (max-width: 768px) {
            .glass-nav { width: calc(100% - 30px); padding: 12px 20px; }
            .glass-logo { font-size: 1.3rem; }
            .glass-btn { padding: 8px 18px; font-size: 0.85rem; }

            .main-container { padding: 100px 16px 30px; }
            .glass-title { font-size: clamp(2rem, 6.5vw, 2.8rem); }
            .glass-subtitle { font-size: clamp(0.85rem, 3vw, 1rem); }
            
            .glass-toolbar { flex-direction: column; gap: 14px; padding: 14px; align-items: stretch; }
            .glass-model-selector-container { width: 100%; }
            .glass-model-btn { justify-content: center; }
            .glass-search-actions { justify-content: space-around; width: 100%; }
            
            .glass-search-input { min-height: 50px; padding: 16px 20px 10px;}
            .glass-content-area { padding: 16px; max-height: 300px;}
            .enhanced-prompt-card, .simple-suggestion-item { padding: 16px; }
            .glass-model-dropdown { min-width: calc(100% - 40px); left: 50%; transform: translateX(-50%) translateY(-8px) scale(0.98); }
            .glass-model-dropdown.visible { transform: translateX(-50%) translateY(0) scale(1); }

        }
         @media (max-width: 480px) {
            .glass-auth-buttons { gap: 8px; }
            .glass-btn { padding: 7px 12px; font-size: 0.8rem; }
            .glass-action-btn { width: 36px; height: 36px; font-size: 0.9rem;}
            .glass-search-actions { gap: 8px;}
         }

    </style>
</head>
<body>
    <!-- Animated Background (C2) -->
    <div class="background-container">
        <div class="floating-shapes">
            <div class="shape"></div><div class="shape"></div><div class="shape"></div><div class="shape"></div>
        </div>
    </div>

    <!-- Glass Navigation (C2) -->
    <nav class="glass-nav">
        <a href="#" class="glass-logo">allhub</a>
        <div class="glass-auth-buttons">
            <a href="#" class="glass-btn glass-btn-signin">Sign In</a>
            <a href="#" class="glass-btn glass-btn-signup">Sign Up Free</a>
        </div>
    </nav>

    <!-- Main Container (C2) -->
    <main class="main-container">
        <!-- Header Section (C2) -->
        <section class="header-section">
            <h1 class="glass-title">Prompt like a pro</h1>
            <!-- Subtítulo de C1 era "Craft the perfect prompts with intelligent suggestions" -->
            <!-- Subtítulo de C2 era "Craft the perfect prompts with intelligent suggestions powered by advanced AI" -->
            <p class="glass-subtitle">Craft the perfect prompts with intelligent suggestions</p>
        </section>

        <!-- Glass Search Container (C2 estructura, IDs de C1 donde sea posible para JS) -->
        <div class="glass-search-container" id="searchAreaContainer"> <!-- ID de C1 -->
            <div class="glass-search-card" id="searchBarCard"> <!-- ID de C1 -->
                <textarea 
                class="glass-search-input" 
                id="searchInput" 
                placeholder="Type your prompt here..."></textarea>

                <div class="glass-toolbar">
                    <div class="glass-model-selector-container"> <!-- Clase de C1, wrapper para el botón -->
                        <button class="glass-model-btn" id="modelSelectorBtn"> <!-- ID de C1 -->
                            <i class="fas fa-cogs"></i>
                            <span id="currentModelName">Model</span> <!-- ID de C1 -->
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>

                    <div class="glass-search-actions" id="searchActionsContainer"> <!-- ID nuevo, wrapper para C1 .search-actions -->
                        <button class="glass-action-btn" id="micBtn" title="Voice Input"><i class="fas fa-microphone"></i></button>
                        <button class="glass-action-btn" id="guidedCreateBtn" title="Guided Creation"><i class="fas fa-wand-magic-sparkles"></i></button> <!-- Mapeado a magicBtn de C2 -->
                        <button class="glass-action-btn" id="copyBtn" title="Copy Prompt"><i class="fas fa-copy"></i></button>
                        <button class="glass-action-btn pulse-effect" id="sendBtn" title="Send Prompt"><i class="fas fa-paper-plane"></i></button>
                    </div>
                </div>
                
                <!-- Área de contenido dinámico de C2, con IDs de C1 para sus hijos -->
                <div class="glass-content-area hidden" id="dynamicContentArea"> <!-- ID de C1 -->
                    <div class="glass-loading-indicator hidden" id="analyzingIndicator"> <!-- ID de C1 -->
                        <div class="glass-spinner"></div>
                        Analyzing your prompt...
                    </div>
                    <!-- Contenedor para sugerencias simples -->
                    <div class="suggestions-panel hidden" id="simplePromptSuggestionsContainer"> <!-- ID de C1 -->
                        <div class="simple-suggestions-list">
                            <!-- Los items se generan por JS -->
                        </div>
                    </div>
                    <!-- Contenedor para sugerencias mejoradas -->
                    <div class="suggestions-panel hidden" id="enhancedPromptSuggestionsContainer"> <!-- ID de C1 -->
                         <div class="enhanced-prompt-list">
                            <!-- Los items se generan por JS -->
                         </div>
                    </div>
                    <!-- Sección de prompts populares/favoritos -->
                    <div class="suggestions-panel hidden" id="favoritePromptsSection"> <!-- ID de C1 -->
                        <h3 class="popular-prompts-section-title">
                            <i class="fas fa-star"></i> <!-- Icono de C1, C2 tenía fa-fire -->
                            Popular Prompts
                        </h3>
                        <div class="enhanced-prompt-list" id="favoritePromptsList"> <!-- ID de C1 -->
                            <!-- Los items se generan por JS -->
                        </div>
                    </div>
                </div>
            </div>
            
            <!-- Dropdown de Modelos (HTML de C1, estilizado como C2 .glass-dropdown) -->
            <div class="glass-model-dropdown hidden" id="modelDropdownList"> <!-- ID de C1 -->
                <div class="model-search-input-container"> <!-- De C1 -->
                    <i class="fas fa-search"></i>
                    <input type="text" id="modelSearchInput" placeholder="Search models"> <!-- ID de C1 -->
                </div>
                <ul class="model-list-ul"></ul> <!-- ul de C1 -->
            </div>
        </div>

        <footer class="glass-footer">
            Copyright © <span id="copyrightYear">2024</span> All Hub. All rights reserved.
        </footer>
    </main>

    <script>
        // === SELECTORES DE DOM (actualizados a la nueva estructura) ===
        const searchAreaContainer = document.getElementById('searchAreaContainer');
        const searchBarCard = document.getElementById('searchBarCard');
        const searchInput = document.getElementById('searchInput');
        
        const guidedCreateBtn = document.getElementById('guidedCreateBtn'); // Antes magicBtn
        const micBtn = document.getElementById('micBtn');
        const copyBtn = document.getElementById('copyBtn');
        const sendBtn = document.getElementById('sendBtn');

        const dynamicContentArea = document.getElementById('dynamicContentArea'); // Antes contentArea
        const analyzingIndicator = document.getElementById('analyzingIndicator'); // Antes loadingState
        const simplePromptSuggestionsContainer = document.getElementById('simplePromptSuggestionsContainer');
        const enhancedPromptSuggestionsContainer = document.getElementById('enhancedPromptSuggestionsContainer');
        const favoritePromptsSection = document.getElementById('favoritePromptsSection'); // Antes popularPrompts
        const favoritePromptsList = document.getElementById('favoritePromptsList');

        const modelSelectorBtn = document.getElementById('modelSelectorBtn'); // Antes modelBtn
        const currentModelNameSpan = document.getElementById('currentModelName'); // Antes currentModel
        const modelDropdownList = document.getElementById('modelDropdownList'); // Antes modelDropdown
        const modelSearchInput = document.getElementById('modelSearchInput');
        const modelListUl = modelDropdownList.querySelector('.model-list-ul');

        // === ESTADO Y VARIABLES GLOBALES (de C1) ===
        let isGuidedModeActive = false;
        let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHub')) || [];
        let blockSuggestionsOnNextFocus = false;
        let inputDebounceTimeout; // Para el debounce del input

        // === DATOS DE MODELOS (de C1, más detallados) ===
        const modelsData = [
            { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
            { id: "anthropic-claude-4", displayName: "Anthropic: Claude 4", shortName: "Claude 4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-orange" },
            { id: "google-gemini-1.5", displayName: "Google: Gemini 1.5", shortName: "Gemini 1.5", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-blue-gemini" },
            { id: "mistral-large", displayName: "Mistral: Large", shortName: "Mistral Large", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-darkgray" },
            { id: "meta-llama-3", displayName: "Meta: LLaMA 3", shortName: "LLaMA 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-skyblue" },
            { id: "mistral-devstral-small-free", displayName: "Mistral: Devstral Small (free)", shortName: "Devstral Sm", iconType: "img", iconUrl: "data:image/svg+xml,%3Csvg xmlns='http://www.w3.org/2000/svg' viewBox='0 0 24 24' fill='%23FF8C00'%3E%3Cpath d='M12 2L2 7l10 5 10-5-10-5zM2 17l10 5 10-5M2 12l10 5 10-5'/%3E%3C/svg%3E" },
            { id: "google-gemma-3n-4b-free", displayName: "Google: Gemma 3n 4B (free)", shortName: "Gemma 3n", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "google-blue" }
        ];
        
        // === FUNCIONES UTILITARIAS (de C1 y C2) ===
        function debounce(func, delay) {
            let timeout;
            const debounced = function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
            debounced.cancel = function() { clearTimeout(timeout); };
            return debounced;
        }

        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            let scrollHeight = textarea.scrollHeight;
            // Mantener un min-height si es necesario (ej. 60px como en CSS)
            const minHeight = parseInt(window.getComputedStyle(textarea).minHeight, 10) || 60;
            textarea.style.height = Math.max(minHeight, scrollHeight) + 'px';
            textarea.style.overflowY = (scrollHeight > minHeight && scrollHeight > textarea.clientHeight) ? 'auto' : 'hidden';
        }
        
        function formatFullPromptForDisplay(fullPromptText) {
            if (!fullPromptText) return "";
            let html = fullPromptText.replace(/&/g, "&").replace(/</g, "<").replace(/>/g, ">"); // Escapar HTML básico
            html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim, (match) => `<span class="prompt-heading">${match.trim()}</span>`);
            return html.replace(/\n/g, '<br>'); // Reemplazar saltos de línea con <br>
        }

        // === MANEJO DE VISIBILIDAD DE CONTENIDO DINÁMICO (Adaptado de C1 y C2) ===
        function updateDynamicContentAreaVisibility() {
            const isSimpleVisible = !simplePromptSuggestionsContainer.classList.contains('hidden') && simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')?.children.length > 0;
            const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')?.children.length > 0;
            const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && favoritePromptsList.children.length > 0;
            const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden');

            if (isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible) {
                dynamicContentArea.classList.remove('hidden');
            } else {
                dynamicContentArea.classList.add('hidden');
            }
        }

        function hideAllDynamicContentExcept(exceptContainer = null) {
            [simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => {
                if (container && container !== exceptContainer) {
                    container.classList.add('hidden');
                    if (container !== analyzingIndicator && container.id !== 'simplePromptSuggestionsContainer' && container.id !== 'enhancedPromptSuggestionsContainer' && container.id !== 'favoritePromptsSection' ) {
                         // No limpiar el contenido de los contenedores principales, solo sus listas internas
                    } else if (container.id === 'simplePromptSuggestionsContainer' && container.querySelector('.simple-suggestions-list')) {
                        container.querySelector('.simple-suggestions-list').innerHTML = '';
                    } else if (container.id === 'enhancedPromptSuggestionsContainer' && container.querySelector('.enhanced-prompt-list')) {
                        container.querySelector('.enhanced-prompt-list').innerHTML = '';
                    } else if (container.id === 'favoritePromptsSection' && favoritePromptsList) {
                        favoritePromptsList.innerHTML = '';
                    }
                }
            });
             if (exceptContainer) exceptContainer.classList.remove('hidden');
        }


        // === LÓGICA DE MODELOS (de C1) ===
        function setDefaultModel() {
            if (modelsData.length > 0) {
                const defaultModel = modelsData[0];
                currentModelNameSpan.textContent = defaultModel.shortName;
                currentModelNameSpan.dataset.selectedModelId = defaultModel.id;
            } else {
                currentModelNameSpan.textContent = "Model";
                delete currentModelNameSpan.dataset.selectedModelId;
            }
        }

        function populateModelDropdown() {
            modelListUl.innerHTML = '';
            const searchTerm = modelSearchInput.value.toLowerCase();
            const currentSelectedModelId = currentModelNameSpan.dataset.selectedModelId;

            modelsData.filter(model => model.displayName.toLowerCase().includes(searchTerm)).forEach(model => {
                const li = document.createElement('li');
                li.dataset.modelId = model.id;
                li.dataset.modelShortName = model.shortName;
                li.dataset.modelDisplayName = model.displayName;

                let iconElement;
                if (model.iconType === "fa") {
                    iconElement = document.createElement('i');
                    iconElement.className = `model-icon ${model.iconClass} ${model.iconColorClass || ''}`;
                } else if (model.iconType === "img") {
                    iconElement = document.createElement('img');
                    iconElement.src = model.iconUrl;
                    iconElement.alt = model.displayName.split(':')[0];
                    iconElement.onerror = () => { iconElement.style.display = 'none'; };
                } else { // Placeholder si no hay icono
                    iconElement = document.createElement('span');
                    iconElement.className = 'model-icon icon-placeholder';
                    iconElement.textContent = '●'; // O un icono FA genérico
                }
                li.appendChild(iconElement);

                const span = document.createElement('span');
                span.textContent = model.displayName;
                li.appendChild(span);

                if (currentSelectedModelId && currentSelectedModelId === model.id) {
                    li.classList.add('selected-model-item');
                }

                li.addEventListener('click', (e) => {
                    e.stopPropagation();
                    currentModelNameSpan.textContent = model.shortName;
                    currentModelNameSpan.dataset.selectedModelId = model.id;

                    modelListUl.querySelectorAll('li').forEach(item => item.classList.remove('selected-model-item'));
                    li.classList.add('selected-model-item');

                    modelDropdownList.classList.remove('visible');
                    modelDropdownList.classList.add('hidden'); // Usar hidden de C1
                    modelSelectorBtn.classList.remove('open');
                    
                    blockSuggestionsOnNextFocus = true;
                    searchInput.focus();
                    hideAllDynamicContentExcept();
                    updateDynamicContentAreaVisibility();
                });
                modelListUl.appendChild(li);
            });
        }

        // === LÓGICA DE FAVORITOS Y TARJETAS DE PROMPT (de C1, adaptada a estilos C2) ===
        function renderFavoritePrompts() {
            if (isGuidedModeActive || modelDropdownList.classList.contains('visible')) return;

            hideAllDynamicContentExcept(favoritePromptsSection);
            favoritePromptsList.innerHTML = ''; // Limpiar lista existente

            if (favoritePrompts.length > 0 && searchInput.value.length === 0) {
                favoritePrompts.forEach(promptData => {
                    const item = createPromptCard(promptData, false, true);
                    favoritePromptsList.appendChild(item);
                });
                favoritePromptsSection.classList.remove('hidden');
            } else {
                favoritePromptsSection.classList.add('hidden');
            }
            updateDynamicContentAreaVisibility();
        }

        function toggleFavorite(promptData, starIconElement) {
            const promptId = promptData.id || promptData.title;
            let index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptId);
            let wasFavorited = index > -1;

            if (wasFavorited) {
                favoritePrompts.splice(index, 1);
            } else {
                const favData = { 
                    id: promptData.id, 
                    title: promptData.title, 
                    fullPrompt: promptData.fullPrompt, 
                    metaMinimal: promptData.metaMinimal, 
                    description: promptData.description || "N/A" 
                };
                favoritePrompts.push(favData);
            }
            localStorage.setItem('favoritePromptsAllHub', JSON.stringify(favoritePrompts));
            
            document.querySelectorAll(`.glass-star-btn[data-prompt-id="${promptId}"]`).forEach(starBtn => {
                 const iElement = starBtn.querySelector('i');
                if (wasFavorited) {
                    starBtn.classList.remove('favorited');
                    if(iElement) { iElement.classList.remove('fas'); iElement.classList.add('far'); }
                } else {
                    starBtn.classList.add('favorited');
                    if(iElement) { iElement.classList.remove('far'); iElement.classList.add('fas'); }
                }
            });

            if (!isGuidedModeActive && searchInput.value === '') {
                renderFavoritePrompts(); // Re-renderizar lista de favoritos si está visible
            }
        }
        
        function createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) {
            const card = document.createElement('div');
            card.classList.add('enhanced-prompt-card'); // Estilo C2
            if (isFavoriteListItem) card.classList.add('favorite-list-item-card');
            const uniqueId = promptData.id || promptData.title.replace(/\s+/g, '-').toLowerCase();
            card.dataset.promptId = uniqueId;

            const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === (promptData.id || promptData.title));
            const starIconClass = isFavorited ? 'fas fa-star' : 'far fa-star';

            let metaMinimalHTML = '';
            if (promptData.metaMinimal) {
                metaMinimalHTML = '<div class="enhanced-prompt-meta-minimal">';
                if (promptData.metaMinimal.inputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-in-alt"></i> ${promptData.metaMinimal.inputTokens}</span>`;
                if (promptData.metaMinimal.outputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-out-alt"></i> ${promptData.metaMinimal.outputTokens}</span>`;
                if (promptData.metaMinimal.time) metaMinimalHTML += `<span class="meta-item"><i class="far fa-clock"></i> ${promptData.metaMinimal.time}</span>`;
                if (promptData.metaMinimal.reuse) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-redo-alt"></i> ${promptData.metaMinimal.reuse}</span>`;
                metaMinimalHTML += '</div>';
            }

            const buttonText = isFavoriteListItem ? 'Use Favorite' : 'Select Prompt';

            card.innerHTML = `
                <div class="enhanced-card-header">
                    <h5 class="enhanced-card-title">${promptData.title || "Prompt"}</h5>
                    <button class="glass-star-btn" title="Mark as favorite" data-prompt-id="${uniqueId}">
                        <i class="${starIconClass}"></i>
                    </button>
                </div>
                ${metaMinimalHTML}
                <div class="full-prompt-display">${formatFullPromptForDisplay(promptData.fullPrompt || promptData.title)}</div>
                <button class="select-prompt-button">
                    <i class="fas fa-check-circle"></i> ${buttonText}
                </button>
            `;
            card.querySelector('.glass-star-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(promptData, e.currentTarget.querySelector('i'));
            });
            card.querySelector('.select-prompt-button').addEventListener('click', () => {
                let promptTextToSet = promptData.fullPrompt || promptData.title;
                // Limpieza específica de C1
                if (promptData.id === "few-shot-summarize-pdf") {
                    promptTextToSet = promptTextToSet.replace(/\n{2,}/g, '\n'); 
                }
                searchInput.value = promptTextToSet;
                autoResizeTextarea(searchInput);
                exitGuidedModeIfNeeded();
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
                blockSuggestionsOnNextFocus = true;
                searchInput.focus();
            });
            return card;
        }

        // === LÓGICA DE MODO GUIADO (de C1) ===
        function displayGuidedPromptList(suggestions) {
            isGuidedModeActive = true;
            hideAllDynamicContentExcept(enhancedPromptSuggestionsContainer);
            const listContainer = enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list');
            if (!listContainer) return;
            listContainer.innerHTML = '';


            if (suggestions && suggestions.length > 0) {
                suggestions.forEach(suggData => {
                    const card = createPromptCard(suggData);
                    listContainer.appendChild(card);
                });
                enhancedPromptSuggestionsContainer.classList.remove('hidden');
            }
            searchBarCard.classList.add('focused'); // Usar clase 'focused' de C2
            updateDynamicContentAreaVisibility();
        }

        function generateGuidedPrompts() {
            // Datos de ejemplo de C1
            let suggestions = [];
            suggestions.push({ title: "Improve with Few-Shot Examples", id: "few-shot-summarize-pdf", fullPrompt: `Write a blog article about how to summarize a PDF. Follow the style and structure of these examples:\n\nExample 1:\nTitle: "How to Create a Mind Map"\nIntroduction: Mind maps are great for organizing thoughts. This article explains simple steps to create one.\nStep 1: Choose a central topic...\nStep 2: Add branches for subtopics...\nConclusion: Mind mapping is easy and fun. Try it today!\n\nExample 2:\nTitle: "How to Take Better Notes"\nIntroduction: Good notes help you study better. Here’s how to do it.\nStep 1: Use a clear format...\nStep 2: Highlight key points...\nConclusion: Better notes lead to better learning. Start now! Write a 400-word blog article titled "How to Summarize a PDF Effectively" with an introduction, 3 practical steps, and a conclusion. Use a clear and simple tone for students.`, metaMinimal: { inputTokens: "170", outputTokens: "~400-600", time: "~1-2s", reuse: "High" }, description: "Uses few-shot examples." });
            suggestions.push({ title: "Enhance with Chain of Thought", id: "cot-summarize-pdf", fullPrompt: `Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process as follows: Identify the key components of a blog article (e.g., introduction, main steps, conclusion). List 3 practical methods for summarizing a PDF (e.g., using software, manual highlighting, or online tools). For each method, explain one benefit and one challenge. Structure the article with a clear introduction, a section for each method, and a conclusion encouraging readers to try summarizing. Use a professional yet accessible tone for small business owners. Title the article "Mastering PDF Summarization: A Step-by-Step Guide."`, metaMinimal: { inputTokens: "195", outputTokens: "~500-800", time: "~1-3s", reuse: "High" }, description: "Uses Chain of Thought." });
            return suggestions;
        }

        async function handleGuidedCreateClick() {
            if (modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }
            hideAllDynamicContentExcept(analyzingIndicator);
            analyzingIndicator.classList.remove('hidden');
            searchBarCard.classList.add('focused'); // Usar clase 'focused' de C2
            updateDynamicContentAreaVisibility();

            await new Promise(resolve => setTimeout(resolve, 1200)); // Simular carga
            const guidedPrompts = generateGuidedPrompts();
            displayGuidedPromptList(guidedPrompts);
        }

        function exitGuidedModeIfNeeded() {
            if (isGuidedModeActive) {
                isGuidedModeActive = false;
                hideAllDynamicContentExcept();
                if (document.activeElement !== searchInput) { // Solo quitar 'focused' si el input no tiene el foco
                    searchBarCard.classList.remove('focused');
                }
                if (searchInput.value === '') renderFavoritePrompts();
                updateDynamicContentAreaVisibility();
            }
        }

        // === LÓGICA DE SUGERENCIAS SIMPLES (de C1) ===
        function generateSimpleSuggestions(inputText) {
            const baseSuggestions = [
                "Write a blog post about how to automatically extract key points from a PDF",
                "Write an article about summarizing research papers using AI",
                "Write a tweet about the best free tools to summarize PDFs",
                "Write a blog post about building a PDF summarizer with Python",
                "Write a blog article about summarizing legal documents without reading them",
                "Write a blog post about syncing summarized PDF meeting notes with Notion"
            ];
            if (!inputText) return [];
            const lowerInput = inputText.toLowerCase();
            return baseSuggestions.filter(sugg => sugg.toLowerCase().includes(lowerInput)).slice(0, 4);
        }

        const handleSimpleInput = debounce(function() {
            if (isGuidedModeActive || modelDropdownList.classList.contains('visible')) {
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
                return;
            }

            const inputText = searchInput.value;
            hideAllDynamicContentExcept(simplePromptSuggestionsContainer);
            const listContainer = simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list');
            if (!listContainer) return;
            listContainer.innerHTML = '';


            if (inputText.length < 1) { // C1 tenía < 3, C2 < 2. Usaremos < 1 para mostrar favoritos si está vacío.
                simplePromptSuggestionsContainer.classList.add('hidden');
                if (inputText.length === 0) renderFavoritePrompts();
                updateDynamicContentAreaVisibility();
                return;
            }

            const suggestions = generateSimpleSuggestions(inputText);
            if (suggestions.length > 0) {
                simplePromptSuggestionsContainer.classList.remove('hidden');
                suggestions.forEach(suggText => {
                    const item = document.createElement('div');
                    item.classList.add('simple-suggestion-item');
                    const matchIndex = suggText.toLowerCase().indexOf(inputText.toLowerCase());
                    if (matchIndex > -1) {
                        item.innerHTML = suggText.substring(0, matchIndex) +
                                         `<strong>${suggText.substring(matchIndex, matchIndex + inputText.length)}</strong>` +
                                         suggText.substring(matchIndex + inputText.length);
                    } else {
                        item.textContent = suggText;
                    }
                    item.addEventListener('click', () => {
                        searchInput.value = suggText;
                        autoResizeTextarea(searchInput);
                        hideAllDynamicContentExcept();
                        updateDynamicContentAreaVisibility();
                        blockSuggestionsOnNextFocus = true;
                        searchInput.focus();
                    });
                    listContainer.appendChild(item);
                });
            } else {
                simplePromptSuggestionsContainer.classList.add('hidden');
            }
            updateDynamicContentAreaVisibility();
        }, 300); // Debounce de C1 y C2

        // === MANEJADORES DE EVENTOS PRINCIPALES ===
        searchInput.addEventListener('input', () => {
            autoResizeTextarea(searchInput);
            if (isGuidedModeActive && searchInput.value === '') {
                exitGuidedModeIfNeeded();
            } else if (!isGuidedModeActive && !modelDropdownList.classList.contains('visible')) {
                blockSuggestionsOnNextFocus = false; // Permitir sugerencias en el próximo focus si es necesario
                handleSimpleInput(); // Llamar a la función debounced
            } else if (modelDropdownList.classList.contains('visible')) {
                hideAllDynamicContentExcept(); // Ocultar todo si el dropdown de modelos está abierto
                updateDynamicContentAreaVisibility();
            }
        });

        searchInput.addEventListener('focus', () => {
            searchBarCard.classList.add('focused');
            autoResizeTextarea(searchInput);

            if (modelDropdownList.classList.contains('visible') || isGuidedModeActive) {
                return; // No hacer nada si el dropdown o el modo guiado están activos
            }
            
            // Lógica de C1 para mostrar favoritos o sugerencias simples al enfocar
            if (searchInput.value === '') {
                renderFavoritePrompts();
            } else {
                if (blockSuggestionsOnNextFocus) {
                    blockSuggestionsOnNextFocus = false; // Resetear flag
                } else {
                    handleSimpleInput.cancel(); // Cancelar debounce anterior si existe
                    handleSimpleInput(); // Ejecutar inmediatamente o con nuevo debounce
                }
            }
        });
        
        let blurTimeout; // De C2, para manejar el blur correctamente
        searchInput.addEventListener('blur', () => {
            blurTimeout = setTimeout(() => {
                const activeEl = document.activeElement;
                const isFocusWithinRelevantArea = 
                    searchBarCard.contains(activeEl) ||
                    modelDropdownList.contains(activeEl) ||
                    (dynamicContentArea && dynamicContentArea.contains(activeEl));

                if (!isFocusWithinRelevantArea) {
                    searchBarCard.classList.remove('focused');
                    if (!modelDropdownList.classList.contains('visible') && !isGuidedModeActive) {
                        hideAllDynamicContentExcept();
                        updateDynamicContentAreaVisibility();
                    }
                }
            }, 150);
        });
        
        // Prevenir blur timeout si el foco se mueve a elementos relevantes (de C2)
        [modelDropdownList, dynamicContentArea].forEach(el => {
            if(el) { // Verificar que el elemento exista
                el.addEventListener('focusin', () => clearTimeout(blurTimeout));
                el.addEventListener('mousedown', () => clearTimeout(blurTimeout));
            }
        });


        modelSelectorBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const isCurrentlyHidden = modelDropdownList.classList.contains('hidden');
            
            exitGuidedModeIfNeeded(); // Salir del modo guiado si está activo
            hideAllDynamicContentExcept(); // Ocultar otras áreas dinámicas

            if (isCurrentlyHidden) {
                // Posicionamiento del dropdown (de C1, adaptado)
                const cardRect = searchBarCard.getBoundingClientRect();
                const containerRect = searchAreaContainer.getBoundingClientRect();
                const buttonRect = modelSelectorBtn.getBoundingClientRect();
                const spaceBetweenCardAndDropdown = 8;

                // Calcular top relativo al searchAreaContainer
                modelDropdownList.style.top = (buttonRect.bottom - containerRect.top + spaceBetweenCardAndDropdown - (cardRect.bottom - buttonRect.bottom)) + 'px';

                // Calcular left relativo al searchAreaContainer
                modelDropdownList.style.left = (buttonRect.left - containerRect.left) + 'px';
                modelDropdownList.style.width = buttonRect.width + 'px'; // Ancho igual al botón o min-width de CSS

                modelDropdownList.classList.remove('hidden');
                modelDropdownList.classList.add('visible'); // Clase 'visible' de C2
                modelSelectorBtn.classList.add('open');
                modelSearchInput.value = '';
                populateModelDropdown();
                modelSearchInput.focus();
            } else {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }
            updateDynamicContentAreaVisibility();
        });

        modelSearchInput.addEventListener('input', populateModelDropdown);
        modelSearchInput.addEventListener('click', (e) => e.stopPropagation()); // Evitar que el click cierre el dropdown

        // Eventos para botones de acción
        guidedCreateBtn.addEventListener('click', handleGuidedCreateClick);
        if (micBtn) { micBtn.addEventListener('click', () => console.log("Micrófono presionado (implementar funcionalidad)")); }
        copyBtn.addEventListener('click', (e) => { 
            e.stopPropagation(); 
            if(searchInput.value) navigator.clipboard.writeText(searchInput.value)
                .then(() => console.log('Prompt Copied!'))
                .catch(err => console.error('Failed to copy prompt: ', err)); 
        });
        sendBtn.addEventListener('click', (e) => { 
            e.stopPropagation(); 
            if(searchInput.value) console.log('Prompt sent: ' + searchInput.value + ' (Model: ' + currentModelNameSpan.textContent + ')');
        });

        // Click global para cerrar dropdowns/paneles (combinado C1 y C2)
        document.addEventListener('click', (event) => {
            // Cerrar dropdown de modelos
            if (!modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target) && modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }

            // Cerrar paneles de contenido dinámico si el click es fuera del área de búsqueda completa
            const isClickInsideSearchArea = searchAreaContainer.contains(event.target) || 
                                            modelDropdownList.contains(event.target);

            if (!isClickInsideSearchArea) {
                 searchBarCard.classList.remove('focused'); // Quitar foco de la tarjeta principal
                if (!isGuidedModeActive && !modelDropdownList.classList.contains('visible')) {
                    hideAllDynamicContentExcept();
                    if(searchInput.value === '') renderFavoritePrompts(); // Volver a mostrar favoritos si el input está vacío
                    updateDynamicContentAreaVisibility();
                }
            }
        });
        
        // === INICIALIZACIÓN ===
        document.addEventListener('DOMContentLoaded', () => {
            autoResizeTextarea(searchInput);
            setDefaultModel();
            document.getElementById('copyrightYear').textContent = new Date().getFullYear();

            if (searchInput.value === '') {
                renderFavoritePrompts();
            }
            if (document.activeElement === searchInput) {
                searchBarCard.classList.add('focused');
                if (searchInput.value !== '') { 
                    // handleSimpleInput(); // Opcional: mostrar sugerencias al cargar si está enfocado y con texto
                }
            }
            // Asegurar que los contenedores de listas para JS existan
            if (!simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')) {
                const list = document.createElement('div');
                list.className = 'simple-suggestions-list';
                simplePromptSuggestionsContainer.appendChild(list);
            }
            if (!enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')) {
                 const list = document.createElement('div');
                list.className = 'enhanced-prompt-list';
                enhancedPromptSuggestionsContainer.appendChild(list);
            }
        });

    </script>
</body>
</html>