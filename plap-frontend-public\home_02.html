<!DOCTYPE html>
<html lang="es">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Prompt Like a Pro - Fusión Mejorada</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Manrope:wght@400;500;600;700;800&family=Orbitron:wght@400;700;900&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.5.1/css/all.min.css">
    <style>
        :root {
            --font-header: 'Orbitron', sans-serif;
            --font-body: '<PERSON>rope', sans-serif;
            --color-primary: #C645F9;
            --color-secondary: #000d83;
            --color-dark: #0D0425;
            --color-light: #FFFFFF;
            --glass-bg: rgba(255, 255, 255, 0.08);
            --glass-bg-hover: rgba(255, 255, 255, 0.15);
            --glass-bg-toolbar: rgba(255, 255, 255, 0.06);
            --glass-border: rgba(255, 255, 255, 0.15);
            --glass-border-highlight: rgba(255, 255, 255, 0.3);
            --glass-shadow: 0 8px 32px rgba(13, 4, 37, 0.1);
            --glass-hover-shadow: 0 12px 40px rgba(13, 4, 37, 0.15);
            --color-text-primary: var(--color-dark);
            --color-text-secondary: color-mix(in srgb, var(--color-dark) 70%, transparent);
            --color-text-muted: color-mix(in srgb, var(--color-dark) 50%, transparent);
            --color-text-on-gradient: var(--color-light);
            --color-hover-accent: rgba(198, 69, 249, 0.15);
            --color-active-accent: rgba(94, 108, 231, 0.25);
            --border-radius-card: 20px;
            --border-radius-pill: 50px;
            --border-radius-element: 14px;
            --transition-speed: 0.3s;
            --transition-ease: ease-in-out;
            --color-star-active: #FFD700;
            --color-placeholder: var(--color-text-muted);
            --color-icon-gray: var(--color-text-secondary);
            --google-blue: #4285F4;
            --google-green: #34A853;
            --google-red: #EA4335;
            --star-black: #2c3e50;
            --star-orange: #f39c12;
            --star-blue-gemini: #4A88DA;
            --star-darkgray: #7f8c8d;
            --star-skyblue: #3498db;
        }

        * { margin: 0; padding: 0; box-sizing: border-box; }

        body {
            font-family: var(--font-body);
            min-height: 100vh;
            overflow-x: hidden;
            position: relative;
            color: var(--color-text-primary);
            background: linear-gradient(135deg, #F5F5F5 0%, #E8E8F0 30%, #DDD9E8 70%, #D5D5D5 100%);
            background-image: linear-gradient(to bottom left, #ffffff, #f2d7fa);
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
        }

        .background-container {
            position: fixed; top: 0; left: 0; width: 100%; height: 100%;
            z-index: -1;
            background: linear-gradient(145deg, 
                rgba(250, 248, 248, 0.2) 0%, rgba(150, 170, 255, 0.15) 30%,
                rgba(231, 228, 255, 0.3) 60%, rgba(13, 4, 37, 0.05) 100%);
            overflow: hidden;
        }

        .glass-nav {
            position: fixed; top: 25px; left: 50%; transform: translateX(-50%);
            width: calc(100% - 50px); max-width: 1200px; z-index: 1000;
            display: flex; justify-content: space-between; align-items: center;
            padding: 14px 28px; background: var(--glass-bg);
            backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-pill); box-shadow: var(--glass-shadow);
            transition: background var(--transition-speed) var(--transition-ease), box-shadow var(--transition-speed) var(--transition-ease);
        }
        .glass-nav:hover { background: var(--glass-bg-hover); }
        .glass-logo {
            font-family: var(--font-header); font-size: 1.5rem; font-weight: 700;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            -webkit-background-clip: text; -webkit-text-fill-color: transparent; background-clip: text;
            text-decoration: none;
        }
        .glass-logo::after {
            content: ''; position: absolute; bottom: -3px;
            left: 0; width: 100%; height: 2px;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            border-radius: 2px; opacity: 0; transform: scaleX(0);
            transition: all 0.3s ease;
        }
        .glass-logo:hover::after { opacity: 1; transform: scaleX(1); }

        .glass-auth-buttons { display: flex; gap: 12px; }
        .glass-btn {
            padding: 10px 22px; border-radius: var(--border-radius-pill);
            font-family: var(--font-body); font-size: 0.9rem; font-weight: 600;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            text-decoration: none; border: none;
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            position: relative; overflow: hidden;
        }
        .glass-btn::before {
            content: ''; position: absolute; top: 0; left: -100%;
            width: 100%; height: 100%;
            background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
            transition: left 0.5s ease;
        }
        .glass-btn:hover::before { left: 100%; }

        .glass-btn-signin {
            background: rgba(255, 255, 255, 0.15); color: var(--color-text-primary);
            border: 0.1px solid #c5a8e6; /* Borde fino */
        }
        .glass-btn-signin:hover {
            background: rgba(255, 255, 255, 0.25); border-color: var(--glass-border-highlight);
            transform: translateY(-2px); box-shadow: 0 8px 15px rgba(17, 1, 61, 0.08);
        }
        .glass-btn-signup {
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            border: 0px solid rgba(255, 255, 255, 0.1);
        }
        .glass-btn-signup.pulse-effect { animation: pulseGlow 7s ease-in-out infinite; } /* Animación pulse */
        .glass-btn-signup:hover {
            transform: translateY(-2px) scale(1.03);
            box-shadow: 0 16px 45px color-mix(in srgb, var(--color-primary) 10%, transparent);
        }

        .main-container {
            display: flex; flex-direction: column; align-items: center;
            min-height: 100vh;
            padding: 250px 20px 40px;
            width: 100%;
        }
        .header-section { text-align: center; margin-bottom: 40px; z-index: 5; }

        @keyframes synchronizedNeonShine {
            0% { background-position: 0% 50%; }
            50% { background-position: 100% 50%; }
            100% { background-position: 0% 50%; }
        }

        .glass-title {
            font-family: var(--font-header);
            font-size: clamp(1rem, 4vw, 3rem);
            font-weight: 600;
            margin-bottom: 12px;
            background: linear-gradient(135deg, 
                var(--color-primary), 
                var(--color-secondary), 
                var(--color-primary), 
                var(--color-secondary), 
                var(--color-primary)
            );
            background-size: 300% 100%;
            -webkit-background-clip: text;
            -webkit-text-fill-color: transparent;
            background-clip: text;
            letter-spacing: -0.02em;
            line-height: 1.1;
            animation: synchronizedNeonShine 5s linear infinite;
        }
        .glass-subtitle {
            font-size: clamp(0.9rem, 2.2vw, 1.07rem);
            font-weight: 500; color: var(--color-text-secondary);
            max-width: 600px; margin: 0 auto; line-height: 1.6;
        }

        .glass-search-container { width: 100%; max-width: 760px; position: relative; z-index: 10; }
        .glass-search-card {
            background: var(--glass-bg);
            backdrop-filter: blur(22px); -webkit-backdrop-filter: blur(22px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-card); box-shadow: var(--glass-shadow);
            transition: all var(--transition-speed) var(--transition-ease);
            overflow: visible; position: relative;
        }
        .glass-search-card::before {
            content: ''; position: absolute; top: -1px; left: -1px; right: -1px; bottom: -1px;
            border-radius: inherit; 
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            padding: 1.5px;
            -webkit-mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
            -webkit-mask-composite: destination-out; mask-composite: exclude;
            opacity: 0; transition: opacity var(--transition-speed) var(--transition-ease);
            pointer-events: none; z-index: -1;
        }
        .glass-search-card.focused {
            transform: translateY(-3px); box-shadow: var(--glass-hover-shadow);
            border-color: transparent;
        }
        .glass-search-card.focused::before { opacity: 0.7; }

        .glass-search-input {
            width: 100%; background: transparent; border: none; outline: none;
            padding: 40px 26px 14px; font-size: 1rem;
            font-family: var(--font-body); font-weight: 500;
            color: var(--color-text-primary); resize: none;
            min-height: 50px;
            line-height: 1.5;
        }
        .glass-search-input::placeholder { color: var(--color-placeholder); font-weight: 400; }

        .glass-toolbar {
            display: flex; justify-content: space-between; align-items: center;
            padding: 12px 22px; background: var(--glass-bg-toolbar);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
        }
        .glass-model-selector-container { position: relative; }
        .glass-model-btn {
            display: flex; align-items: center; gap: 8px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 9px 20px; font-size: 0.8rem; font-weight: 600;
            color: var(--color-text-primary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
        }
        .glass-model-btn:hover {
            background: rgba(255, 255, 255, 0.2); border-color: var(--glass-border-highlight);
            transform: translateY(-1px); box-shadow: 0 2px 10px rgba(13,4,37, 0.08);
        }
        .glass-model-btn .fa-chevron-down { transition: transform var(--transition-speed) var(--transition-ease); }
        .glass-model-btn.open .fa-chevron-down { transform: rotate(180deg); }
        .glass-model-btn > i:first-child { color: var(--color-secondary); font-size:0.9em; }

        .glass-search-actions { display: flex; align-items: center; gap: 10px; }
        .glass-action-btn {
            width: 38px; height: 38px;
            display: flex; align-items: center; justify-content: center;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(8px); -webkit-backdrop-filter: blur(8px);
            border: 1px solid var(--glass-border);
            border-radius: 50%; color: var(--color-text-secondary);
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            font-size: 0.95rem;
        }
        .glass-action-btn:hover {
            background: var(--color-hover-accent); color: var(--color-primary);
            border-color: var(--color-primary); transform: translateY(-2px) scale(1.08);
            box-shadow: 0 4px 15px color-mix(in srgb, var(--color-primary) 20%, transparent);
        }
        .glass-action-btn:active { transform: translateY(0px) scale(1); background: var(--color-active-accent); }
        .pulse-effect { animation: pulseGlow 1.8s ease-in-out infinite; }
        @keyframes pulseGlow {
            0%, 100% { box-shadow: 0 0 12px color-mix(in srgb, var(--color-primary) 15%, transparent), 0 0 4px var(--color-primary); }
            50% { box-shadow: 0 0 25px color-mix(in srgb, var(--color-primary) 35%, transparent), 0 0 8px var(--color-primary); }
        }

        .glass-content-area {
            background: rgba(255, 255, 255, 0.03);
            backdrop-filter: blur(12px); -webkit-backdrop-filter: blur(12px);
            border-top: 1px solid var(--glass-border);
            padding: 18px; max-height: 380px;
            overflow-y: auto;
        }
        .glass-content-area::-webkit-scrollbar { width: 6px; }
        .glass-content-area::-webkit-scrollbar-track { background: transparent; }
        .glass-content-area::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.2); border-radius: 3px;}
        .glass-content-area::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.3); }

        .glass-loading-indicator {
            display: flex; align-items: center; justify-content: center;
            padding: 25px; color: var(--color-text-secondary); font-size: 0.9rem;
            font-weight: 500;
        }
        .glass-spinner {
            width: 20px; height: 20px;
            border: 2.5px solid color-mix(in srgb, var(--color-primary) 20%, transparent);
            border-top-color: var(--color-primary);
            border-radius: 50%; animation: spin 0.8s linear infinite; margin-right: 12px;
        }
        @keyframes spin { 0% { transform: rotate(0deg); } 100% { transform: rotate(360deg); } }

        .suggestions-panel { animation: fadeInUp 0.4s var(--transition-ease) forwards; }

        .simple-suggestions-list { display: flex; flex-direction: column; gap: 10px; }
        .simple-suggestion-item {
            background: rgba(255, 255, 255, 0.07);
            backdrop-filter: blur(10px); -webkit-backdrop-filter: blur(10px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            padding: 14px 18px; cursor: pointer;
            transition: all var(--transition-speed) var(--transition-ease);
            font-size: 0.9rem; color: var(--color-text-primary); line-height: 1.5;
        }
        .simple-suggestion-item:hover {
            background: rgba(255, 255, 255, 0.16); border-color: var(--glass-border-highlight);
            transform: translateY(-2px); box-shadow: 0 5px 18px rgba(13,4,37, 0.07);
        }
        .simple-suggestion-item strong { font-weight: 700; color: var(--color-primary); }

        .enhanced-prompt-list { display: flex; flex-direction: column; gap: 16px; }
        .enhanced-prompt-card {
            background: rgba(255, 255, 255, 0.06);
            backdrop-filter: blur(18px); -webkit-backdrop-filter: blur(18px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element); padding: 18px;
            transition: all var(--transition-speed) var(--transition-ease);
            position: relative;
        }
        .enhanced-prompt-card:hover {
            background: rgba(255, 255, 255, 0.12); border-color: var(--glass-border-highlight);
            transform: translateY(-3px); box-shadow: 0 10px 30px rgba(13,4,37, 0.1);
        }
        .enhanced-card-header {
            display: flex; justify-content: space-between; align-items: center;
            margin-bottom: 10px;
        }
        .enhanced-card-title {
            font-size: 1.05rem; font-weight: 600; color: var(--color-text-primary);
            line-height: 1.3;
        }
        .glass-star-btn {
            background: rgba(255, 255, 255, 0.1); border: 1px solid var(--glass-border);
            border-radius: 50%; width: 30px; height: 30px;
            display: flex; align-items: center; justify-content: center;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            color: var(--color-text-muted); font-size: 0.85rem;
        }
        .glass-star-btn:hover {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-primary); transform: scale(1.1);
        }
        .glass-star-btn.favorited {
            background: color-mix(in srgb, var(--color-primary) 15%, transparent);
            color: var(--color-star-active);
            border-color: var(--color-primary);
        }
        .glass-star-btn.favorited i { font-weight: 900; }

        .enhanced-prompt-meta-minimal {
            display: flex; flex-wrap: wrap; gap: 6px 12px;
            margin-bottom: 10px; font-size: 0.75rem; color: var(--color-text-secondary);
        }
        .meta-item { display: flex; align-items: center; gap: 5px; }
        .meta-item i { font-size: 0.9em; }

        .full-prompt-display {
            font-size: 0.85rem; line-height: 1.6; color: var(--color-text-secondary);
            margin-bottom: 12px; white-space: pre-wrap;
        }
        .prompt-heading {
            font-weight: 600; color: var(--color-text-primary); display: block; margin-top: 8px;
        }
        .full-prompt-display br + .prompt-heading { margin-top: 8px; }

        .select-prompt-button {
            display: inline-flex; align-items: center; gap: 8px;
            padding: 8px 16px; border-radius: var(--border-radius-pill);
            font-family: var(--font-body); font-size: 0.8rem; font-weight: 600;
            cursor: pointer; transition: all var(--transition-speed) var(--transition-ease);
            text-decoration: none; border: none;
            background: linear-gradient(135deg, var(--color-primary), var(--color-secondary));
            color: var(--color-text-on-gradient);
            margin-top: 8px;
        }
        .select-prompt-button:hover {
            transform: translateY(-2px) scale(1.02);
            box-shadow: 0 6px 20px color-mix(in srgb, var(--color-primary) 25%, transparent);
        }
        .select-prompt-button i { font-size: 0.9em; }

        .popular-prompts-section-title {
            color: var(--color-text-primary); margin-bottom: 14px;
            display: flex; align-items: center; gap: 8px;
            font-weight: 600; font-size: 1.05rem;
        }
        .popular-prompts-section-title i { color: var(--color-primary); }

        /* === GLASS DROPDOWN (para modelos, C2 con adaptaciones de C1) === */
        .glass-model-dropdown {
            position: absolute; top: calc(100% + 10px); left: 0;
            width: auto; min-width: 280px; max-width: 350px;
            background: var(--glass-bg);
            backdrop-filter: blur(20px); -webkit-backdrop-filter: blur(20px);
            border: 1px solid var(--glass-border);
            border-radius: var(--border-radius-element);
            box-shadow: 0 10px 30px rgba(13,4,37,0.1);
            opacity: 0; visibility: hidden;
            transform: translateY(-8px) scale(0.98);
            transition: all var(--transition-speed) var(--transition-ease);
            z-index: 1100;
            padding: 8px;
        }
        .glass-model-dropdown.visible { opacity: 1; visibility: visible; transform: translateY(0) scale(1); }
        
        .model-search-input-container {
            position: relative; margin-bottom: 15px;
        }
        .model-search-input-container .fa-search {
            position: absolute; top: 50%; left: 12px; transform: translateY(-50%);
            color: var(--color-text-muted); font-size: 0.9em;
        }
        #modelSearchInput {
            width: 100%;
            padding: 10px 12px 10px 34px;
            background: rgba(255, 255, 255, 0.08);
            backdrop-filter: blur(5px); -webkit-backdrop-filter: blur(5px);
            border: 1px solid var(--glass-border);
            border-radius: calc(var(--border-radius-element) - 4px);
            font-family: var(--font-body); font-size: 0.85rem;
            color: var(--color-text-primary); outline: none;
            transition: border-color var(--transition-speed) var(--transition-ease);
        }
        #modelSearchInput::placeholder { color: var(--color-placeholder); }
        #modelSearchInput:focus { border-color: var(--glass-border-highlight); }

        .model-list-ul {
            list-style: none; padding: 0; margin: 0;
            max-height: 250px; overflow-y: auto;
        }
        .model-list-ul::-webkit-scrollbar { width: 5px; }
        .model-list-ul::-webkit-scrollbar-track { background: transparent; }
        .model-list-ul::-webkit-scrollbar-thumb { background: rgba(255,255,255,0.25); border-radius: 3px;}
        .model-list-ul::-webkit-scrollbar-thumb:hover { background: rgba(255,255,255,0.35); }

        .model-list-ul li {
            padding: 10px 14px;
            color: var(--color-text-primary); font-weight: 500; font-size: 0.85rem;
            cursor: pointer; transition: background-color var(--transition-speed) var(--transition-ease);
            border-radius: calc(var(--border-radius-element) - 6px);
            display: flex; align-items: center; gap: 10px;
            margin-bottom: 4px;
        }
        .model-list-ul li:last-child { margin-bottom: 0; }
        .model-list-ul li:hover { background: var(--color-hover-accent); color: var(--color-primary); }
        .model-list-ul li.selected-model-item {
            background-color: var(--color-active-accent);
            color: var(--color-primary); font-weight: 600;
        }
        .model-list-ul li img, .model-list-ul li .model-icon {
            width: 18px; height: 18px; object-fit: contain; flex-shrink: 0;
        }
        .model-list-ul li .model-icon { font-size: 1em; text-align: center; }
        .model-icon.google-blue { color: var(--google-blue); }
        .model-icon.google-green { color: var(--google-green); }
        .model-icon.google-red { color: var(--google-red); }
        .model-icon.star-black { color: var(--star-black); }
        .model-icon.star-orange { color: var(--star-orange); }
        .model-icon.star-blue-gemini { color: var(--star-blue-gemini); }
        .model-icon.star-darkgray { color: var(--star-darkgray); }
        .model-icon.star-skyblue { color: var(--star-skyblue); }

        .glass-footer {
            margin-top: auto; padding: 40px 20px 25px; text-align: center;
            color: var(--color-text-muted); font-size: 0.8rem;
        }
        .glass-footer i.fa-heart { color: var(--color-primary); }

        @keyframes fadeInUp { from { opacity: 0; transform: translateY(15px) scale(0.99); } to { opacity: 1; transform: translateY(0) scale(1); } }
        @keyframes fadeInDown { from { opacity: 0; transform: translateY(-10px); } to { opacity: 1; transform: translateY(0); } }

        .hidden { display: none !important; }

        @media (max-width: 768px) {
            .glass-nav { width: calc(100% - 30px); padding: 12px 20px; }
            .glass-logo { font-size: 1.3rem; }
            .glass-btn { padding: 8px 18px; font-size: 0.85rem; }

            .main-container { padding: 100px 16px 30px; }
            .glass-title { font-size: clamp(2rem, 6.5vw, 2.8rem); }
            .glass-subtitle { font-size: clamp(0.85rem, 3vw, 1rem); }
            
            .glass-toolbar { flex-direction: column; gap: 14px; padding: 14px; align-items: stretch; }
            .glass-model-selector-container { width: 100%; }
            .glass-model-btn { justify-content: center; }
            .glass-search-actions { justify-content: space-around; width: 100%; }
            
            .glass-search-input { min-height: 50px; padding: 16px 20px 10px;}
            .glass-content-area { padding: 16px; max-height: 300px;}
            .enhanced-prompt-card, .simple-suggestion-item { padding: 16px; }
            .glass-model-dropdown { min-width: calc(100% - 40px); left: 50%; transform: translateX(-50%) translateY(-8px) scale(0.98); }
            .glass-model-dropdown.visible { transform: translateX(-50%) translateY(0) scale(1); }
        }
         @media (max-width: 480px) {
            .glass-auth-buttons { gap: 8px; }
            .glass-btn { padding: 7px 12px; font-size: 0.8rem; }
            .glass-action-btn { width: 36px; height: 36px; font-size: 0.9rem;}
            .glass-search-actions { gap: 8px;}
         }
    </style>
</head>
<body>
    <div class="background-container">
        <div class="floating-shapes">
            <div class="shape"></div><div class="shape"></div><div class="shape"></div><div class="shape"></div>
        </div>
    </div>

    <nav class="glass-nav">
        <a href="#" class="glass-logo">allhub</a>
        <div class="glass-auth-buttons">
            <a href="#" class="glass-btn glass-btn-signin">Sign In</a>
            <a href="#" class="glass-btn glass-btn-signup pulse-effect">Sign Up Free</a>
        </div>
    </nav>

    <main class="main-container">
        <section class="header-section">
            <h1 class="glass-title">Prompt like a pro</h1>
            <p class="glass-subtitle">Craft the perfect prompts with intelligent suggestions</p>
        </section>

        <div class="glass-search-container" id="searchAreaContainer">
            <div class="glass-search-card" id="searchBarCard">
                <textarea 
                class="glass-search-input" 
                id="searchInput" 
                placeholder="Type your prompt here..."></textarea>

                <div class="glass-toolbar">
                    <div class="glass-model-selector-container">
                        <button class="glass-model-btn" id="modelSelectorBtn">
                            <i class="fas fa-cogs"></i>
                            <span id="currentModelName">Model</span>
                            <i class="fas fa-chevron-down"></i>
                        </button>
                    </div>

                    <div class="glass-search-actions" id="searchActionsContainer">
                        <button class="glass-action-btn" id="micBtn" title="Voice Input"><i class="fas fa-microphone"></i></button>
                        <button class="glass-action-btn" id="guidedCreateBtn" title="Guided Creation"><i class="fas fa-wand-magic-sparkles"></i></button>
                        <button class="glass-action-btn" id="copyBtn" title="Copy Prompt"><i class="fas fa-copy"></i></button>
                        <button class="glass-action-btn pulse-effect" id="sendBtn" title="Send Prompt"><i class="fas fa-paper-plane"></i></button>
                    </div>
                </div>
                
                <div class="glass-content-area hidden" id="dynamicContentArea">
                    <div class="glass-loading-indicator hidden" id="analyzingIndicator">
                        <div class="glass-spinner"></div>
                        Analyzing your prompt...
                    </div>
                    <div class="suggestions-panel hidden" id="simplePromptSuggestionsContainer">
                        <div class="simple-suggestions-list">
                        </div>
                    </div>
                    <div class="suggestions-panel hidden" id="enhancedPromptSuggestionsContainer">
                         <div class="enhanced-prompt-list">
                         </div>
                    </div>
                    <div class="suggestions-panel hidden" id="favoritePromptsSection">
                        <h3 class="popular-prompts-section-title">
                            <i class="fas fa-star"></i>
                            Popular Prompts
                        </h3>
                        <div class="enhanced-prompt-list" id="favoritePromptsList">
                        </div>
                    </div>
                </div>
            </div>
            
            <div class="glass-model-dropdown hidden" id="modelDropdownList">
                <div class="model-search-input-container">
                    <i class="fas fa-search"></i>
                    <input type="text" id="modelSearchInput" placeholder="Search models">
                </div>
                <ul class="model-list-ul"></ul>
            </div>
        </div>

        <footer class="glass-footer">
            Copyright © <span id="copyrightYear">2024</span> All Hub. Crafted with <i class="fas fa-heart"></i> & AI.
        </footer>
    </main>

    <script>
        const searchAreaContainer = document.getElementById('searchAreaContainer');
        const searchBarCard = document.getElementById('searchBarCard');
        const searchInput = document.getElementById('searchInput');
        
        const guidedCreateBtn = document.getElementById('guidedCreateBtn');
        const micBtn = document.getElementById('micBtn');
        const copyBtn = document.getElementById('copyBtn');
        const sendBtn = document.getElementById('sendBtn');

        const dynamicContentArea = document.getElementById('dynamicContentArea');
        const analyzingIndicator = document.getElementById('analyzingIndicator');
        const simplePromptSuggestionsContainer = document.getElementById('simplePromptSuggestionsContainer');
        const enhancedPromptSuggestionsContainer = document.getElementById('enhancedPromptSuggestionsContainer');
        const favoritePromptsSection = document.getElementById('favoritePromptsSection');
        const favoritePromptsList = document.getElementById('favoritePromptsList');

        const modelSelectorBtn = document.getElementById('modelSelectorBtn');
        const currentModelNameSpan = document.getElementById('currentModelName');
        const modelDropdownList = document.getElementById('modelDropdownList');
        const modelSearchInput = document.getElementById('modelSearchInput');
        const modelListUl = modelDropdownList.querySelector('.model-list-ul');

        let isGuidedModeActive = false;
        let favoritePrompts = JSON.parse(localStorage.getItem('favoritePromptsAllHub')) || [];
        let blockSuggestionsOnNextFocus = false;
        let inputDebounceTimeout;

        const modelsData = [
            { id: "openai-gpt-4", displayName: "OpenAI: GPT-4", shortName: "GPT-4", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-black" },
            { id: "anthropic-claude-3", displayName: "Anthropic: Claude 3", shortName: "Claude 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-orange" },
            { id: "google-gemini-1.5", displayName: "Google: Gemini 1.5", shortName: "Gemini 1.5", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-blue-gemini" },
            { id: "mistral-large", displayName: "Mistral: Large", shortName: "Mistral Large", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-darkgray" },
            { id: "meta-llama-3", displayName: "Meta: LLaMA 3", shortName: "LLaMA 3", iconType: "fa", iconClass: "fas fa-star", iconColorClass: "star-skyblue" },
        ];
        
        function debounce(func, delay) {
            let timeout;
            const debounced = function(...args) {
                const context = this;
                clearTimeout(timeout);
                timeout = setTimeout(() => func.apply(context, args), delay);
            };
            debounced.cancel = function() { clearTimeout(timeout); };
            return debounced;
        }

        function autoResizeTextarea(textarea) {
            textarea.style.height = 'auto';
            let scrollHeight = textarea.scrollHeight;
            const minHeight = parseInt(window.getComputedStyle(textarea).minHeight, 10) || 60;
            textarea.style.height = Math.max(minHeight, scrollHeight) + 'px';
            textarea.style.overflowY = (scrollHeight > minHeight && scrollHeight > textarea.clientHeight) ? 'auto' : 'hidden';
        }
        
        function formatFullPromptForDisplay(fullPromptText) {
            if (!fullPromptText) return "";
            let html = fullPromptText.replace(/&/g, "&").replace(/</g, "<").replace(/>/g, ">"); 
            html = html.replace(/^(Example \d+:|Step \d+:|Title:|Introduction:|Conclusion:|Identify the key components.*|List \d+ practical methods.*|For each method.*|Structure the article.*|Write a \d+-word blog article.*)/gim, (match) => `<span class="prompt-heading">${match.trim()}</span>`);
            return html.replace(/\n/g, '<br>');
        }

        function updateDynamicContentAreaVisibility() {
            const isSimpleVisible = !simplePromptSuggestionsContainer.classList.contains('hidden') && simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')?.children.length > 0;
            const isEnhancedVisible = !enhancedPromptSuggestionsContainer.classList.contains('hidden') && enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')?.children.length > 0;
            const isFavoritesVisible = !favoritePromptsSection.classList.contains('hidden') && favoritePromptsList.children.length > 0;
            const isAnalyzingVisible = !analyzingIndicator.classList.contains('hidden');

            if (isSimpleVisible || isEnhancedVisible || isFavoritesVisible || isAnalyzingVisible) {
                dynamicContentArea.classList.remove('hidden');
            } else {
                dynamicContentArea.classList.add('hidden');
            }
        }

        function hideAllDynamicContentExcept(exceptContainer = null) {
            [simplePromptSuggestionsContainer, enhancedPromptSuggestionsContainer, favoritePromptsSection, analyzingIndicator].forEach(container => {
                if (container && container !== exceptContainer) {
                    container.classList.add('hidden');
                    if (container !== analyzingIndicator && container.id !== 'simplePromptSuggestionsContainer' && container.id !== 'enhancedPromptSuggestionsContainer' && container.id !== 'favoritePromptsSection' ) {
                    } else if (container.id === 'simplePromptSuggestionsContainer' && container.querySelector('.simple-suggestions-list')) {
                        container.querySelector('.simple-suggestions-list').innerHTML = '';
                    } else if (container.id === 'enhancedPromptSuggestionsContainer' && container.querySelector('.enhanced-prompt-list')) {
                        container.querySelector('.enhanced-prompt-list').innerHTML = '';
                    } else if (container.id === 'favoritePromptsSection' && favoritePromptsList) {
                        favoritePromptsList.innerHTML = '';
                    }
                }
            });
             if (exceptContainer) exceptContainer.classList.remove('hidden');
        }

        function setDefaultModel() {
            if (modelsData.length > 0) {
                const defaultModel = modelsData[0];
                currentModelNameSpan.textContent = defaultModel.shortName;
                currentModelNameSpan.dataset.selectedModelId = defaultModel.id;
            } else {
                currentModelNameSpan.textContent = "Model";
                delete currentModelNameSpan.dataset.selectedModelId;
            }
        }

        function populateModelDropdown() {
            modelListUl.innerHTML = '';
            const searchTerm = modelSearchInput.value.toLowerCase();
            const currentSelectedModelId = currentModelNameSpan.dataset.selectedModelId;

            modelsData.filter(model => model.displayName.toLowerCase().includes(searchTerm)).forEach(model => {
                const li = document.createElement('li');
                li.dataset.modelId = model.id;
                li.dataset.modelShortName = model.shortName;
                li.dataset.modelDisplayName = model.displayName;

                let iconElement;
                if (model.iconType === "fa") {
                    iconElement = document.createElement('i');
                    iconElement.className = `model-icon ${model.iconClass} ${model.iconColorClass || ''}`;
                } else if (model.iconType === "img") {
                    iconElement = document.createElement('img');
                    iconElement.src = model.iconUrl;
                    iconElement.alt = model.displayName.split(':')[0];
                    iconElement.onerror = () => { iconElement.style.display = 'none'; };
                } else {
                    iconElement = document.createElement('span');
                    iconElement.className = 'model-icon icon-placeholder';
                    iconElement.textContent = '●';
                }
                li.appendChild(iconElement);

                const span = document.createElement('span');
                span.textContent = model.displayName;
                li.appendChild(span);

                if (currentSelectedModelId && currentSelectedModelId === model.id) {
                    li.classList.add('selected-model-item');
                }

                li.addEventListener('click', (e) => {
                    e.stopPropagation();
                    currentModelNameSpan.textContent = model.shortName;
                    currentModelNameSpan.dataset.selectedModelId = model.id;

                    modelListUl.querySelectorAll('li').forEach(item => item.classList.remove('selected-model-item'));
                    li.classList.add('selected-model-item');

                    modelDropdownList.classList.remove('visible');
                    modelDropdownList.classList.add('hidden');
                    modelSelectorBtn.classList.remove('open');
                    
                    blockSuggestionsOnNextFocus = true;
                    searchInput.focus();
                    hideAllDynamicContentExcept();
                    updateDynamicContentAreaVisibility();
                });
                modelListUl.appendChild(li);
            });
        }

        function renderFavoritePrompts() {
            if (isGuidedModeActive || modelDropdownList.classList.contains('visible')) return;

            hideAllDynamicContentExcept(favoritePromptsSection);
            favoritePromptsList.innerHTML = '';

            if (favoritePrompts.length > 0 && searchInput.value.length === 0) {
                favoritePrompts.forEach(promptData => {
                    const item = createPromptCard(promptData, false, true);
                    favoritePromptsList.appendChild(item);
                });
                favoritePromptsSection.classList.remove('hidden');
            } else {
                favoritePromptsSection.classList.add('hidden');
            }
            updateDynamicContentAreaVisibility();
        }

        function toggleFavorite(promptData, starIconElement) {
            const promptId = promptData.id || promptData.title;
            let index = favoritePrompts.findIndex(fp => (fp.id || fp.title) === promptId);
            let wasFavorited = index > -1;

            if (wasFavorited) {
                favoritePrompts.splice(index, 1);
            } else {
                const favData = { 
                    id: promptData.id, 
                    title: promptData.title, 
                    fullPrompt: promptData.fullPrompt, 
                    metaMinimal: promptData.metaMinimal, 
                    description: promptData.description || "N/A" 
                };
                favoritePrompts.push(favData);
            }
            localStorage.setItem('favoritePromptsAllHub', JSON.stringify(favoritePrompts));
            
            document.querySelectorAll(`.glass-star-btn[data-prompt-id="${promptId}"]`).forEach(starBtn => {
                 const iElement = starBtn.querySelector('i');
                if (wasFavorited) {
                    starBtn.classList.remove('favorited');
                    if(iElement) { iElement.classList.remove('fas'); iElement.classList.add('far'); }
                } else {
                    starBtn.classList.add('favorited');
                    if(iElement) { iElement.classList.remove('far'); iElement.classList.add('fas'); }
                }
            });

            if (!isGuidedModeActive && searchInput.value === '') {
                renderFavoritePrompts();
            }
        }
        
        function createPromptCard(promptData, _isSingleGuidedView_IGNORED = false, isFavoriteListItem = false) {
            const card = document.createElement('div');
            card.classList.add('enhanced-prompt-card');
            if (isFavoriteListItem) card.classList.add('favorite-list-item-card');
            const uniqueId = promptData.id || promptData.title.replace(/\s+/g, '-').toLowerCase();
            card.dataset.promptId = uniqueId;

            const isFavorited = favoritePrompts.some(fp => (fp.id || fp.title) === (promptData.id || promptData.title));
            const starIconClass = isFavorited ? 'fas fa-star' : 'far fa-star';

            let metaMinimalHTML = '';
            if (promptData.metaMinimal) {
                metaMinimalHTML = '<div class="enhanced-prompt-meta-minimal">';
                if (promptData.metaMinimal.inputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-in-alt"></i> ${promptData.metaMinimal.inputTokens}</span>`;
                if (promptData.metaMinimal.outputTokens) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-sign-out-alt"></i> ${promptData.metaMinimal.outputTokens}</span>`;
                if (promptData.metaMinimal.time) metaMinimalHTML += `<span class="meta-item"><i class="far fa-clock"></i> ${promptData.metaMinimal.time}</span>`;
                if (promptData.metaMinimal.reuse) metaMinimalHTML += `<span class="meta-item"><i class="fas fa-redo-alt"></i> ${promptData.metaMinimal.reuse}</span>`;
                metaMinimalHTML += '</div>';
            }

            const buttonText = isFavoriteListItem ? 'Use Favorite' : 'Select Prompt';

            card.innerHTML = `
                <div class="enhanced-card-header">
                    <h5 class="enhanced-card-title">${promptData.title || "Prompt"}</h5>
                    <button class="glass-star-btn" title="Mark as favorite" data-prompt-id="${uniqueId}">
                        <i class="${starIconClass}"></i>
                    </button>
                </div>
                ${metaMinimalHTML}
                <div class="full-prompt-display">${formatFullPromptForDisplay(promptData.fullPrompt || promptData.title)}</div>
                <button class="select-prompt-button">
                    <i class="fas fa-check-circle"></i> ${buttonText}
                </button>
            `;
            card.querySelector('.glass-star-btn').addEventListener('click', (e) => {
                e.stopPropagation();
                toggleFavorite(promptData, e.currentTarget.querySelector('i'));
            });
            card.querySelector('.select-prompt-button').addEventListener('click', () => {
                let promptTextToSet = promptData.fullPrompt || promptData.title;
                if (promptData.id === "few-shot-summarize-pdf") {
                    promptTextToSet = promptTextToSet.replace(/\n{2,}/g, '\n'); 
                }
                searchInput.value = promptTextToSet;
                autoResizeTextarea(searchInput);
                exitGuidedModeIfNeeded();
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
                blockSuggestionsOnNextFocus = true;
                searchInput.focus();
            });
            return card;
        }

        function displayGuidedPromptList(suggestions) {
            isGuidedModeActive = true;
            hideAllDynamicContentExcept(enhancedPromptSuggestionsContainer);
            const listContainer = enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list');
            if (!listContainer) return;
            listContainer.innerHTML = '';

            if (suggestions && suggestions.length > 0) {
                suggestions.forEach(suggData => {
                    const card = createPromptCard(suggData);
                    listContainer.appendChild(card);
                });
                enhancedPromptSuggestionsContainer.classList.remove('hidden');
            }
            searchBarCard.classList.add('focused');
            updateDynamicContentAreaVisibility();
        }

        function generateGuidedPrompts() {
            let suggestions = [];
            suggestions.push({ title: "Improve with Few-Shot Examples", id: "few-shot-summarize-pdf", fullPrompt: `Write a blog article about how to summarize a PDF. Follow the style and structure of these examples:\n\nExample 1:\nTitle: "How to Create a Mind Map"\nIntroduction: Mind maps are great for organizing thoughts. This article explains simple steps to create one.\nStep 1: Choose a central topic...\nStep 2: Add branches for subtopics...\nConclusion: Mind mapping is easy and fun. Try it today!\n\nExample 2:\nTitle: "How to Take Better Notes"\nIntroduction: Good notes help you study better. Here’s how to do it.\nStep 1: Use a clear format...\nStep 2: Highlight key points...\nConclusion: Better notes lead to better learning. Start now! Write a 400-word blog article titled "How to Summarize a PDF Effectively" with an introduction, 3 practical steps, and a conclusion. Use a clear and simple tone for students.`, metaMinimal: { inputTokens: "170", outputTokens: "~400-600", time: "~1-2s", reuse: "High" }, description: "Uses few-shot examples." });
            suggestions.push({ title: "Enhance with Chain of Thought", id: "cot-summarize-pdf", fullPrompt: `Write a 400-word blog article about how to summarize a PDF. Before writing, break down the process as follows: Identify the key components of a blog article (e.g., introduction, main steps, conclusion). List 3 practical methods for summarizing a PDF (e.g., using software, manual highlighting, or online tools). For each method, explain one benefit and one challenge. Structure the article with a clear introduction, a section for each method, and a conclusion encouraging readers to try summarizing. Use a professional yet accessible tone for small business owners. Title the article "Mastering PDF Summarization: A Step-by-Step Guide."`, metaMinimal: { inputTokens: "195", outputTokens: "~500-800", time: "~1-3s", reuse: "High" }, description: "Uses Chain of Thought." });
            return suggestions;
        }

        async function handleGuidedCreateClick() {
            if (modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }
            hideAllDynamicContentExcept(analyzingIndicator);
            analyzingIndicator.classList.remove('hidden');
            searchBarCard.classList.add('focused');
            updateDynamicContentAreaVisibility();

            await new Promise(resolve => setTimeout(resolve, 1200));
            const guidedPrompts = generateGuidedPrompts();
            displayGuidedPromptList(guidedPrompts);
        }

        function exitGuidedModeIfNeeded() {
            if (isGuidedModeActive) {
                isGuidedModeActive = false;
                hideAllDynamicContentExcept();
                if (document.activeElement !== searchInput) {
                    searchBarCard.classList.remove('focused');
                }
                if (searchInput.value === '') renderFavoritePrompts();
                updateDynamicContentAreaVisibility();
            }
        }

        function generateSimpleSuggestions(inputText) {
            const baseSuggestions = [
                "Write a blog post about how to automatically extract key points from a PDF",
                "Write an article about summarizing research papers using AI",
                "Write a tweet about the best free tools to summarize PDFs",
                "Write a blog post about building a PDF summarizer with Python",
                "Write a blog article about summarizing legal documents without reading them",
                "Write a blog post about syncing summarized PDF meeting notes with Notion"
            ];
            if (!inputText) return [];
            const lowerInput = inputText.toLowerCase();
            return baseSuggestions.filter(sugg => sugg.toLowerCase().includes(lowerInput)).slice(0, 4);
        }

        const handleSimpleInput = debounce(function() {
            if (isGuidedModeActive || modelDropdownList.classList.contains('visible')) {
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
                return;
            }

            const inputText = searchInput.value;
            hideAllDynamicContentExcept(simplePromptSuggestionsContainer);
            const listContainer = simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list');
            if (!listContainer) return;
            listContainer.innerHTML = '';

            if (inputText.length < 1) {
                simplePromptSuggestionsContainer.classList.add('hidden');
                if (inputText.length === 0) renderFavoritePrompts();
                updateDynamicContentAreaVisibility();
                return;
            }

            const suggestions = generateSimpleSuggestions(inputText);
            if (suggestions.length > 0) {
                simplePromptSuggestionsContainer.classList.remove('hidden');
                suggestions.forEach(suggText => {
                    const item = document.createElement('div');
                    item.classList.add('simple-suggestion-item');
                    const matchIndex = suggText.toLowerCase().indexOf(inputText.toLowerCase());
                    if (matchIndex > -1) {
                        item.innerHTML = suggText.substring(0, matchIndex) +
                                         `<strong>${suggText.substring(matchIndex, matchIndex + inputText.length)}</strong>` +
                                         suggText.substring(matchIndex + inputText.length);
                    } else {
                        item.textContent = suggText;
                    }
                    item.addEventListener('click', () => {
                        searchInput.value = suggText;
                        autoResizeTextarea(searchInput);
                        hideAllDynamicContentExcept();
                        updateDynamicContentAreaVisibility();
                        blockSuggestionsOnNextFocus = true;
                        searchInput.focus();
                    });
                    listContainer.appendChild(item);
                });
            } else {
                simplePromptSuggestionsContainer.classList.add('hidden');
            }
            updateDynamicContentAreaVisibility();
        }, 300);

        searchInput.addEventListener('input', () => {
            autoResizeTextarea(searchInput);
            if (isGuidedModeActive && searchInput.value === '') {
                exitGuidedModeIfNeeded();
            } else if (!isGuidedModeActive && !modelDropdownList.classList.contains('visible')) {
                blockSuggestionsOnNextFocus = false;
                handleSimpleInput();
            } else if (modelDropdownList.classList.contains('visible')) {
                hideAllDynamicContentExcept();
                updateDynamicContentAreaVisibility();
            }
        });

        searchInput.addEventListener('focus', () => {
            searchBarCard.classList.add('focused');
            autoResizeTextarea(searchInput);

            if (modelDropdownList.classList.contains('visible') || isGuidedModeActive) {
                return;
            }
            
            if (searchInput.value === '') {
                renderFavoritePrompts();
            } else {
                if (blockSuggestionsOnNextFocus) {
                    blockSuggestionsOnNextFocus = false;
                } else {
                    handleSimpleInput.cancel();
                    handleSimpleInput();
                }
            }
        });
        
        let blurTimeout;
        searchInput.addEventListener('blur', () => {
            blurTimeout = setTimeout(() => {
                const activeEl = document.activeElement;
                const isFocusWithinRelevantArea = 
                    searchBarCard.contains(activeEl) ||
                    modelDropdownList.contains(activeEl) ||
                    (dynamicContentArea && dynamicContentArea.contains(activeEl));

                if (!isFocusWithinRelevantArea) {
                    searchBarCard.classList.remove('focused');
                    if (!modelDropdownList.classList.contains('visible') && !isGuidedModeActive) {
                        hideAllDynamicContentExcept();
                        updateDynamicContentAreaVisibility();
                    }
                }
            }, 150);
        });
        
        [modelDropdownList, dynamicContentArea].forEach(el => {
            if(el) {
                el.addEventListener('focusin', () => clearTimeout(blurTimeout));
                el.addEventListener('mousedown', () => clearTimeout(blurTimeout));
            }
        });

        modelSelectorBtn.addEventListener('click', (e) => {
            e.stopPropagation();
            const isCurrentlyHidden = modelDropdownList.classList.contains('hidden');
            
            exitGuidedModeIfNeeded();
            hideAllDynamicContentExcept();

            if (isCurrentlyHidden) {
                // Eliminamos el posicionamiento dinámico para depender solo del CSS
                modelDropdownList.style.top = ''; // Dejar que el CSS controle 'top'
                modelDropdownList.style.left = ''; // Dejar que el CSS controle 'left'
                modelDropdownList.style.width = ''; // Limpiar ancho dinámico

                modelDropdownList.classList.remove('hidden');
                modelDropdownList.classList.add('visible');
                modelSelectorBtn.classList.add('open');
                modelSearchInput.value = '';
                populateModelDropdown();
                modelSearchInput.focus();
            } else {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }
            updateDynamicContentAreaVisibility();
        });

        modelSearchInput.addEventListener('input', populateModelDropdown);
        modelSearchInput.addEventListener('click', (e) => e.stopPropagation());

        guidedCreateBtn.addEventListener('click', handleGuidedCreateClick);
        if (micBtn) { micBtn.addEventListener('click', () => console.log("Micrófono presionado (implementar funcionalidad)")); }
        copyBtn.addEventListener('click', (e) => { 
            e.stopPropagation(); 
            if(searchInput.value) navigator.clipboard.writeText(searchInput.value)
                .then(() => console.log('Prompt Copied!'))
                .catch(err => console.error('Failed to copy prompt: ', err)); 
        });
        sendBtn.addEventListener('click', (e) => { 
            e.stopPropagation(); 
            if(searchInput.value) console.log('Prompt sent: ' + searchInput.value + ' (Model: ' + currentModelNameSpan.textContent + ')');
        });

        document.addEventListener('click', (event) => {
            if (!modelSelectorBtn.contains(event.target) && !modelDropdownList.contains(event.target) && modelDropdownList.classList.contains('visible')) {
                modelDropdownList.classList.remove('visible');
                modelDropdownList.classList.add('hidden');
                modelSelectorBtn.classList.remove('open');
            }

            const isClickInsideSearchArea = searchAreaContainer.contains(event.target) || 
                                            modelDropdownList.contains(event.target);

            if (!isClickInsideSearchArea) {
                 searchBarCard.classList.remove('focused');
                if (!isGuidedModeActive && !modelDropdownList.classList.contains('visible')) {
                    hideAllDynamicContentExcept();
                    if(searchInput.value === '') renderFavoritePrompts();
                    updateDynamicContentAreaVisibility();
                }
            }
        });
        
        document.addEventListener('DOMContentLoaded', () => {
            autoResizeTextarea(searchInput);
            setDefaultModel();
            document.getElementById('copyrightYear').textContent = new Date().getFullYear();

            if (searchInput.value === '') {
                renderFavoritePrompts();
            }
            if (document.activeElement === searchInput) {
                searchBarCard.classList.add('focused');
                if (searchInput.value !== '') { 
                }
            }
            if (!simplePromptSuggestionsContainer.querySelector('.simple-suggestions-list')) {
                const list = document.createElement('div');
                list.className = 'simple-suggestions-list';
                simplePromptSuggestionsContainer.appendChild(list);
            }
            if (!enhancedPromptSuggestionsContainer.querySelector('.enhanced-prompt-list')) {
                 const list = document.createElement('div');
                list.className = 'enhanced-prompt-list';
                enhancedPromptSuggestionsContainer.appendChild(list);
            }
        });

    </script>
</body>
</html>