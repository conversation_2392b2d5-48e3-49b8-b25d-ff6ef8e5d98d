"use client";

import { useState } from "react";
import { PromptInput } from "@/components/PromptInput";
import { SuggestionList } from "@/components/SuggestionList";
import { Logo } from "@/components/Logo";
import { usePromptSuggestions } from "@/hooks/usePromptSuggestions";
import { Footer } from "@/components/Footer";
import { ThemeToggle } from "@/components/ThemeToggle";
import { getPromptCategories } from "@/utils/suggestions";
import { toast } from "sonner";

export function PromptCopilot() {
  const [inputValue, setInputValue] = useState("");
  const { suggestions, isLoading } = usePromptSuggestions(inputValue);
  const categories = getPromptCategories();

  const handleCopy = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast.success("Prompt copiado al portapapeles");
    } catch (err) {
      toast.error("Error al copiar el prompt");
    }
  };

  return (
    <div className="w-full max-w-3xl mx-auto flex flex-col items-center">
      <div className="absolute top-4 right-4">
        <ThemeToggle />
      </div>
      
      <div className="flex flex-col items-center justify-center w-full mb-8">
        <Logo className="mb-6 mt-16 md:mt-0" />
        <h1 className="text-2xl font-bold text-center mb-2">Prompting Copilot</h1>
        <p className="text-muted-foreground text-center mb-8 max-w-md">
          Crea mejores prompts con sugerencias inteligentes
        </p>
        
        <PromptInput 
          value={inputValue}
          onChange={setInputValue}
          isLoading={isLoading}
        />
      </div>
      
      {inputValue.length > 0 ? (
        <SuggestionList 
          suggestions={suggestions}
          onSuggestionClick={handleCopy}
        />
      ) : (
        <div className="w-full grid grid-cols-1 md:grid-cols-2 gap-6">
          {categories.map((category) => (
            <div key={category.name} className="space-y-4">
              <h2 className="text-xl font-semibold text-gray-800 dark:text-gray-200">
                {category.name}
              </h2>
              <div className="space-y-2">
                {category.prompts.slice(0, 3).map((prompt) => (
                  <button
                    key={prompt}
                    onClick={() => handleCopy(prompt)}
                    className="w-full text-left p-3 rounded-lg border border-gray-200 dark:border-gray-800 hover:border-blue-400 dark:hover:border-blue-500 transition-colors"
                  >
                    <p className="text-sm text-gray-800 dark:text-gray-200">
                      {prompt}
                    </p>
                  </button>
                ))}
              </div>
            </div>
          ))}
        </div>
      )}
      
      <Footer />
    </div>
  );
}