"use client";

import { useEffect, useRef, useState } from "react";
import { Loader2, Search } from "lucide-react";
import { cn } from "@/lib/utils";
import { useDebounce } from "@/hooks/useDebounce";
import { getAutocompletions } from "@/utils/suggestions";

interface PromptInputProps {
  value: string;
  onChange: (value: string) => void;
  isLoading?: boolean;
}

export function PromptInput({ value, onChange, isLoading = false }: PromptInputProps) {
  const [isFocused, setIsFocused] = useState(false);
  const [showCompletions, setShowCompletions] = useState(false);
  const [completions, setCompletions] = useState<string[]>([]);
  const [selectedIndex, setSelectedIndex] = useState(-1);
  const debouncedValue = useDebounce(value, 300);
  const inputRef = useRef<HTMLInputElement>(null);

  useEffect(() => {
    if (debouncedValue.length > 0) {
      const suggestions = getAutocompletions(debouncedValue);
      setCompletions(suggestions);
      setShowCompletions(suggestions.length > 0);
    } else {
      setCompletions([]);
      setShowCompletions(false);
    }
  }, [debouncedValue]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    onChange(e.target.value);
    setSelectedIndex(-1);
  };

  const handleFocus = () => {
    setIsFocused(true);
    if (completions.length > 0) {
      setShowCompletions(true);
    }
  };

  const handleBlur = () => {
    // Delay hiding completions to allow clicking on them
    setTimeout(() => {
      setIsFocused(false);
      setShowCompletions(false);
    }, 200);
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (completions.length === 0) return;

    // Arrow Down
    if (e.key === "ArrowDown") {
      e.preventDefault();
      setSelectedIndex((prev) => (prev < completions.length - 1 ? prev + 1 : prev));
    }
    // Arrow Up
    else if (e.key === "ArrowUp") {
      e.preventDefault();
      setSelectedIndex((prev) => (prev > 0 ? prev - 1 : 0));
    }
    // Enter
    else if (e.key === "Enter" && selectedIndex >= 0) {
      e.preventDefault();
      onChange(completions[selectedIndex]);
      setShowCompletions(false);
      setSelectedIndex(-1);
    }
    // Tab
    else if (e.key === "Tab" && showCompletions && completions.length > 0) {
      e.preventDefault();
      onChange(completions[selectedIndex >= 0 ? selectedIndex : 0]);
      setShowCompletions(false);
      setSelectedIndex(-1);
    }
    // Escape
    else if (e.key === "Escape") {
      e.preventDefault();
      setShowCompletions(false);
      setSelectedIndex(-1);
      inputRef.current?.blur();
    }
  };

  return (
    <div className="relative w-full max-w-2xl">
      <div
        className={cn(
          "flex items-center w-full rounded-full border bg-white dark:bg-card py-3 px-4 shadow-sm transition-all duration-200",
          isFocused
            ? "border-blue-500 dark:border-blue-400 shadow-md ring-2 ring-blue-100 dark:ring-blue-900"
            : "border-gray-200 dark:border-gray-700"
        )}
      >
        <Search className="w-5 h-5 text-gray-400 mr-2" />
        <input
          ref={inputRef}
          type="text"
          value={value}
          onChange={handleInputChange}
          onFocus={handleFocus}
          onBlur={handleBlur}
          onKeyDown={handleKeyDown}
          placeholder="Create a prompt..."
          className="flex-1 bg-transparent outline-none text-gray-800 dark:text-gray-100 placeholder:text-gray-400"
          aria-label="Prompt input"
          autoComplete="off"
        />
        {isLoading && (
          <Loader2 className="w-5 h-5 text-gray-400 animate-spin" />
        )}
      </div>

      {/* Autocompletion dropdown */}
      {showCompletions && completions.length > 0 && (
        <div className="absolute left-0 right-0 mt-1 bg-white dark:bg-card rounded-lg shadow-lg border border-gray-200 dark:border-gray-700 py-2 z-10 max-h-60 overflow-y-auto">
          {completions.map((completion, index) => (
            <div
              key={completion}
              className={cn(
                "px-4 py-2 cursor-pointer text-gray-800 dark:text-gray-100 hover:bg-gray-100 dark:hover:bg-gray-800 transition-colors",
                selectedIndex === index && "bg-gray-100 dark:bg-gray-800"
              )}
              onClick={() => {
                onChange(completion);
                setShowCompletions(false);
              }}
            >
              {completion}
            </div>
          ))}
        </div>
      )}
    </div>
  );
}