"use client";

import { useState } from "react";
import { cn } from "@/lib/utils";
import { ArrowR<PERSON>, Star, Clock, ThumbsUp } from "lucide-react";
import { useFavorites } from "@/hooks/useFavorites";

interface SuggestionListProps {
  suggestions: string[];
  onSuggestionClick: (suggestion: string) => void;
}

export function SuggestionList({ suggestions, onSuggestionClick }: SuggestionListProps) {
  const [hoveredIndex, setHoveredIndex] = useState<number | null>(null);
  const { favorites, addFavorite, removeFavorite, isFavorite } = useFavorites();
  
  if (suggestions.length === 0) {
    return (
      <div className="w-full text-center py-8">
        <p className="text-muted-foreground">No se encontraron sugerencias</p>
      </div>
    );
  }

  return (
    <div className="w-full max-w-2xl mx-auto">
      <div className="space-y-6">
        {suggestions.map((suggestion, index) => (
          <div
            key={index}
            className="group"
            onClick={() => onSuggestionClick(suggestion)}
          >
            <div className="flex items-start gap-4 cursor-pointer">
              <div className="flex-1">
                <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400 group-hover:underline mb-1">
                  {suggestion}
                </h3>
                <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400 mb-2">
                  <span className="flex items-center gap-1">
                    <Clock className="w-4 h-4" />
                    Usado recientemente
                  </span>
                  <span className="flex items-center gap-1">
                    <ThumbsUp className="w-4 h-4" />
                    98% efectivo
                  </span>
                </div>
                <p className="text-sm text-gray-600 dark:text-gray-400">
                  Este prompt ha sido optimizado para obtener mejores resultados. Perfecto para {index % 2 === 0 ? "principiantes" : "usuarios avanzados"}.
                </p>
              </div>
              <button
                onClick={(e) => {
                  e.stopPropagation();
                  isFavorite(suggestion) ? removeFavorite(suggestion) : addFavorite(suggestion);
                }}
                className="text-gray-400 hover:text-yellow-400 transition-colors p-2"
              >
                <Star
                  className={cn(
                    "w-5 h-5",
                    isFavorite(suggestion) && "fill-yellow-400 text-yellow-400"
                  )}
                />
              </button>
            </div>
          </div>
        ))}
      </div>

      {favorites.length > 0 && (
        <div className="mt-12 border-t pt-8">
          <h2 className="text-xl font-semibold mb-6 text-gray-800 dark:text-gray-200 flex items-center gap-2">
            <Star className="w-5 h-5 fill-yellow-400 text-yellow-400" />
            Prompts Favoritos
          </h2>
          <div className="space-y-6">
            {favorites.map((favorite, index) => (
              <div
                key={index}
                className="group"
                onClick={() => onSuggestionClick(favorite)}
              >
                <div className="flex items-start gap-4 cursor-pointer">
                  <div className="flex-1">
                    <h3 className="text-lg font-medium text-blue-600 dark:text-blue-400 group-hover:underline mb-1">
                      {favorite}
                    </h3>
                    <div className="flex items-center gap-3 text-sm text-gray-600 dark:text-gray-400">
                      <span className="flex items-center gap-1">
                        <Star className="w-4 h-4 fill-yellow-400" />
                        Favorito
                      </span>
                    </div>
                  </div>
                  <button
                    onClick={(e) => {
                      e.stopPropagation();
                      removeFavorite(favorite);
                    }}
                    className="text-yellow-400 hover:text-gray-400 transition-colors p-2"
                  >
                    <Star className="w-5 h-5 fill-yellow-400" />
                  </button>
                </div>
              </div>
            ))}
          </div>
        </div>
      )}
    </div>
  );
}