"use client";

import { useState, useEffect } from "react";
import { useDebounce } from "@/hooks/useDebounce";
import { generateSuggestions } from "@/utils/suggestions";

export function usePromptSuggestions(input: string) {
  const [suggestions, setSuggestions] = useState<string[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const debouncedInput = useDebounce(input, 500);

  useEffect(() => {
    if (debouncedInput.length === 0) {
      setSuggestions([]);
      return;
    }

    const fetchSuggestions = async () => {
      setIsLoading(true);
      try {
        // Simulate a slight delay as if we're querying an API
        await new Promise(resolve => setTimeout(resolve, 300));
        const results = generateSuggestions(debouncedInput);
        setSuggestions(results);
      } finally {
        setIsLoading(false);
      }
    };

    fetchSuggestions();
  }, [debouncedInput]);

  return { suggestions, isLoading };
}