// Categorías de prompts organizadas por tipo con descripciones y metadatos
const promptCategories = {
  development: {
    prompts: [
      {
        text: "Crea un componente React para una barra de navegación responsive",
        description: "Genera un componente de navegación moderno con soporte móvil y animaciones suaves",
        difficulty: "Intermedio",
        useCase: "Desarrollo Frontend"
      },
      {
        text: "Optimiza el rendimiento de una aplicación React con muchos estados",
        description: "Mejora la velocidad y eficiencia de tu aplicación React utilizando las mejores prácticas",
        difficulty: "Avanzado",
        useCase: "Optimización"
      },
      {
        text: "Implementa autenticación con NextAuth.js en una aplicación Next.js",
        description: "Configura un sistema de autenticación seguro y escalable",
        difficulty: "Intermedio",
        useCase: "Seguridad"
      }
    ]
  },
  design: {
    prompts: [
      {
        text: "Diseña una landing page moderna para una startup de tecnología",
        description: "Crea un diseño atractivo y conversion-focused para una empresa tech",
        difficulty: "Intermedio",
        useCase: "Diseño Web"
      },
      {
        text: "Crea un sistema de diseño con Tailwind CSS y shadcn/ui",
        description: "Desarrolla componentes reutilizables y consistentes para tu aplicación",
        difficulty: "Avanzado",
        useCase: "Sistemas de Diseño"
      }
    ]
  },
  business: {
    prompts: [
      {
        text: "Genera un plan de marketing digital para un producto SaaS",
        description: "Estrategia completa de marketing para software como servicio",
        difficulty: "Intermedio",
        useCase: "Marketing"
      },
      {
        text: "Crea una estrategia de precios para una aplicación móvil",
        description: "Estructura de precios optimizada para maximizar ingresos y retención",
        difficulty: "Avanzado",
        useCase: "Monetización"
      }
    ]
  }
};

// Función para obtener sugerencias basadas en la categoría y el input
export function generateSuggestions(input: string): string[] {
  if (!input || input.length < 2) return [];
  
  const lowercasedInput = input.toLowerCase();
  let suggestions: string[] = [];
  
  // Buscar en todas las categorías
  Object.values(promptCategories).forEach(category => {
    const matches = category.prompts
      .filter(prompt => 
        prompt.text.toLowerCase().includes(lowercasedInput) ||
        prompt.description.toLowerCase().includes(lowercasedInput)
      )
      .map(prompt => prompt.text);
    suggestions.push(...matches);
  });
  
  // Añadir el input como una sugerencia si es lo suficientemente específico
  if (input.length > 15) {
    suggestions.unshift(input);
  }
  
  return Array.from(new Set(suggestions)).slice(0, 8);
}

// Autocompletado mientras el usuario escribe
export function getAutocompletions(input: string): string[] {
  if (!input || input.length < 2) return [];
  
  const lowercasedInput = input.toLowerCase();
  let completions: string[] = [];
  
  Object.values(promptCategories).forEach(category => {
    const matches = category.prompts
      .filter(prompt => 
        prompt.text.toLowerCase().startsWith(lowercasedInput)
      )
      .map(prompt => prompt.text);
    completions.push(...matches);
  });
  
  return Array.from(new Set(completions)).slice(0, 5);
}

// Obtener todas las categorías de prompts
export function getPromptCategories() {
  return Object.entries(promptCategories).map(([key, category]) => ({
    name: key.charAt(0).toUpperCase() + key.slice(1),
    prompts: category.prompts.map(p => p.text)
  }));
}