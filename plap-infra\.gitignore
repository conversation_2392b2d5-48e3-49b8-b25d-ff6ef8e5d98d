# === Archivos de Configuración Local/Sensible ===
.env # Si creas uno aquí para variables de Terraform o Docker locales
.env*.local
.env.*.local
.env

# === Archivos de Estado Terraform ===
# ¡NUNCA subir el estado de Terraform a Git si contiene secretos! Usa backends remotos seguros.
# *.tfstate
# *.tfstate.backup
.terraform.lock.hcl # Gestionado por Terraform, a menudo se incluye en gitignore

# === Archivos del Sistema Operativo ===
.DS_Store
Thumbs.db
ehthumbs.db

# === Archivos de IDEs / Editores ===
.idea/
.vscode/
*.sublime-project
*.sublime-workspace

# === Archivos Temporales ===
*~
*.tmp
*.swp
*.swo

# === Credenciales (Aunque no deberían estar aquí) ===
*.pem
*.key
*.cer
*.p12
*.pfx
*.jks
google-cloud-key.json # Por si acaso

# === Archivos específicos de Windows ===
desktop.ini