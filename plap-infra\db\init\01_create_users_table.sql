-- plap-infra/db/init/01_create_users_table.sql
DROP TABLE IF EXISTS credit_transactions CASCADE; -- <PERSON><PERSON><PERSON> dependientes primero
DROP TABLE IF EXISTS invoices CASCADE;            -- <PERSON><PERSON><PERSON> dependientes primero
DROP TABLE IF EXISTS users CASCADE;
DROP TABLE IF EXISTS plans CASCADE;               -- <PERSON> tienes tabla plans

-- Si tienes tabla plans, créala ANTES que users si users tiene FK a plans
CREATE TABLE IF NOT EXISTS plans (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    name VARCHAR(255) UNIQUE NOT NULL,
    description TEXT,
    monthly_cost FLOAT NOT NULL,
    monthly_credits INTEGER NOT NULL,
    features JSONB,
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') NOT NULL
);

INSERT INTO plans (id, name, monthly_cost, monthly_credits, description, features, is_active) VALUES
(gen_random_uuid(), 'Free', 0.0, 100, 'Free plan with basic features', '{"feature1": true, "max_prompts": 10}', true),
(gen_random_uuid(), 'Pro', 19.99, 1000, 'Pro plan with advanced features', '{"feature1": true, "feature2": true, "max_prompts": 100}', true)
ON CONFLICT (name) DO NOTHING; -- Evitar error si los planes ya existen por alguna razón

CREATE TABLE users (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    email VARCHAR(255) UNIQUE NOT NULL,
    hashed_password VARCHAR(255) NOT NULL,
    name VARCHAR(255) NULL,
    avatar_url VARCHAR(512) NULL,
    credits_total INTEGER DEFAULT 0 NOT NULL,
    credits_used INTEGER DEFAULT 0 NOT NULL,
    current_plan_id UUID REFERENCES plans(id) ON DELETE SET NULL, -- FK a plans
    notification_preferences JSONB NULL, -- ¡AÑADIDO!
    is_active BOOLEAN DEFAULT TRUE NOT NULL,
    is_superuser BOOLEAN DEFAULT FALSE NOT NULL,
    created_at TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') NOT NULL,
    updated_at TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') NOT NULL
);
CREATE INDEX IF NOT EXISTS idx_users_email ON users(email);

-- (Opcional: crea otras tablas como credit_transactions, invoices aquí si no tienes otros scripts para ellas)
-- CREATE TABLE credit_transactions ...
-- CREATE TABLE invoices ...