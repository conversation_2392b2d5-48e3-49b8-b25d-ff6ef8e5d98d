-- Tabla para almacenar los prompts guardados por los usuarios
CREATE TABLE prompt_records (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(), -- ID único para cada prompt guardado
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE, -- ID del usuario dueño (se borra si el usuario se borra)
    name VARCHAR(255) NOT NULL, -- Nombre que el usuario le dio al prompt
    created_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Fecha de creación
    updated_at TIMESTAMP WITH TIME ZONE DEFAULT CURRENT_TIMESTAMP, -- Fecha de última modificación
    status VARCHAR(50) DEFAULT 'saved', -- Estado del prompt ('saved', 'in_workflow', 'archived', etc.)
    final_text TEXT, -- El texto final del prompt generado/editado
    structured_draft JSONB, -- La estructura del prompt para el editor (JSON)
    technique_id VARCHAR(50), -- ID de la técnica de prompting usada
    llm_id VARCHAR(50), -- ID del LLM usado para la última ejecución
    associated_attachment_ids JSONB, -- Lista de IDs de archivos adjuntos asociados a la última ejecución/guardado
    latest_run_status VARCHAR(50), -- Estado de la última ejecución del workflow (ej: 'completed', 'failed')
    latest_run_cost INTEGER, -- Costo en créditos de la última ejecución
    quality_score FLOAT -- Puntuación de calidad (del Reviewer o SIRIUS)
    -- Puedes añadir un campo VECTOR aquí si decides usar pgvector para embeddings de los prompts guardados
    -- content_embedding vector(DIMENSION_DEL_MODELO_DE_EMBEDDING)
);

-- Índice para buscar prompts por usuario muy rápido
CREATE INDEX idx_prompt_records_user_id ON prompt_records (user_id);