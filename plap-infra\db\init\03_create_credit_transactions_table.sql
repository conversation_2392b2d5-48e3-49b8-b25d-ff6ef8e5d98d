-- plap-infra/db/init/0X_create_credit_transactions_table.sql
DROP TABLE IF EXISTS credit_transactions CASCADE;

CREATE TABLE credit_transactions (
    id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
    user_id UUID NOT NULL REFERENCES users(id) ON DELETE CASCADE,
    type VARCHAR(50) NOT NULL, -- 'usage_deduction', 'purchase', 'refund', 'monthly_grant', etc.
    credits_amount INTEGER NOT NULL, -- Negativo para deducción, positivo para adición
    timestamp TIMESTAMPTZ DEFAULT (CURRENT_TIMESTAMP AT TIME ZONE 'UTC') NOT NULL,
    prompt_workflow_id VARCHAR(100) NULL, -- Cambiado a VARCHAR
    description TEXT NULL,
    related_invoice_id UUID NULL -- REFERENCES invoices(id) ON DELETE SET NULL (si tienes tabla invoices)
);

CREATE INDEX IF NOT EXISTS idx_credit_transactions_user_id ON credit_transactions(user_id);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_type ON credit_transactions(type);
CREATE INDEX IF NOT EXISTS idx_credit_transactions_timestamp ON credit_transactions(timestamp);

COMMENT ON TABLE credit_transactions IS 'Registra todas las transacciones de créditos de los usuarios.';
-- ... (más comentarios para las columnas) ...