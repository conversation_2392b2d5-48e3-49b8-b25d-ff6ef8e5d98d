# plap-infra/docker-compose.dev.yml
version: '3.8' # Debe coincidir con la versión de tu docker-compose.yml principal

services:
  auth-service:
    volumes:
      # Mapea el código fuente local al contenedor para hot-reloading
      # La imagen base ya tiene plap-shared-models mapeado desde docker-compose.yml
      # Este volumen sobrescribe el /app copiado por el Dockerfile
      - ../plap-auth-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 80 --reload
    # environment:
      # - LOG_LEVEL=DEBUG # Ejemplo de variable de entorno para desarrollo

  user-service:
    volumes:
      - ../plap-user-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 80 --reload
    # environment:
      # - LOG_LEVEL=DEBUG

  prompt-library-service:
    volumes:
      - ../plap-prompt-library-service:/app
    command: uvicorn main:app --host 0.0.0.0 --port 80 --reload
    # environment:
      # - LOG_LEVEL=DEBUG

  agents:
    volumes:
      - ../plap-agents:/app
      # El volumen para google-cloud-key.json y local_uploads ya está en docker-compose.yml, no necesita repetirse
      # El volumen para plap-shared-models ya está en docker-compose.yml
    command: uvicorn main:app --host 0.0.0.0 --port 80 --reload
    # environment:
      # - LOG_LEVEL=DEBUG

  orchestrator:
    volumes:
      - ../plap-orchestrator:/app
    command: uvicorn main:app --host 0.0.0.0 --port 80 --reload
    # environment:
      # - LOG_LEVEL=DEBUG

  backend-api:
    volumes:
      - ../plap-backend-api:/app
    command: uvicorn main:app --host 0.0.0.0 --port 80 --reload
    # environment:
      # - LOG_LEVEL=DEBUG

  # No es necesario redefinir traefik, postgres, qdrant aquí
  # a menos que quieras cambiar alguna configuración específicamente para desarrollo.
  # Por ejemplo, si quisieras exponer el puerto de postgres directamente al host
  # solo en desarrollo, podrías añadirlo aquí, pero ya lo tienes configurable en el .yml principal.