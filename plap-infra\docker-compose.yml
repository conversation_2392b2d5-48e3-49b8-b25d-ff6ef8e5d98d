# docker-compose.yml (MODIFICADO v0.9.3 - Enfoque ADC para Agents y correcciones)
# Ubicación: ./plap-infra/docker-compose.yml

services:
  traefik:
    image: "traefik:v3.0"
    container_name: plap_traefik_proxy
    command:
      - "--entrypoints.web.address=:80"
      - "--entrypoints.websecure.address=:443"
      - "--providers.docker=true"
      - "--providers.docker.exposedbydefault=false"
      - "--api.insecure=true"
      - "--entrypoints.web.http.redirections.entrypoint.to=websecure"
      - "--entrypoints.web.http.redirections.entrypoint.scheme=https"
    ports:
      - "80:80"
      - "443:443"
      - "8080:8080"
    volumes:
      - "/var/run/docker.sock:/var/run/docker.sock:ro"
    restart: unless-stopped
    networks:
      - plap_network

  postgres:
    image: pgvector/pgvector:pg16
    container_name: plap_postgres
    environment:
      POSTGRES_DB: ${POSTGRES_DB:-plap_db}
      POSTGRES_USER: ${POSTGRES_USER:-plap_user}
      POSTGRES_PASSWORD: ${POSTGRES_PASSWORD}
    ports:
      - "${POSTGRES_PORT:-5432}:5432"
    volumes:
      - postgres_data:/var/lib/postgresql/data
      - ./db/init:/docker-entrypoint-initdb.d
    restart: unless-stopped
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U ${POSTGRES_USER:-plap_user} -d ${POSTGRES_DB:-plap_db}"]
      interval: 10s
      timeout: 5s
      retries: 5
    networks:
      - plap_network

  qdrant:
    image: qdrant/qdrant:latest
    container_name: plap_qdrant
    ports:
      - "${QDRANT_PORT:-6333}:6333"
      - "${QDRANT_GRPC_PORT:-6334}:6334"
    volumes:
      - qdrant_data:/qdrant/storage
    restart: unless-stopped
    networks:
      - plap_network

  auth-service:
    build:
      context: ../plap-auth-service
      dockerfile: Dockerfile
      args:
        V: ${V:-latest}
    container_name: plap_auth_service
    env_file:
      - .env
    environment:
      PYTHONPATH: /app:/opt/plap-shared-models
    volumes:
      - ../plap-shared-models:/opt/plap-shared-models:ro
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
    networks:
      - plap_network

  user-service:
    build:
      context: ../plap-user-service
      dockerfile: Dockerfile
      args:
        V: ${V:-latest}
    container_name: plap_user_service
    env_file:
      - .env
    environment:
      PYTHONPATH: /app:/opt/plap-shared-models
      AUTH_SERVICE_INTERNAL_URL: ${AUTH_SERVICE_INTERNAL_URL:-http://auth-service:80}
    volumes:
      - ../plap-shared-models:/opt/plap-shared-models:ro
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      auth-service:
        condition: service_started
    networks:
      - plap_network

  prompt-library-service:
    build:
      context: ../plap-prompt-library-service
      dockerfile: Dockerfile
      args:
        V: ${V:-latest}
    container_name: plap_library_service
    env_file:
      - .env
    environment:
      PYTHONPATH: /app:/opt/plap-shared-models
      QDRANT_URL: ${QDRANT_INTERNAL_URL:-http://qdrant:6333}
    volumes:
      - ../plap-shared-models:/opt/plap-shared-models:ro
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_started
    networks:
      - plap_network

  agents: # <--- SERVICIO MODIFICADO ---
    build:
      context: ../plap-agents
      # Asegúrate de que plap-agents/Dockerfile NO define ENV GOOGLE_APPLICATION_CREDENTIALS
      # y que crea el home directory para appuser si montas ADC en /home/<USER>/...
      dockerfile: Dockerfile 
      args:
        V: ${V:-latest}
    container_name: plap_agents_service 
    env_file:
      - .env
    environment:
      # NO SE DEFINE GOOGLE_APPLICATION_CREDENTIALS AQUÍ. El SDK lo encontrará vía ADC.
      PYTHONPATH: /app:/opt/plap-shared-models
      QDRANT_URL: ${QDRANT_INTERNAL_URL:-http://qdrant:6333}
      ZEP_API_KEY: ${ZEP_API_KEY}
      GOOGLE_CLOUD_PROJECT_ID: ${GOOGLE_CLOUD_PROJECT_ID} # Aún útil para el SDK
      VERTEX_AI_LOCATION: ${VERTEX_AI_LOCATION} # Añadido para pasar desde .env
      GEMINI_FLASH_MODEL_NAME: ${GEMINI_FLASH_MODEL_NAME} # Añadido para pasar desde .env
      TEXT_EMBEDDING_MODEL_NAME: ${GOOGLE_EMBEDDING_MODEL} # Añadido para pasar desde .env
      LOCAL_UPLOADS_DIR: /app/uploads_temp
    volumes:
      - ../plap-shared-models:/opt/plap-shared-models:ro
      - ../local_uploads:/app/uploads_temp
      # --- INICIO DE CORRECCIÓN: Montar ADC para el usuario 'appuser' ---
      # Monta el archivo de credenciales ADC de tu host (Windows) 
      # en la ubicación estándar dentro del contenedor para el usuario 'appuser'.
      # Asegúrate de que la ruta del host 'C:/Users/<USER>/AppData/Roaming/gcloud/application_default_credentials.json' existe y es correcta.
      - C:/Users/<USER>/AppData/Roaming/gcloud/application_default_credentials.json:/home/<USER>/.config/gcloud/application_default_credentials.json:ro
      # --- FIN DE CORRECCIÓN ---
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_started
    networks:
      - plap_network

  orchestrator:
    build:
      context: ../plap-orchestrator
      dockerfile: Dockerfile
      args:
        V: ${V:-latest}
    container_name: plap_orchestrator 
    env_file:
      - .env
    environment:
      PYTHONPATH: /app:/opt/plap-shared-models
      USER_SERVICE_INTERNAL_URL: ${USER_SERVICE_INTERNAL_URL:-http://user-service:80}
      PROMPT_LIBRARY_SERVICE_INTERNAL_URL: ${PROMPT_LIBRARY_SERVICE_INTERNAL_URL:-http://prompt-library-service:80}
      AGENTS_SERVICE_INTERNAL_URL: ${AGENTS_SERVICE_INTERNAL_URL:-http://agents:80}
      QDRANT_URL: ${QDRANT_INTERNAL_URL:-http://qdrant:6333}
      ZEP_API_KEY: ${ZEP_API_KEY}
    volumes:
      - ../plap-shared-models:/opt/plap-shared-models:ro
    restart: unless-stopped
    depends_on:
      postgres:
        condition: service_healthy
      qdrant:
        condition: service_started
      auth-service:
        condition: service_started
      user-service:
        condition: service_started
      prompt-library-service:
        condition: service_started
      agents: 
        condition: service_started
    networks:
      - plap_network

  backend-api:
    build:
      context: ../plap-backend-api
      dockerfile: Dockerfile
      args:
        V: ${V:-latest}
    container_name: plap_api_gateway
    env_file:
      - .env
    environment:
      PYTHONPATH: /app:/opt/plap-shared-models
      AUTH_SERVICE_INTERNAL_URL: ${AUTH_SERVICE_INTERNAL_URL:-http://auth-service:80}
      USER_SERVICE_INTERNAL_URL: ${USER_SERVICE_INTERNAL_URL:-http://user-service:80}
      PROMPT_LIBRARY_SERVICE_INTERNAL_URL: ${PROMPT_LIBRARY_SERVICE_INTERNAL_URL:-http://prompt-library-service:80}
      ORCHESTRATOR_SERVICE_INTERNAL_URL: ${ORCHESTRATOR_SERVICE_INTERNAL_URL:-http://orchestrator:80}
      AGENTS_SERVICE_INTERNAL_URL: ${AGENTS_SERVICE_INTERNAL_URL:-http://agents:80}
      QDRANT_INTERNAL_URL: ${QDRANT_INTERNAL_URL:-http://qdrant:6333}
      ZEP_API_KEY: ${ZEP_API_KEY}
    volumes:
      - ../plap-shared-models:/opt/plap-shared-models:ro
      - ../local_uploads:/app/uploads_temp
    restart: unless-stopped
    depends_on:
      auth-service:
        condition: service_started
      user-service:
        condition: service_started
      prompt-library-service:
        condition: service_started
      orchestrator:
        condition: service_started
      agents:
        condition: service_started
      qdrant:
        condition: service_started
    labels:
      - "traefik.enable=true"
      - "traefik.http.routers.plap-api-http.entrypoints=web"
      - "traefik.http.routers.plap-api-http.rule=Host(`localhost`)"
      - "traefik.http.routers.plap-api-secure.entrypoints=websecure"
      - "traefik.http.routers.plap-api-secure.rule=Host(`localhost`)"
      - "traefik.http.routers.plap-api-secure.tls=true"
      - "traefik.http.routers.plap-api-secure.service=plap-api-service"
      - "traefik.http.services.plap-api-service.loadbalancer.server.port=80"
    networks:
      - plap_network

volumes:
  postgres_data:
  qdrant_data:

networks:
  plap_network:
    driver: bridge