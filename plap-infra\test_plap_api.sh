#!/bin/bash

# Paso 1: Obtener el token de acceso
echo "Obteniendo token JWT..."
TOKEN_RESPONSE=$(curl -s -k -X POST \
  "https://localhost/api/v1/auth/login" \
  -H "Content-Type: application/x-www-form-urlencoded" \
  -d "username=<EMAIL>&password=StrongPassword123!")

if [[ -z "$TOKEN_RESPONSE" || "$TOKEN_RESPONSE" == *"Not Found"* || "$TOKEN_RESPONSE" == *"Error"* ]]; then
  echo "Error al obtener el token:"
  echo "$TOKEN_RESPONSE"
  exit 1
fi
echo "Respuesta del login: $TOKEN_RESPONSE"

# Paso 2: Intentar extraer el access_token
ACCESS_TOKEN="" # Inicializar vacía

if command -v jq &> /dev/null; then
  echo "jq encontrado, usando jq para extraer el token..."
  ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | jq -r .access_token)
else
  echo "jq no encontrado."
fi

# Si jq falló o no se encontró, intentar con Python
if [[ -z "$ACCESS_TOKEN" || "$ACCESS_TOKEN" == "null" ]]; then
  if command -v python3 &> /dev/null; then
    echo "Intentando con Python 3 para extraer el token..."
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | python3 -c "import sys, json; data = json.load(sys.stdin); print(data.get('access_token', ''))")
  elif command -v python &> /dev/null; then
    echo "Python 3 no encontrado, intentando con Python (legacy) para extraer el token..."
    ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | python -c "import sys, json; data = json.load(sys.stdin); print data.get('access_token', '')")
  fi
fi

# Verificar que el token se extrajo
if [[ -z "$ACCESS_TOKEN" || "$ACCESS_TOKEN" == "null" ]]; then
  echo "No se pudo extraer el ACCESS_TOKEN de la respuesta usando jq ni Python."
  # Como último recurso, intento muy básico con sed (puede fallar si el JSON cambia)
  echo "Intentando extracción básica con sed (menos fiable)..."
  ACCESS_TOKEN=$(echo "$TOKEN_RESPONSE" | sed -n 's/.*"access_token":"\([^"]*\)".*/\1/p')
  if [[ -z "$ACCESS_TOKEN" || "$ACCESS_TOKEN" == "null" ]]; then
    echo "Extracción con sed también falló."
    exit 1
  fi
fi

echo "-------------------------------------"
echo "ACCESS_TOKEN EXTRAÍDO: $ACCESS_TOKEN"
echo "-------------------------------------"

# Ahora usar el token para llamar al endpoint de prompts
echo "Llamando a /api/v1/prompts/ con el token..."
curl -k -H "Authorization: Bearer $ACCESS_TOKEN" -H "Content-Type: application/json" \
  "https://localhost/api/v1/prompts/" \
  -d '{"prompt_text_idea": "Prueba automatizada desde script Bash.", "target_llm_model": "gemini-1.0-flash-001", "workflow_type": "default"}'

echo # Añadir una nueva línea al final para claridad