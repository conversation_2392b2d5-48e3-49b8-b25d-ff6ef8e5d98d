# plap-orchestrator/main.py
from fastapi import FastAPI, HTTPException, Header, Request # Request añadido
from fastapi.middleware.cors import CORSMiddleware
import os
from typing import Optional, Dict, Any
from pydantic import BaseModel # No es estrictamente necesario aquí si todos los modelos están en workflow.py
import traceback # Para un traceback completo en la excepción genérica

# --- Importar Lógica y Schemas desde workflow.py ---
import workflow # Importación absoluta

# --- Configuración de la Aplicación FastAPI ---
app = FastAPI(
    title="PLAP Orchestrator Service",
    version="0.9.4_ZepSessionDebugRaw", # Actualización de versión
    description="Servicio para orquestar workflows de prompting en PLAP.",
)

# --- Configuración de CORS ---
_origins_str = os.getenv("CORS_ORIGINS_ORCHESTRATOR_SERVICE", "*")
origins = _origins_str.split(",") if _origins_str and _origins_str != "*" else ["*"]
app.add_middleware(
    CORSMiddleware,
    allow_origins=origins,
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

# --- Endpoints del Orchestrator ---
@app.post("/internal/workflows", response_model=workflow.WorkflowState, status_code=201, tags=["Workflow Lifecycle"])
async def initiate_workflow_endpoint(
    http_request: Request, # Usamos el Request crudo de FastAPI para inspeccionar el body
    x_user_id: str = Header(..., alias="X-User-ID")
):
    print(f"[ORCHESTRATOR ENDPOINT /internal/workflows] === INICIO SOLICITUD ===")
    print(f"[ORCHESTRATOR ENDPOINT] User ID (X-User-ID): {x_user_id}")
    
    request_data_obj: Optional[workflow.WorkflowInitiationRequest] = None
    try:
        raw_body = await http_request.json() # Leemos el JSON crudo del cuerpo
        print(f"[ORCHESTRATOR ENDPOINT] Cuerpo JSON CRUDO recibido: {raw_body}")
        
        # Ahora intentamos parsear manualmente con el modelo Pydantic
        request_data_obj = workflow.WorkflowInitiationRequest(**raw_body)
        print(f"[ORCHESTRATOR ENDPOINT] Payload PARSEADO (request_data tipo {type(request_data_obj)}): {request_data_obj.model_dump(exclude_none=True)}")
        print(f"[ORCHESTRATOR ENDPOINT] Valor de request_data_obj.zep_session_id directamente: {request_data_obj.zep_session_id}")

    except Exception as parse_exc:
        print(f"[ORCHESTRATOR ENDPOINT] ERROR AL PROCESAR/PARSEAR EL CUERPO DE LA SOLICITUD: {parse_exc}")
        traceback.print_exc()
        raise HTTPException(status_code=400, detail=f"Error procesando o parseando el payload en orchestrator: {str(parse_exc)}")

    if request_data_obj is None: # No debería pasar si el parseo fue exitoso, pero por si acaso
        print("[ORCHESTRATOR ENDPOINT] request_data_obj es None después del parseo, algo muy raro ocurrió.")
        raise HTTPException(status_code=500, detail="Error interno procesando la solicitud en orchestrator.")

    try:
        created_workflow = await workflow.initiate_new_workflow(request_data_obj, x_user_id)
        
        if created_workflow.status in [workflow.WorkflowStatus.FAILED, workflow.WorkflowStatus.CREDIT_CHECK_FAILED]:
            status_code_to_return = 402 if created_workflow.status == workflow.WorkflowStatus.CREDIT_CHECK_FAILED else 400
            error_detail = created_workflow.error_message or "Error iniciando el workflow."
            print(f"[ORCHESTRATOR ENDPOINT] Workflow fallido, devolviendo HTTP {status_code_to_return} con detalle: {error_detail}")
            raise HTTPException(status_code=status_code_to_return, detail=error_detail)
        
        print(f"[ORCHESTRATOR ENDPOINT] Workflow creado/procesado exitosamente: {created_workflow.workflow_id}, Zep Session: {created_workflow.zep_session_id}")
        return created_workflow

    except HTTPException as e_http: # Re-lanzar HTTPExceptions conocidas del workflow
        raise e_http
    except Exception as e_main_endpoint:
        print(f"[ORCHESTRATOR ENDPOINT] EXCEPCIÓN INESPERADA en la lógica de initiate_new_workflow: {e_main_endpoint}")
        traceback.print_exc()
        raise HTTPException(status_code=500, detail=f"Error interno inesperado en orchestrator al procesar workflow: {str(e_main_endpoint)}")

@app.get("/internal/workflows/{workflow_id}", response_model=workflow.WorkflowState, tags=["Workflow Lifecycle"])
async def get_workflow_status_endpoint(
    workflow_id: str,
    x_user_id: str = Header(..., alias="X-User-ID")
):
    print(f"[ORCHESTRATOR ENDPOINT /internal/workflows/{{workflow_id}}] GET para wf: {workflow_id}, user: {x_user_id}")
    current_workflow_state = workflow.get_workflow_state(workflow_id, x_user_id)
    if not current_workflow_state:
        raise HTTPException(status_code=404, detail="Workflow no encontrado o no autorizado.")
    return current_workflow_state

@app.put("/internal/workflows/{workflow_id}/action", response_model=workflow.WorkflowState, tags=["Workflow Actions"])
async def handle_workflow_action_endpoint(
    workflow_id: str,
    action_request: workflow.WorkflowActionRequest,
    x_user_id: str = Header(..., alias="X-User-ID")
):
    print(f"[ORCHESTRATOR ENDPOINT /internal/workflows/{{workflow_id}}/action] PUT para wf: {workflow_id}, user: {x_user_id}, action: {action_request.action}")
    updated_workflow = await workflow.process_workflow_action(workflow_id, x_user_id, action_request)
    if not updated_workflow:
        raise HTTPException(status_code=404, detail="Workflow no encontrado o acción no permitida.")
    if updated_workflow.status == workflow.WorkflowStatus.FAILED:
        error_detail = updated_workflow.error_message or "Error procesando la acción del workflow."
        print(f"[ORCHESTRATOR ENDPOINT] Acción de workflow fallida, devolviendo HTTP 400 con detalle: {error_detail}")
        raise HTTPException(status_code=400, detail=error_detail)
    return updated_workflow

@app.get("/health", tags=["Health"])
async def health_check():
    return {"status": "healthy", "service": "Orchestrator Service", "version": app.version}

@app.on_event("startup")
async def startup_event():
    print("Orchestrator Service (main.py): Startup event.")

@app.on_event("shutdown")
async def shutdown_event():
    print("Orchestrator Service (main.py): Shutdown event.")
    if hasattr(workflow, 'on_shutdown_workflow') and callable(workflow.on_shutdown_workflow):
        await workflow.on_shutdown_workflow()

print(f"[ORCHESTRATOR_MAIN.PY] Módulo main.py cargado y endpoints definidos.")