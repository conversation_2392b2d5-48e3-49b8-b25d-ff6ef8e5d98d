# Framework web (si expone endpoints internos para API Gateway)
fastapi==0.111.0
uvicorn==0.30.1 

# Validación de datos
pydantic==2.7.1 

# Cliente HTTP para llamar a otros servicios (Agentes, User Service, etc.)
httpx==0.27.0

# Base de datos relacional (para gestionar estado del workflow en prompt_records)
sqlalchemy==2.0.30
psycopg2-binary==2.9.9

# Cliente para Zep Cloud (para contexto/memoria y logging)
zep-cloud # Dejar sin versión fija para que instale la última compatible

# Para leer variables de .env directamente
python-dotenv==1.0.0 

# Opcional: Google ADK si decides usarlo formalmente como framework de orquestación
# google-adk # (Verificar nombre exacto del paquete e instalación) 
# Por ahora, lo implementamos con Python/FastAPI puro.