# plap-orchestrator/workflow.py
import httpx
import uuid
from typing import Dict, Any, Optional, List, Type, cast
from enum import Enum
from datetime import datetime, timezone
from pydantic import BaseModel, Field, ConfigDict
import os

# --- URLs de Servicios Internos ---
USER_SERVICE_URL = os.getenv("USER_SERVICE_INTERNAL_URL", "http://user-service:80")
PROMPT_LIBRARY_SERVICE_URL = os.getenv("PROMPT_LIBRARY_SERVICE_INTERNAL_URL", "http://prompt-library-service:80")
AGENTS_SERVICE_URL = os.getenv("AGENTS_SERVICE_INTERNAL_URL", "http://agents:80")
ZEP_API_KEY = os.getenv("ZEP_API_KEY")
ZEP_API_URL = os.getenv("ZEP_API_URL")

# --- Declaración de Tipos para Schemas Compartidos ---
ImportedSharedPromptSchemaForLibrary: Optional[Type[BaseModel]] = None
LLMModelTypeShared: Optional[Type[Enum]] = None
WorkflowTypeShared: Optional[Type[Enum]] = None

_SHARED_SCHEMAS_IMPORTED_SUCCESSFULLY = False
_LLM_MODEL_TYPE_IMPORTED = False
_WORKFLOW_TYPE_IMPORTED = False

try:
    print("[WORKFLOW.PY] Intentando importar schemas compartidos desde 'schemas.prompt_schemas'...")
    try:
        from schemas.prompt_schemas import Prompt_v0_9_2 as ImportedPromptSchema
        ImportedSharedPromptSchemaForLibrary = ImportedPromptSchema
        print("[WORKFLOW.PY] Prompt_v0_9_2 importado con éxito.")
    except ImportError:
        print("[WORKFLOW.PY] WARNING: No se pudo importar Prompt_v0_9_2. Usando fallback.")

    try:
        from schemas.prompt_schemas import LLMModelType_v0_9_2 as ActualImportedLLMEnum
        LLMModelTypeShared = ActualImportedLLMEnum
        _LLM_MODEL_TYPE_IMPORTED = True
        print("[WORKFLOW.PY] LLMModelType_v0_9_2 importado con éxito.")
    except ImportError:
        print("[WORKFLOW.PY] WARNING: No se pudo importar LLMModelType_v0_9_2. Se usará fallback para LLMModelTypeShared.")

    try:
        from schemas.prompt_schemas import WorkflowType_v0_9_2 as ActualImportedWorkflowEnum
        WorkflowTypeShared = ActualImportedWorkflowEnum
        _WORKFLOW_TYPE_IMPORTED = True
        print("[WORKFLOW.PY] WorkflowType_v0_9_2 importado con éxito.")
    except ImportError as e:
        print(f"[WORKFLOW.PY] WARNING: No se pudo importar WorkflowType_v0_9_2: {e}. Se usará fallback para WorkflowTypeShared.")


    if ImportedSharedPromptSchemaForLibrary and _LLM_MODEL_TYPE_IMPORTED and _WORKFLOW_TYPE_IMPORTED:
        _SHARED_SCHEMAS_IMPORTED_SUCCESSFULLY = True
        print("[WORKFLOW.PY] Todos los schemas principales compartidos de prompt importados con éxito.")
    else:
        print("[WORKFLOW.PY] Algunos schemas compartidos de prompt no se pudieron importar. Se usarán fallbacks donde sea posible.")

except Exception as e_outer:
    print(f"[WORKFLOW.PY] ERROR GENERAL DURANTE IMPORTACIÓN DE SCHEMAS: {e_outer}")

if LLMModelTypeShared is None:
    class _LLMModelTypeSharedFallback(str, Enum):
        GEMINI_PRO = "gemini-pro"; GEMINI_FLASH = "gemini-1.0-flash-001"; GEMINI_1_5_PRO = "gemini-1.5-pro"
    LLMModelTypeShared = cast(Type[Enum], _LLMModelTypeSharedFallback)
    print("[WORKFLOW.PY] Usando fallback local para LLMModelTypeShared.")

if WorkflowTypeShared is None:
    class _WorkflowTypeSharedFallback(str, Enum):
        GUIDED = "guided"; EDITOR = "editor"; DEFAULT = "default"
    WorkflowTypeShared = cast(Type[Enum], _WorkflowTypeSharedFallback)
    print("[WORKFLOW.PY] Usando fallback local para WorkflowTypeShared.")

if ImportedSharedPromptSchemaForLibrary is None:
    class _PromptBaseFallback(BaseModel): name: str = Field(...); description: Optional[str] = None; tags: Optional[List[str]] = None; model_config = ConfigDict(protected_namespaces=())
    class _PromptSchemaForLibraryFallback(_PromptBaseFallback): final_prompt_text: Optional[str] = None; llm_model_used: Optional[str] = None; workflow_type_used: Optional[str] = None
    ImportedSharedPromptSchemaForLibrary = _PromptSchemaForLibraryFallback
    print("[WORKFLOW.PY] Usando fallback local para ImportedSharedPromptSchemaForLibrary.")


class WorkflowStatus(str, Enum):
    STARTED = "started"; PENDING_CREDIT_CHECK = "pending_credit_check"; CREDIT_CHECK_FAILED = "credit_check_failed"; PENDING_INITIAL_GENERATION = "pending_initial_generation"; PROCESSING_INITIAL_GENERATION = "processing_initial_generation"; COMPLETED = "completed"; FAILED = "failed"; SAVING = "saving"

# --- INICIO DE MODIFICACIÓN DEL REQUEST ---
class WorkflowInitiationRequest(BaseModel):
    prompt_text_idea: str = Field(..., description="Idea inicial.")
    target_llm_model: str # Ya era str, lo cual es flexible.
    workflow_type: str = "default" # Ya era str.
    zep_session_id: Optional[str] = Field(None, description="ID de sesión de Zep opcional para continuar.") # AÑADIDO
    model_config = ConfigDict(protected_namespaces=(), use_enum_values=True)
# --- FIN DE MODIFICACIÓN DEL REQUEST ---

class WorkflowActionRequest(BaseModel): action: str; payload: Optional[Dict[str, Any]] = None; model_config = ConfigDict(protected_namespaces=())
class WorkflowBase(BaseModel):
    user_id: str; status: WorkflowStatus; initial_prompt_text: Optional[str] = None; current_prompt_text: Optional[str] = None
    target_llm_model: Optional[str] = None; workflow_type: Optional[str] = "default"; generated_result_text: Optional[str] = None
    error_message: Optional[str] = None; zep_session_id: Optional[str] = None; model_config = ConfigDict(protected_namespaces=())
class WorkflowState(WorkflowBase):
    workflow_id: str = Field(default_factory=lambda: uuid.uuid4().hex)
    created_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    updated_at: datetime = Field(default_factory=lambda: datetime.now(timezone.utc))
    suggestions: Optional[List[str]] = Field(default_factory=list)
    refinements_history: List[Dict[str, Any]] = Field(default_factory=list)

workflows_in_progress: Dict[str, WorkflowState] = {}

async def call_internal_service(method: str, service_base_url: str, path: str, uid: Optional[str]=None, json_payload: Optional[Dict]=None) -> httpx.Response:
    headers = {"Content-Type": "application/json"}; _service_base_url = service_base_url.rstrip("/"); _path = path.lstrip("/")
    if uid: headers["X-User-ID"] = uid
    full_url = f"{_service_base_url}/{_path}"
    print(f"Orchestrator/Workflow: Calling {method} {full_url} for user {uid or 'N/A'} with payload: {str(json_payload)[:300]}...")
    async with httpx.AsyncClient(timeout=30.0) as client:
        try:
            response = await client.request(method, full_url, json=json_payload, headers=headers)
            print(f"Orchestrator/Workflow: Response from {full_url}: {response.status_code} - {response.text[:300]}")
            return response
        except httpx.TimeoutException as e: print(f"Orchestrator/Workflow: Timeout {full_url}: {e}"); return httpx.Response(status_code=504, json={"detail": f"Timeout: {e}"})
        except httpx.ConnectError as e: print(f"Orchestrator/Workflow: ConnectError {full_url}: {e}"); return httpx.Response(status_code=503, json={"detail": f"ConnectError: {e}"})
        except Exception as e: print(f"Orchestrator/Workflow: Error {full_url}: {e}"); return httpx.Response(status_code=500, json={"detail": f"Internal error: {e}"})

async def step_check_credits(workflow: WorkflowState, estimated_cost: int = 1) -> bool:
    print(f"WF {workflow.workflow_id}: Step - Credit Check (cost: {estimated_cost})")
    if estimated_cost <= 0: return True
    usage_payload = {"user_id": str(workflow.user_id), "cost": estimated_cost, "prompt_workflow_id": workflow.workflow_id,
                     "description": f"PLAP: WF {workflow.workflow_id[:8]} for '{workflow.initial_prompt_text[:20]}...'"}
    print(f"WF {workflow.workflow_id}: Payload to UserSvc: {usage_payload}")
    response = await call_internal_service("POST", USER_SERVICE_URL, "/internal/usage/record", uid=workflow.user_id, json_payload=usage_payload)
    if response.status_code != 200:
        workflow.status = WorkflowStatus.CREDIT_CHECK_FAILED; detail_msg = f"Credit check fail: {response.status_code}"
        try: detail_msg += f" - {response.json().get('detail', response.text[:200])}"
        except: detail_msg += f" - {response.text[:200]}"
        workflow.error_message = detail_msg; print(workflow.error_message); return False
    print(f"WF {workflow.workflow_id}: Credits OK. UserSvc Resp: {response.json()}"); return True

async def step_generate_initial_with_generator(workflow: WorkflowState) -> bool:
    print(f"WF {workflow.workflow_id}: Step - Initial Generation with GeneratorAgent")
    if not workflow.current_prompt_text:
        workflow.status = WorkflowStatus.FAILED
        workflow.error_message = "No hay 'current_prompt_text' para enviar al GeneratorAgent."
        print(workflow.error_message)
        return False
    payload_to_agent = {
        "prompt_idea": workflow.current_prompt_text,
        "target_llm_model": workflow.target_llm_model,
        "zep_session_id": workflow.zep_session_id # Pasar el zep_session_id del workflow
    }
    workflow.status = WorkflowStatus.PROCESSING_INITIAL_GENERATION
    workflows_in_progress[workflow.workflow_id] = workflow
    response = await call_internal_service(
        "POST",
        AGENTS_SERVICE_URL,
        "/agents/generator/process_idea",
        uid=workflow.user_id,
        json_payload=payload_to_agent
    )
    if response.status_code == 200:
        agent_response_data = response.json()
        workflow.generated_result_text = agent_response_data.get("generated_prompt", "[GeneratorAgent no devolvió 'generated_prompt']")
        workflow.status = WorkflowStatus.COMPLETED
        print(f"WF {workflow.workflow_id}: GeneratorAgent OK. Result: '{workflow.generated_result_text[:100]}...'")
        return True
    else:
        workflow.status = WorkflowStatus.FAILED
        detail_msg = f"Fallo en llamada a GeneratorAgent: {response.status_code}"
        try: detail_msg += f" - {response.json().get('detail', response.text[:200])}"
        except: detail_msg += f" - {response.text[:200]}"
        workflow.error_message = detail_msg
        print(workflow.error_message)
        return False

async def step_save_to_library(workflow: WorkflowState) -> bool:
    print(f"WF {workflow.workflow_id}: Step - Save to Lib (Placeholder - no se llamará si es un error)")
    if workflow.status != WorkflowStatus.COMPLETED: return False
    print(f"WF {workflow.workflow_id}: Simulación de guardado en librería."); return True

async def initiate_new_workflow(request_data: WorkflowInitiationRequest, user_id: str) -> WorkflowState:
    # --- INICIO DE MODIFICACIÓN: Usar zep_session_id del request si se proporciona ---
    if request_data.zep_session_id:
        current_zep_session_id = request_data.zep_session_id
        print(f"Orchestrator/Workflow: Continuando Zep Session ID proporcionado: {current_zep_session_id}")
    else:
        current_zep_session_id = f"zep_session_{user_id}_{uuid.uuid4().hex[:12]}"
        print(f"Orchestrator/Workflow: Generando nuevo Zep Session ID: {current_zep_session_id}")
    # --- FIN DE MODIFICACIÓN ---

    wf = WorkflowState(user_id=user_id, status=WorkflowStatus.STARTED, initial_prompt_text=request_data.prompt_text_idea,
                       current_prompt_text=request_data.prompt_text_idea, target_llm_model=str(request_data.target_llm_model),
                       workflow_type=str(request_data.workflow_type),
                       zep_session_id=current_zep_session_id) # Usar el session_id determinado
    
    print(f"Orchestrator/Workflow: Iniciando/Continuando wf {wf.workflow_id} para user {user_id} con datos: {request_data.model_dump(exclude_none=True)} y Zep Session: {wf.zep_session_id}")
    workflows_in_progress[wf.workflow_id] = wf

    wf.status = WorkflowStatus.PENDING_CREDIT_CHECK; workflows_in_progress[wf.workflow_id] = wf
    if not await step_check_credits(wf): workflows_in_progress[wf.workflow_id] = wf; return wf

    print(f"WF {wf.workflow_id}: Créditos OK. Procediendo a generación inicial. Tipo: {wf.workflow_type}")
    wf.status = WorkflowStatus.PENDING_INITIAL_GENERATION; workflows_in_progress[wf.workflow_id] = wf

    if str(wf.workflow_type) == "default":
        if not await step_generate_initial_with_generator(wf):
            workflows_in_progress[wf.workflow_id] = wf; return wf
        if wf.status == WorkflowStatus.COMPLETED:
            await step_save_to_library(wf)
    else:
        wf.status = WorkflowStatus.FAILED; wf.error_message = f"Tipo de workflow '{wf.workflow_type}' no soportado para generación automática en esta fase."; print(wf.error_message)

    wf.updated_at = datetime.now(timezone.utc); workflows_in_progress[wf.workflow_id] = wf
    print(f"Orchestrator/Workflow: Wf {wf.workflow_id} finalizado con estado: {wf.status}, error: {wf.error_message}"); return wf

def get_workflow_state(wf_id: str, uid: str) -> Optional[WorkflowState]:
    wf = workflows_in_progress.get(wf_id); return wf if wf and wf.user_id == uid else None
async def process_workflow_action(wf_id: str, uid: str, act_req: WorkflowActionRequest) -> Optional[WorkflowState]:
    wf = get_workflow_state(wf_id, uid);
    if not wf: return None; print(f"WF {wf_id}: Action {act_req.action}"); wf.status=WorkflowStatus.FAILED; wf.error_message="Action N/I"; return wf
async def on_shutdown_workflow(): print("Orchestrator/Workflow: Shutdown."); print(f"{len(workflows_in_progress)} WFs lost.")

print(f"[WORKFLOW.PY] Módulo workflow.py cargándose.")
# Comentamos temporalmente para evitar el NameError y ver si el módulo carga
# print(f"[WORKFLOW.PY] Loaded. Shared Schemas Imported: {_SHARED_SCHEMAS_IMPORTED_SUCCESSFULLY}")
# print(f"  User Service URL: {USER_SERVICE_URL}")
# print(f"  Agents Service URL: {AGENTS_SERVICE_URL}")
# print(f"  Prompt Library Service URL: {PROMPT_LIBRARY_SERVICE_URL}")