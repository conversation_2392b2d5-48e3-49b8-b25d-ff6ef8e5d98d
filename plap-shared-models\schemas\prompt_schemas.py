# plap-shared-models/schemas/prompt_schemas.py
from pydantic import BaseModel, Field, ConfigDict
from typing import Optional, List, Dict, Any # Dict y Any por si los usas aquí
import uuid
from datetime import datetime
from enum import Enum # <--- Asegúrate de tener esta importación

# --- INICIO DE NUEVAS DEFINICIONES DE ENUM ---
class LLMModelType_v0_9_2(str, Enum):
    GEMINI_PRO = "gemini-pro"
    GEMINI_1_0_FLASH_001 = "gemini-1.0-flash-001" # Manteniendo por si algún fallback lo usa
    GEMINI_1_5_PRO = "gemini-1.5-pro"
    GEMINI_1_5_FLASH = "gemini-1.5-flash" # Nombre más corto y común
    GEMINI_2_5_FLASH_PREVIEW_04_17 = "gemini-2.5-flash-preview-04-17" # El que estamos usando
    # Añade otros modelos que uses o planees usar

class WorkflowType_v0_9_2(str, Enum):
    GUIDED = "guided"
    EDITOR = "editor"
    DEFAULT = "default"
    # Puedes añadir otros tipos de workflow que tengas planeados
# --- FIN DE NUEVAS DEFINICIONES DE ENUM ---


# Si necesitas UserProfile_v0_9_2 aquí (ej. para author_profile), impórtalo:
# from .user_schemas import UserProfile_v0_9_2 # Importación relativa dentro del mismo paquete 'schemas'

class PromptBase_v0_9_2(BaseModel):
    title: str = Field(..., min_length=3, description="Título del prompt")
    prompt_text: str = Field(..., min_length=10, description="El contenido principal del prompt")
    description: Optional[str] = Field(None, description="Descripción breve del prompt")
    tags: Optional[List[str]] = Field(default_factory=list, description="Lista de etiquetas o palabras clave")
    # Podrías añadir más campos base aquí, como 'category', 'language', etc.

class PromptCreate_v0_9_2(PromptBase_v0_9_2):
    # user_id: uuid.UUID # El ID del usuario que crea el prompt, usualmente se añade en el backend
    pass

class PromptUpdate_v0_9_2(BaseModel):
    title: Optional[str] = None
    prompt_text: Optional[str] = None
    description: Optional[str] = None
    tags: Optional[List[str]] = None

# Este es uno de los que plap-agents/main.py intenta importar
class Prompt_v0_9_2(PromptBase_v0_9_2): # Para respuestas de listado, etc.
    id: uuid.UUID
    user_id: uuid.UUID
    version: int = 1
    created_at: datetime
    updated_at: Optional[datetime] = None
    # Campos adicionales que podrían ser útiles
    # usage_count: Optional[int] = 0
    # average_rating: Optional[float] = None
    # is_public: bool = False

    model_config = ConfigDict(from_attributes=True)

# Este es el OTRO que plap-agents/main.py intenta importar
class PromptDetail_v0_9_2(Prompt_v0_9_2): # Para una vista detallada
    # Hereda todos los campos de Prompt_v0_9_2
    # Puedes añadir campos específicos para la vista detallada si es necesario
    # Por ejemplo:
    # example_inputs: Optional[List[Dict[str, Any]]] = None
    # example_outputs: Optional[List[str]] = None
    # revision_history_summary: Optional[List[str]] = None # Un resumen simple
    additional_notes: Optional[str] = Field(None, description="Notas adicionales o contexto para el prompt")

# Este print es solo para confirmar que el módulo se carga sin errores de sintaxis internos
print("[SHARED PROMPT_SCHEMAS.PY] Módulo cargado y clases de prompt definidas.")