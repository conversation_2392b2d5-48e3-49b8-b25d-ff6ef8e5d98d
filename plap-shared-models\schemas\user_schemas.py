# plap-shared-models/schemas/user_schemas.py
import uuid
from pydantic import BaseModel, EmailStr, Field, ConfigDict
from typing import Optional, List, Dict, Any
from datetime import datetime

# --- Schemas Base y de Creación ---
class UserProfileBase_v0_9_2(BaseModel):
    email: Optional[EmailStr] = None
    name: Optional[str] = Field(None, description="Nombre completo del usuario")
    avatar_url: Optional[str] = Field(None, description="URL del avatar del usuario")

class UserCreate_v0_9_2(UserProfileBase_v0_9_2):
    email: EmailStr
    password: str = Field(..., min_length=8, description="Contraseña del nuevo usuario")

class UserProfileUpdate_v0_9_2(UserProfileBase_v0_9_2):
    pass

# --- Schemas de Respuesta del Perfil ---
class UserProfile_v0_9_2(BaseModel):
    id: uuid.UUID
    email: EmailStr
    name: Optional[str] = None
    avatar_url: Optional[str] = None
    credits_total: int
    credits_used: int
    credits_remaining: int
    current_plan_id: Optional[uuid.UUID] = None
    plan_name: Optional[str] = None
    is_active: bool
    is_superuser: bool
    created_at: datetime
    updated_at: Optional[datetime] = None
    model_config = ConfigDict(from_attributes=True)

class UserProfileInDB_v0_9_2(UserProfile_v0_9_2):
    hashed_password: str

# --- Schemas de Autenticación ---
class TokenData_v0_9_2(BaseModel):
    sub: str

class TokenResponse_v0_9_2(BaseModel):
    access_token: str
    token_type: str = "bearer"

class UserLogin_v0_9_2(BaseModel):
    email: EmailStr
    password: str

# --- Schemas de Créditos y Uso ---
class UserCredits_v0_9_2(BaseModel):
    user_id: uuid.UUID
    credits_total: int
    credits_used: int
    credits_remaining: int
    plan_name: Optional[str] = None
    cycle_renewal_date: Optional[datetime] = None
    model_config = ConfigDict(from_attributes=True)

class UsageRecord_v0_9_2(BaseModel):
    id: uuid.UUID
    timestamp: datetime
    type: str
    credits_deducted: int
    description: Optional[str] = None
    prompt_workflow_id: Optional[uuid.UUID] = None
    model_config = ConfigDict(from_attributes=True)

# --- Schemas de Facturación y Pagos ---
class BillingDetails_v0_9_2(BaseModel):
    payment_method_summary: Optional[str] = Field(None, description="Ej: Visa terminada en 4242")
    billing_address_line1: Optional[str] = None
    billing_address_city: Optional[str] = None
    billing_address_postal_code: Optional[str] = None
    billing_address_country: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)

class InvoiceSummary_v0_9_2(BaseModel):
    id: uuid.UUID
    stripe_invoice_id: Optional[str] = None
    issue_date: datetime
    due_date: Optional[datetime] = None
    amount: float
    currency: str
    status: str
    link_to_pdf: Optional[str] = None
    model_config = ConfigDict(from_attributes=True)

# --- Schema de Preferencias de Notificación ---
class NotificationPreferences_v0_9_2(BaseModel):
    receive_promotional_emails: bool = Field(True)
    receive_usage_reports: bool = Field(False)
    receive_new_feature_updates: bool = Field(True)
    model_config = ConfigDict(from_attributes=True)

print("[SHARED USER_SCHEMAS.PY] Módulo cargado y todas las clases definidas.")