# plap-user-service/crud.py
from sqlalchemy.orm import Session
import uuid
from typing import Optional
from datetime import datetime, timezone

import models 
import schemas 

def get_user_by_id(db: Session, user_id: uuid.UUID) -> Optional[models.User]:
    return db.query(models.User).filter(models.User.id == user_id).first()

def update_user_profile(db: Session, user_id: uuid.UUID, user_update_data_dict: dict) -> Optional[models.User]:
    db_user = get_user_by_id(db, user_id)
    if not db_user: return None
    changed = False
    for key, value in user_update_data_dict.items():
        if hasattr(db_user, key): 
            current_value = getattr(db_user, key)
            if current_value != value: setattr(db_user, key, value); changed = True
    if changed and hasattr(db_user, 'updated_at'): db_user.updated_at = datetime.now(timezone.utc)
    try: db.add(db_user); db.commit(); db.refresh(db_user); return db_user
    except Exception as e: db.rollback(); print(f"Error en update_user_profile: {e}"); return None

def deduct_credits_atomic(
    db: Session,
    user_id: uuid.UUID,
    cost_to_deduct: int, 
    description: Optional[str],
    workflow_id_str: Optional[str] = None # Sigue siendo un string opcional
) -> Optional[models.User]: 
    
    user = get_user_by_id(db, user_id)
    if not user:
        print(f"Deduct Credits CRUD: Usuario {user_id} no encontrado.")
        return None

    cost = abs(cost_to_deduct) 
    if cost == 0:
        print(f"Deduct Credits CRUD: Costo cero para usuario {user_id}. No se deducen créditos.")
        return user 

    current_credits_total = getattr(user, 'credits_total', 0)
    current_credits_used = getattr(user, 'credits_used', 0)
    credits_remaining_before_deduction = current_credits_total - current_credits_used

    if credits_remaining_before_deduction < cost:
        print(f"Deduct Credits CRUD: Creditos insuficientes para user {user_id}. Restantes: {credits_remaining_before_deduction}, Intento deducir: {cost}")
        return None 

    parsed_workflow_uuid: Optional[uuid.UUID] = None
    if workflow_id_str:
        try:
            # --- INICIO DE CORRECCIÓN ---
            # Intentar quitar el prefijo "wf-" si existe, antes de convertir a UUID
            if workflow_id_str.startswith("wf-"):
                hex_uuid_str = workflow_id_str[3:] # Quita los primeros 3 caracteres ("wf-")
            else:
                hex_uuid_str = workflow_id_str # Asume que ya es un UUID string si no tiene "wf-"
            
            parsed_workflow_uuid = uuid.UUID(hex_uuid_str)
            print(f"Deduct Credits CRUD: workflow_id_str '{workflow_id_str}' parseado a UUID: {parsed_workflow_uuid}")
            # --- FIN DE CORRECCIÓN ---
        except ValueError:
            print(f"Deduct Credits CRUD: ADVERTENCIA - workflow_id_str '{workflow_id_str}' no es un UUID válido. Se guardará como None.")
            # Decidir si esto debe ser un error o solo una advertencia. Por ahora, advertencia y None.
            # Si es mandatorio, podrías levantar una excepción o retornar None aquí.
            pass # parsed_workflow_uuid se quedará como None

    try:
        user.credits_used = current_credits_used + cost # type: ignore
        if hasattr(user, 'updated_at'):
            user.updated_at = datetime.now(timezone.utc)
        db.add(user)

        transaction = models.CreditTransaction(
            id=uuid.uuid4(), # Nuevo UUID para la transacción misma
            user_id=user_id,
            type="usage_deduction",
            credits_amount=-cost, 
            description=description,
            prompt_workflow_id=parsed_workflow_uuid, # Usar el UUID parseado (o None)
            timestamp=datetime.now(timezone.utc)
        )
        db.add(transaction)
        
        db.commit() 
        db.refresh(user) # Refrescar también 'transaction' si la necesitas devuelta o con datos de DB
        
        print(f"Deduct Credits CRUD: ÉxITO para user {user_id}. Créditos usados: {user.credits_used}, Restantes: {user.credits_total - user.credits_used}") # type: ignore
        return user
        
    except Exception as e:
        db.rollback()
        print(f"Deduct Credits CRUD: Error de DB al commitear para user {user_id}: {e}")
        import traceback
        traceback.print_exc() # Para ver el traceback completo del error de DB
        return None