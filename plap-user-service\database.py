# plap-user-service/database.py
from sqlalchemy import create_engine
from sqlalchemy.orm import sessionmaker
import os
# from dotenv import load_dotenv # Comentado, confiar en variables de entorno de Docker Compose

# --- CAMBIO AQUÍ ---
import models # Importación absoluta, asume que models.py está en /app
# Anteriormente: from . import models 

DATABASE_URL = os.getenv("DATABASE_URL")
if not DATABASE_URL:
    print("FATAL ERROR: DATABASE_URL environment variable not set!")
    # Considera lanzar un error aquí para detener el arranque si es crítico
    # raise ValueError("FATAL ERROR: DATABASE_URL environment variable not set!")
    engine = None
    SessionLocal = None
else:
    print(f"User Service DB: DATABASE_URL environment variable found.") # No imprimas la URL en prod
    engine = create_engine(DATABASE_URL, pool_pre_ping=True)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)

def get_db():
    if not SessionLocal:
        print("User Service DB: SessionLocal not initialized due to missing DATABASE_URL.")
        raise RuntimeError("Database not configured for User Service") 
    db = SessionLocal()
    try:
        yield db
    finally:
        db.close()

# La función create_db_tables fue eliminada de aquí porque asumimos que las tablas
# se crean mediante scripts de inicialización de plap-infra/db/init o por un sistema de migraciones.
# Si necesitas que este servicio cree tablas, la función debería definirse aquí
# y llamarse desde el main.py on_startup, y 'models.Base' debería existir.