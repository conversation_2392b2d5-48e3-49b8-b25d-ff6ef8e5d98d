# plap-user-service/main.py
from fastapi import FastAPI, Depends, HTTPException, status, Header
from sqlalchemy.orm import Session
from sqlalchemy.sql import text
from typing import Annotated, List, Optional, Any, Type
import uuid
from datetime import datetime, timezone

# --- Importaciones de Módulos Locales del Servicio ---
import crud
import models 
import schemas # Se refiere a plap-user-service/schemas.py
from database import SessionLocal, engine 
import os
from fastapi.middleware.cors import CORSMiddleware

# --- Definición de Tipos para Schemas (usando las variables de schemas.py) ---
# Estos serán los placeholders o los reales dependiendo de la importación en schemas.py
UserProfile_ResponseSchema: Type[schemas.UserProfile_v0_9_2] = schemas.UserProfile_v0_9_2
UserProfileUpdate_InputSchema: Type[schemas.UserProfileUpdate_v0_9_2] = schemas.UserProfileUpdate_v0_9_2
UserCredits_ResponseSchema: Type[schemas.UserCredits_v0_9_2] = schemas.UserCredits_v0_9_2
UsageRecord_ResponseSchemaList: Type[List[schemas.UsageRecord_v0_9_2]] = List[schemas.UsageRecord_v0_9_2] # type: ignore
BillingDetails_ResponseSchema: Type[schemas.BillingDetails_v0_9_2] = schemas.BillingDetails_v0_9_2
InvoiceSummary_ResponseSchemaList: Type[List[schemas.InvoiceSummary_v0_9_2]] = List[schemas.InvoiceSummary_v0_9_2] # type: ignore
NotificationPreferences_Schema: Type[schemas.NotificationPreferences_v0_9_2] = schemas.NotificationPreferences_v0_9_2

# --- Configuración de la Aplicación FastAPI ---
app = FastAPI(
    title="PLAP User Service",
    version="0.9.2",
    description="Servicio para gestionar perfiles de usuario, créditos y configuraciones en PLAP.",
)

# --- Evento Startup ---
@app.on_event("startup")
def on_startup():
    print("User Service: Startup event triggered.")
    try:
        if not SessionLocal: 
            print("User Service: CRITICAL - SessionLocal not initialized.")
        else:
            with SessionLocal() as db: db.execute(text("SELECT 1"))
            print("User Service: Database connection successful on startup.")
    except Exception as e: print(f"User Service: CRITICAL - DB connection failed on startup: {e}")
    print("User Service: Application started.")

# --- Configuración de CORS ---
_origins_str = os.getenv("CORS_ORIGINS_USER_SERVICE", "*")
origins = _origins_str.split(',') if _origins_str and _origins_str != "*" else ["*"] 
print(f"User Service: CORS Origins: {origins}")
app.add_middleware(
    CORSMiddleware, allow_origins=origins, allow_credentials=True,
    allow_methods=["*"], allow_headers=["*"])

# --- Dependencia DB ---
def get_db():
    if not SessionLocal: print("User Service DB: SessionLocal not initialized."); raise HTTPException(503, "DB not configured")
    db = SessionLocal();
    try: yield db
    finally: db.close()

# --- Dependencia para user_id del header ---
def get_requesting_user_id_from_internal_header(
    x_user_id: str = Header(..., alias="X-User-ID", description="ID del usuario solicitante (del API Gateway)")
) -> uuid.UUID:
    try: return uuid.UUID(x_user_id)
    except ValueError: raise HTTPException(status.HTTP_400_BAD_REQUEST, "Invalid X-User-ID format")

AnnotatedSession = Annotated[Session, Depends(get_db)]
AnnotatedRequestingUserId = Annotated[uuid.UUID, Depends(get_requesting_user_id_from_internal_header)]

# --- Funciones de Ayuda ---
def get_user_or_404(db: Session, user_id: uuid.UUID) -> models.User:
    user_orm = crud.get_user_by_id(db, user_id=user_id)
    if user_orm is None: raise HTTPException(status.HTTP_404_NOT_FOUND, f"User {user_id} not found")
    return user_orm

# --- Endpoints Internos ---
@app.get("/internal/users/{target_user_id_str}/profile", 
    response_model=UserProfile_ResponseSchema,
    summary="Obtener Perfil de Usuario (Interno)", tags=["User Profile (Internal)"])
async def read_user_profile_internal(
    target_user_id_str: str, db: AnnotatedSession, requesting_user_id: AnnotatedRequestingUserId
):
    try: target_user_uuid = uuid.UUID(target_user_id_str)
    except ValueError: raise HTTPException(status.HTTP_400_BAD_REQUEST, "Invalid target_user_id format")
    if target_user_uuid != requesting_user_id:
        print(f"User Service: AUTHZ FAILED - User {requesting_user_id} intentó leer perfil de {target_user_uuid}")
        raise HTTPException(status.HTTP_403_FORBIDDEN, "Not authorized to view this user's profile")
    user_orm = get_user_or_404(db, target_user_uuid)
    return UserProfile_ResponseSchema.model_validate(user_orm)

@app.patch("/internal/users/{target_user_id_str}/profile", 
    response_model=UserProfile_ResponseSchema,
    summary="Actualizar Perfil de Usuario (Interno)", tags=["User Profile (Internal)"])
async def update_user_profile_internal(
    target_user_id_str: str, user_update: UserProfileUpdate_InputSchema, 
    db: AnnotatedSession, requesting_user_id: AnnotatedRequestingUserId
):
    try: target_user_uuid = uuid.UUID(target_user_id_str)
    except ValueError: raise HTTPException(status.HTTP_400_BAD_REQUEST, "Invalid target_user_id format")
    if target_user_uuid != requesting_user_id:
        print(f"User Service: AUTHZ FAILED - User {requesting_user_id} intentó actualizar perfil de {target_user_uuid}")
        raise HTTPException(status.HTTP_403_FORBIDDEN, "Not authorized to update this user's profile")
    user_update_data_dict = user_update.model_dump(exclude_unset=True)
    if not user_update_data_dict: raise HTTPException(status.HTTP_400_BAD_REQUEST, "No update data provided")
    updated_user_orm = crud.update_user_profile(db, user_id=target_user_uuid, user_update_data_dict=user_update_data_dict)
    if updated_user_orm is None: raise HTTPException(status.HTTP_404_NOT_FOUND, f"User {target_user_uuid} not found or update failed")
    return UserProfile_ResponseSchema.model_validate(updated_user_orm)

@app.get("/internal/users/{target_user_id_str}/credits",
    response_model=UserCredits_ResponseSchema,
    summary="Obtener Créditos de Usuario (Interno)", tags=["User Credits (Internal)"])
async def read_user_credits_internal(
    target_user_id_str: str, db: AnnotatedSession, requesting_user_id: AnnotatedRequestingUserId 
):
    try: target_user_uuid = uuid.UUID(target_user_id_str)
    except ValueError: raise HTTPException(status.HTTP_400_BAD_REQUEST, "Invalid target_user_id format")
    if target_user_uuid != requesting_user_id:
        print(f"User Service: AUTHZ FAILED - User {requesting_user_id} intentó leer créditos de {target_user_uuid}")
        raise HTTPException(status.HTTP_403_FORBIDDEN, "Not authorized to view this user's credits")
    user_orm = get_user_or_404(db, target_user_uuid)
    return UserCredits_ResponseSchema.model_validate(user_orm)

@app.post("/internal/usage/record", 
    response_model=UserCredits_ResponseSchema, status_code=status.HTTP_200_OK,
    summary="Registrar Uso de Créditos (Interno)", tags=["User Credits (Internal)"])
async def record_credit_usage_internal(
    usage_data: schemas.InternalCreditUsage, db: AnnotatedSession, requesting_user_id: AnnotatedRequestingUserId
):
    print(f"User Service: Received internal credit usage request: {usage_data.model_dump()} from requesting user {requesting_user_id}")
    target_user_uuid_from_payload = uuid.UUID(usage_data.user_id)
    if target_user_uuid_from_payload != requesting_user_id:
        print(f"User Service: AUTHZ FAILED - Mismatched X-User-ID and payload user_id for credit deduction.")
        raise HTTPException(status.HTTP_403_FORBIDDEN, "Not authorized to record usage for this user account.")
    updated_user_orm = crud.deduct_credits_atomic(
        db=db, user_id=target_user_uuid_from_payload, cost_to_deduct=usage_data.cost,
        description=usage_data.description, workflow_id_str=usage_data.prompt_workflow_id)
    if updated_user_orm is None: raise HTTPException(status.HTTP_402_PAYMENT_REQUIRED, "Credit deduction failed.")
    # La siguiente línea causaba el ValidationError si UserCredits_ResponseSchema era un placeholder vacío
    return UserCredits_ResponseSchema.model_validate(updated_user_orm)


@app.get("/health", status_code=status.HTTP_200_OK, tags=["Health"])
def health_check():
    db_status = "unknown"; db_message = ""
    try:
        if not SessionLocal: raise RuntimeError("DB SessionLocal not initialized.")
        with SessionLocal() as db: db.execute(text("SELECT 1"))
        db_status = "connected"
    except Exception as e: print(f"User Service Health check DB error: {e}"); db_status = "error"; db_message = str(e)
    return {"status": "OK", "service": "User Service", "version": app.version, "db_status": db_status, "db_message": db_message}

print(f"[USER_SERVICE_MAIN.PY] Módulo main.py cargado y aplicación FastAPI '{app.title}' definida.")