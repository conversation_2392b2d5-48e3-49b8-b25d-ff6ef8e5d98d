# plap-user-service/schemas.py
print("********** [USER_SERVICE_SCHEMAS.PY] ESTE ARCHIVO SE ESTÁ EJECUTANDO **********")

from pydantic import BaseModel, Field, ConfigDict, computed_field
from typing import Optional, List, Any, Type, cast
import uuid
from datetime import datetime
import sys

# --- Variables para los schemas importados o placeholders ---
UserProfile_v0_9_2: Optional[Type[BaseModel]] = None; UserProfileUpdate_v0_9_2: Optional[Type[BaseModel]] = None
UserCredits_v0_9_2: Optional[Type[BaseModel]] = None; UsageRecord_v0_9_2: Optional[Type[BaseModel]] = None
BillingDetails_v0_9_2: Optional[Type[BaseModel]] = None; InvoiceSummary_v0_9_2: Optional[Type[BaseModel]] = None
NotificationPreferences_v0_9_2: Optional[Type[BaseModel]] = None
_SHARED_MODELS_IMPORTED_SUCCESSFULLY = False

try:
    print("[USER_SERVICE_SCHEMAS.PY] Intento 1: importar desde 'schemas.user_schemas'...")
    from schemas.user_schemas import ( 
        UserProfile_v0_9_2 as ImportedUserProfile1, UserProfileUpdate_v0_9_2 as ImportedUserProfileUpdate1,
        UserCredits_v0_9_2 as ImportedUserCredits1, UsageRecord_v0_9_2 as ImportedUsageRecord1,
        BillingDetails_v0_9_2 as ImportedBillingDetails1, InvoiceSummary_v0_9_2 as ImportedInvoiceSummary1,
        NotificationPreferences_v0_9_2 as ImportedNotificationPreferences1)
    UserProfile_v0_9_2 = ImportedUserProfile1; UserProfileUpdate_v0_9_2 = ImportedUserProfileUpdate1
    UserCredits_v0_9_2 = ImportedUserCredits1; UsageRecord_v0_9_2 = ImportedUsageRecord1
    BillingDetails_v0_9_2 = ImportedBillingDetails1; InvoiceSummary_v0_9_2 = ImportedInvoiceSummary1
    NotificationPreferences_v0_9_2 = ImportedNotificationPreferences1
    print(f"[USER_SERVICE_SCHEMAS.PY] ¡ÉXITO (Intento 1)! Schemas compartidos importados.")
    _SHARED_MODELS_IMPORTED_SUCCESSFULLY = True
except ImportError as e1:
    print(f"[USER_SERVICE_SCHEMAS.PY] Falló Intento 1 ('schemas.user_schemas'): {e1}.")
    try:
        print("[USER_SERVICE_SCHEMAS.PY] Intento 2: importar desde 'plap_shared_models.schemas.user_schemas'...")
        from plap_shared_models.schemas.user_schemas import (
            UserProfile_v0_9_2 as ImportedUserProfile2, UserProfileUpdate_v0_9_2 as ImportedUserProfileUpdate2,
            UserCredits_v0_9_2 as ImportedUserCredits2, UsageRecord_v0_9_2 as ImportedUsageRecord2,
            BillingDetails_v0_9_2 as ImportedBillingDetails2, InvoiceSummary_v0_9_2 as ImportedInvoiceSummary2,
            NotificationPreferences_v0_9_2 as ImportedNotificationPreferences2)
        UserProfile_v0_9_2 = ImportedUserProfile2; UserProfileUpdate_v0_9_2 = ImportedUserProfileUpdate2
        UserCredits_v0_9_2 = ImportedUserCredits2; UsageRecord_v0_9_2 = ImportedUsageRecord2
        BillingDetails_v0_9_2 = ImportedBillingDetails2; InvoiceSummary_v0_9_2 = ImportedInvoiceSummary2
        NotificationPreferences_v0_9_2 = ImportedNotificationPreferences2
        print(f"[USER_SERVICE_SCHEMAS.PY] ¡ÉXITO (Intento 2)! Schemas compartidos importados.")
        _SHARED_MODELS_IMPORTED_SUCCESSFULLY = True
    except ImportError as e2:
        print(f"[USER_SERVICE_SCHEMAS.PY] CRITICAL WARNING: Falló también Intento 2: {e2}. Usando placeholders.")

if UserProfile_v0_9_2 is None: UserProfile_v0_9_2 = cast(Type[BaseModel], None) # Placeholder
if UserProfileUpdate_v0_9_2 is None: UserProfileUpdate_v0_9_2 = cast(Type[BaseModel], None)
if UserCredits_v0_9_2 is None: UserCredits_v0_9_2 = cast(Type[BaseModel], None)
if UsageRecord_v0_9_2 is None: UsageRecord_v0_9_2 = cast(Type[BaseModel], None)
if BillingDetails_v0_9_2 is None: BillingDetails_v0_9_2 = cast(Type[BaseModel], None)
if InvoiceSummary_v0_9_2 is None: InvoiceSummary_v0_9_2 = cast(Type[BaseModel], None)
if NotificationPreferences_v0_9_2 is None: NotificationPreferences_v0_9_2 = cast(Type[BaseModel], None)

UsageRecord_ResponseSchemaList: Type[List[Any]] 
InvoiceSummary_ResponseSchemaList: Type[List[Any]]

if not _SHARED_MODELS_IMPORTED_SUCCESSFULLY:
    print("[USER_SERVICE_SCHEMAS.PY] Definiendo placeholders locales porque la importación de compartidos falló.")
    class _BasePH(BaseModel): model_config = ConfigDict(from_attributes=True, protected_namespaces=())
    
    class UserProfile_v0_9_2_PH(_BasePH): 
        id: uuid.UUID; email: str; full_name: Optional[str]=None; is_active: bool=True; 
        credits_total: int=0; created_at:Optional[datetime]=None; updated_at:Optional[datetime]=None
    
    class UserProfileUpdate_v0_9_2_PH(_BasePH): pass
    
    class UserCredits_v0_9_2_PH(_BasePH): 
        id: uuid.UUID 
        credits_total: int=0
        credits_used: int=0
        plan_name: Optional[str] = "Free (Placeholder)" 
        cycle_renewal_date: Optional[datetime] = None

        @computed_field 
        @property
        def credits_remaining(self) -> int: # INDENTACIÓN CORREGIDA
            return self.credits_total - self.credits_used
            
    class UsageRecord_v0_9_2_PH(_BasePH): pass
    class BillingDetails_v0_9_2_PH(_BasePH): pass
    class InvoiceSummary_v0_9_2_PH(_BasePH): pass
    class NotificationPreferences_v0_9_2_PH(_BasePH): pass

    if UserProfile_v0_9_2 is None: UserProfile_v0_9_2 = UserProfile_v0_9_2_PH
    if UserProfileUpdate_v0_9_2 is None: UserProfileUpdate_v0_9_2 = UserProfileUpdate_v0_9_2_PH
    if UserCredits_v0_9_2 is None: UserCredits_v0_9_2 = UserCredits_v0_9_2_PH
    if UsageRecord_v0_9_2 is None: UsageRecord_v0_9_2 = UsageRecord_v0_9_2_PH
    if BillingDetails_v0_9_2 is None: BillingDetails_v0_9_2 = BillingDetails_v0_9_2_PH
    if InvoiceSummary_v0_9_2 is None: InvoiceSummary_v0_9_2 = InvoiceSummary_v0_9_2_PH
    if NotificationPreferences_v0_9_2 is None: NotificationPreferences_v0_9_2 = NotificationPreferences_v0_9_2_PH
    
UsageRecord_ResponseSchemaList = List[UsageRecord_v0_9_2 if UsageRecord_v0_9_2 else Any] # type: ignore
InvoiceSummary_ResponseSchemaList = List[InvoiceSummary_v0_9_2 if InvoiceSummary_v0_9_2 else Any] # type: ignore

class PasswordUpdate(BaseModel):
    current_password: str; new_password: str = Field(..., min_length=8); model_config = ConfigDict(protected_namespaces=())
class InternalCreditUsage(BaseModel):
    user_id: str; cost: int = Field(..., gt=0); prompt_workflow_id: Optional[str] = None
    description: str = "Prompt Execution"; model_config = ConfigDict(protected_namespaces=())

print(f"[USER_SERVICE_SCHEMAS.PY] Módulo schemas.py cargado. Shared models importados exitosamente: {_SHARED_MODELS_IMPORTED_SUCCESSFULLY}")
if UserCredits_v0_9_2: # type: ignore
    qualname = getattr(UserCredits_v0_9_2, '__qualname__', ''); is_placeholder = 'Placeholder' in qualname or '_PH' in qualname
    print(f"[USER_SERVICE_SCHEMAS.PY] UserCredits_v0_9_2 es: {UserCredits_v0_9_2} ({'PLACEHOLDER' if is_placeholder else 'Schema Real'})")
else: print(f"[USER_SERVICE_SCHEMAS.PY] UserCredits_v0_9_2 NO ESTÁ DEFINIDO.")