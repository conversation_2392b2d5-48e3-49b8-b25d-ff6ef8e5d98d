# Script para hacer push inicial de todos los sub-repositorios de PLAP

# Lista de todas las carpetas que son repositorios Git
$repoFolders = @(
    "plap-frontend-public",
    "plap-frontend-private",
    "plap-backend-api",
    "plap-auth-service",
    "plap-user-service",
    "plap-prompt-library-service",
    "plap-orchestrator",
    "plap-agents",
    "plap-shared-models",
    "plap-infra"
)

Write-Host "Starting initial push for all PLAP repositories..." -ForegroundColor Yellow

# Recorre cada carpeta en la lista
foreach ($folder in $repoFolders) {
    Write-Host "Processing repository: $folder" -ForegroundColor Cyan
    # Verifica si la carpeta existe y si es un repositorio Git válido
    if (Test-Path -Path $folder -PathType Container) {
        if (Test-Path -Path (Join-Path $folder ".git") -PathType Container) {
            Write-Host "  Pushing $folder to origin/main..."
            # Ejecuta el comando git push para esa carpeta
            # Captura la salida por si hay errores
            $output = git -C $folder push -u origin main 2>&1
            if ($LASTEXITCODE -eq 0) {
                Write-Host "  Push successful for $folder." -ForegroundColor Green
            } else {
                Write-Host "  ERROR pushing $folder!" -ForegroundColor Red
                Write-Host "  Git output:"
                Write-Host $output
            }
        } else {
            Write-Host "  Skipping $folder - Not a git repository (missing .git folder)." -ForegroundColor Magenta
        }
    } else {
        Write-Host "  Skipping $folder - Folder not found." -ForegroundColor Magenta
    }
    Write-Host "-----------------------------------------"
}

Write-Host "Initial push process finished." -ForegroundColor Yellow